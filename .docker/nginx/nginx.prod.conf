worker_processes auto;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  sendfile on;
  client_max_body_size 10m;
  gzip on;

  map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
  }

  # Real client IPs (trust Traefik)
  real_ip_header X-Forwarded-For;
  real_ip_recursive on;

  # --- Redirect non-www to www ---
  server {
    listen 80;
    server_name dej-prijimacky.cz;
    return 308 https://www.dej-prijimacky.cz$request_uri;
  }

  server {
    listen 80;
    server_name www.dej-prijimacky.cz;

    # --- Permanent Redirects ---
    location = /home {
      return 301 /;
    }

    location = /exams {
      return 301 /prijimacky-nanecisto;
    }

    location = /stan-se-lektorem {
      return 301 /pridej-se-k-nam;
    }

    location = /privacy-policy {
      return 301 /zasady-ochrany-osobnich-udaju;
    }

    location = /about-us {
      return 301 /o-nas;
    }

    location = /become-a-teacher {
      return 301 /pridej-se-k-nam;
    }

    location = /business-conditions {
      return 301 /obchodni-podminky;
    }

    location = /online-courses {
      return 301 /online-doucovani;
    }

    # --- Backend API ---
    location /api/ {
      proxy_http_version 1.1;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
      proxy_set_header X-Forwarded-Host $host;
      proxy_pass http://backend:3000/;
      proxy_read_timeout 60s;
    }

    # --- Frontend ---
    location / {
      proxy_http_version 1.1;
      proxy_set_header Host $host;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
      proxy_set_header X-Forwarded-Host $host;
      proxy_pass http://frontend:3000/;
      proxy_read_timeout 60s;
    }
  }
}