name: "dej-prijimacky-dev"

services:
  postgres:
    image: postgres:17-alpine
    env_file: .env
    volumes:
      - pgdata_dev:/var/lib/postgresql/data
    networks:
      - intranet
    ports:
      - "5432:5432"

  backend:
    build:
      context: .
      dockerfile: .docker/Backend/Dockerfile.dev
    env_file: .env
    environment:
      NODE_ENV: development
      PORT: 3000
    volumes:
      - ./Backend:/app
      - ./.env:/app/.env:ro
      - ./gcp-service-account.json:/run/keys/gcp-service-account.json:ro
      - /app/node_modules
    depends_on:
      - postgres
    networks:
      - intranet
    ports:
      - "5555:5555"

  frontend:
    build:
      context: .
      dockerfile: .docker/Frontend/Dockerfile.dev
    env_file: .env
    environment:
      NODE_ENV: development
      VITE_HOST: 0.0.0.0
      VITE_API_BASE: /api
    volumes:
      - ./Frontend:/app
      - /app/node_modules
      - frontend_sveltekit_dev:/app/.svelte-kit
    depends_on:
      - backend
    networks:
      - intranet

  nginx:
    image: nginx:1.27-alpine
    depends_on:
      - frontend
      - backend
    volumes:
      - ./.docker/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
    networks:
      - intranet
    ports:
      - "80:80"

networks:
  intranet:
    driver: bridge

volumes:
  pgdata_dev:
  frontend_sveltekit_dev: