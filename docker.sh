#!/usr/bin/env bash
set -euo pipefail

export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

CMD="${1:-help}"
ARG="${2:-}"

case "$CMD" in
  # --- Dev ---
  run:dev)
    docker compose -f docker-compose.dev.yml up --build
    ;;
  run:dev:d)
    docker compose -f docker-compose.dev.yml up -d --build
    ;;
  start:dev)
    docker compose -f docker-compose.dev.yml up
    ;;
  start:dev:d)
    docker compose -f docker-compose.dev.yml up -d
    ;;
  down:dev)
    docker compose -f docker-compose.dev.yml down -v
    ;;

  # --- Prod ---
  run:prod)
    docker compose -f docker-compose.yml up --build
    ;;
  run:prod:d)
    docker compose -f docker-compose.yml up -d --build
    ;;
  start:prod)
    docker compose -f docker-compose.yml up
    ;;
  start:prod:d)
    docker compose -f docker-compose.yml up -d
    ;;
  down:prod)
    docker compose -f docker-compose.yml down
    ;;
  rebuild:prod)
    docker compose -f docker-compose.yml build --no-cache
    ;;

  # --- Utils ---
  exec:backend)
    if [ -z "$ARG" ]; then
      echo "Usage: ./docker.sh exec:backend \"<command>\""
      exit 1
    fi
    docker compose -f docker-compose.dev.yml exec backend bash -lc "$ARG"
    ;;
  exec:frontend)
    docker compose -f docker-compose.dev.yml exec frontend bash -lc "${ARG:-bash}"
    ;;
  exec:db)
    docker compose -f docker-compose.dev.yml exec postgres bash -lc "${ARG:-bash}"
    ;;
  exec:nginx)
    docker compose -f docker-compose.dev.yml exec nginx bash -lc "${ARG:-bash}"
    ;;
  migrate:backend)
    docker compose -f docker-compose.dev.yml exec backend bash -lc "npm run prisma:migrate"
    ;;
  *)
    echo "-------------------------------------------------------------------------------"
    echo "Usage:"
    echo ""
    echo "Dev:"
    echo "  ./docker.sh run:dev          # dev up + build (foreground)"
    echo "  ./docker.sh run:dev:d        # dev up + build (detached)"
    echo "  ./docker.sh start:dev        # dev up (no build, foreground)"
    echo "  ./docker.sh start:dev:d      # dev up (no build, detached)"
    echo "  ./docker.sh down:dev         # stop dev + volumes"
    echo ""
    echo "Prod:"
    echo "  ./docker.sh run:prod         # prod up + build (foreground)"
    echo "  ./docker.sh run:prod:d       # prod up + build (detached)"
    echo "  ./docker.sh start:prod       # prod up (no build, foreground)"
    echo "  ./docker.sh start:prod:d     # prod up (no build, detached)"
    echo "  ./docker.sh down:prod        # stop prod"
    echo "  ./docker.sh rebuild:prod     # prod rebuild no cache"
    echo ""
    echo "Utils:"
    echo "  ./docker.sh exec:frontend                 # shell into frontend"
    echo "  ./docker.sh exec:backend                  # shell into backend"
    echo "  ./docker.sh exec:db                       # shell into postgres"
    echo "  ./docker.sh exec:nginx                    # shell into nginx"
    echo "  ./docker.sh migrate:backend               # run prisma:migrate in backend"
    echo "-------------------------------------------------------------------------------"
    ;;
esac
