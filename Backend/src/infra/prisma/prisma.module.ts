import { Global, Module } from '@nestjs/common';

import { PrismaService } from './prisma.service';
import { TransactionService } from './transaction.service';

export const PRISMA = Symbol('PRISMA');

@Global()
@Module({
	providers: [PrismaService, { provide: PRISMA, useExisting: PrismaService }, TransactionService],
	exports: [PrismaService, PRISMA, TransactionService]
})
export class PrismaModule {}
