import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { EPrismaClientErrorCodes } from '@/types';

export const DEFAULT_TRANSACTION_RETRY_COUNT = 5;

type PerformTransactionFunc<T> = (transactionClient: Prisma.TransactionClient) => Promise<T>;

export interface TransactionOptions {
	timeout?: number;
	maxRetries?: number;
	isolationLevel?: Prisma.TransactionIsolationLevel;
}

@Injectable()
export class TransactionService {
	constructor(private readonly prisma: PrismaService) {}

	/**
	 * Executes a function within a database transaction with automatic retry logic
	 * @param func - function to perform in transaction
	 * @param options - transaction configuration options
	 * @param transactionClient - optional transaction client to use (for nested operations)
	 */
	async run<T>(
		func: PerformTransactionFunc<T>,
		options: TransactionOptions = {},
		transactionClient?: Prisma.TransactionClient
	): Promise<T> {
		const {
			timeout,
			maxRetries = DEFAULT_TRANSACTION_RETRY_COUNT,
			isolationLevel = Prisma.TransactionIsolationLevel.Serializable
		} = options;

		let retries = 0;

		while (retries < maxRetries) {
			try {
				// If transactionClient is provided, use it to perform the operation
				// This is useful for operations within an existing transaction
				if (transactionClient) {
					return await func(transactionClient);
				}

				// Execute the transaction
				const transactionResults = await this.prisma.$transaction(
					async (transaction) => {
						return await func(transaction);
					},
					{
						isolationLevel,
						timeout
					}
				);

				return transactionResults;
			} catch (error) {
				if (error instanceof Prisma.PrismaClientKnownRequestError) {
					if (
						error.code === (EPrismaClientErrorCodes.TransactionWriteConflict as string)
					) {
						retries++;
						// Add exponential backoff for retries
						await this.sleep(Math.pow(2, retries) * 100);
						continue;
					}
				}
				throw error;
			}
		}

		throw new Error(`Transaction failed after ${maxRetries} retries`);
	}

	/**
	 * Helper method for exponential backoff delay
	 */
	private sleep(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}
}
