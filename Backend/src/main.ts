import { AppExceptionFilter } from '@common/filters/appException.filter';
import type { AppAbility } from '@modules/auth/ability/types';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import cookieParser from 'cookie-parser';
import type { Express } from 'express';
import helmet from 'helmet';

import { ATClaims } from '@/types/user';

import { AppModule } from './app.module';

declare module 'express-serve-static-core' {
	interface Request {
		//@ts-expect-error express cookies are any by default
		cookies: unknown;
		user?: ATClaims;
		ability?: AppAbility;
	}
}

async function bootstrap() {
	const app = await NestFactory.create(AppModule);

	app.use(cookieParser());
	app.use(helmet());
	app.useGlobalPipes(
		new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true, transform: true })
	);

	const instance = app.getHttpAdapter().getInstance() as Express;
	instance.set('trust proxy', 1);

	app.useGlobalFilters(new AppExceptionFilter());

	app.enableCors({
		//TODO: update credentials
		origin: ['*'],
		credentials: true,
		allowedHeaders: ['Content-Type', 'x-csrf-token'],
		methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']
	});

	await app.listen(process.env.PORT ?? 3000);
}

bootstrap().catch((e) => console.error(e));
