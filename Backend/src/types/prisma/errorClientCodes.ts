export enum EPrismaClientErrorCodes {
	ValueTooLongForColumn = 'P2000',
	RecordNotFound = 'P2001',
	UniqueConstraintFailed = 'P2002',
	ForeignKeyConstraintFailed = 'P2003',
	ConstraintFailedOnDatabase = 'P2004',
	InvalidValueStored = 'P2005',
	InvalidFieldValue = 'P2006',
	DataValidationError = 'P2007',
	QueryParsingError = 'P2008',
	QueryValidationError = 'P2009',
	RawQueryFailed = 'P2010',
	NullConstraintViolation = 'P2011',
	MissingRequiredValue = 'P2012',
	MissingRequiredArgument = 'P2013',
	RequiredRelationViolation = 'P2014',
	RelatedRecordNotFound = 'P2015',
	QueryInterpretationError = 'P2016',
	RelationRecordsNotConnected = 'P2017',
	RequiredConnectedRecordsNotFound = 'P2018',
	InputError = 'P2019',
	ValueOutOfRange = 'P2020',
	TableDoesNotExist = 'P2021',
	ColumnDoesNotExist = 'P2022',
	InconsistentColumnData = 'P2023',
	ConnectionPoolTimeout = 'P2024',
	OperationDependsOnMissingRecords = 'P2025',
	UnsupportedFeature = 'P2026',
	MultipleDatabaseErrors = 'P2027',
	TransactionApiError = 'P2028',
	QueryParameterLimitExceeded = 'P2029',
	FulltextIndexNotFound = 'P2030',
	MongoDBReplicaSetRequired = 'P2031',
	NumberExceeds64Bit = 'P2033',
	TransactionWriteConflict = 'P2034'
}
