import { z } from 'zod';

const PrivateEd25519 = z.object({
	kty: z.literal('OKP'),
	crv: z.literal('Ed25519'),
	x: z.string().min(1),
	d: z.string().min(1),
	alg: z.literal('EdDSA').optional(),
	use: z.literal('sig').optional(),
	kid: z.string().min(1)
});
const PrivateSet = z.record(z.string(), PrivateEd25519);

export const EnvSchema = z.object({
	ISS: z.url(),
	API_AUD: z.string().min(1),
	AUTH_AUD: z.string().min(1),
	JWKS_KID_CURRENT: z.string().min(1),
	JWKS_PRIVATE_SET_B64URL: z.string().min(1),

	FRONTEND_URL: z.string(),

	MAILERSEND_API_KEY: z.string(),
	MAIL_FROM_EMAIL: z.string(),
	MAIL_FROM_NAME: z.string(),

	S2S_SECRET: z.string(),

	GCP_PROJECT_ID: z.string().min(1),
	GCP_BUCKET_NAME: z.string().min(1),
	GCP_KEYFILE: z.string().min(1)
});

export type Env = z.infer<typeof EnvSchema>;

export function loadAndValidateEnv(): Env & { privateSet: z.infer<typeof PrivateSet> } {
	const parsed = EnvSchema.parse(process.env);
	const decoded = Buffer.from(parsed.JWKS_PRIVATE_SET_B64URL, 'base64url').toString('utf8');
	const json: unknown = JSON.parse(decoded);
	const privateSet = PrivateSet.parse(json);
	return { ...parsed, privateSet };
}
