import type { SuccessResponseDto } from '@common/dto/api.dto';
import type { I18nMsg } from '@common/errors';
import { HttpStatus } from '@nestjs/common';

type MsgInput = I18nMsg | I18nMsg[] | undefined;

export const ok = <TData>(
	data: TData,
	messages?: MsgInput,
	status: number = HttpStatus.OK
): SuccessResponseDto<TData> => ({
	ok: true,
	status,
	data,
	messages: messages ? (Array.isArray(messages) ? messages : [messages]) : undefined
});

export const created = <TData>(data: TData, messages?: MsgInput): SuccessResponseDto<TData> =>
	ok(data, messages, HttpStatus.CREATED);
