import moment from 'moment';
import z from 'zod';

export const zodMomentRequired = () =>
	z.string().transform((val) => {
		const date = moment(val, moment.ISO_8601);
		if (!date.isValid()) throw new Error(`Invalid date: ${val}`);

		return date;
	});

export const zodMomentOptional = () =>
	z
		.string()
		.optional()
		.transform((val) => {
			if (!val) return undefined;

			const date = moment(val, moment.ISO_8601);
			if (!date.isValid()) throw new Error(`Invalid date: ${val}`);

			return date;
		});
