// Build a record keyed by the chosen property.
// - Keeps literal keys (e.g. "foo" | "bar").
// - Narrows values per key via Extract<...>.
// - Works with readonly arrays and `as const`.
export function indexBy<
	const T extends readonly Record<PropertyKey, any>[],
	const K extends keyof T[number]
>(arr: T, key: K) {
	type Key = Extract<T[number][K], PropertyKey>;
	type Out = { [P in Key]: Extract<T[number], Record<K, P>> };

	const out = {} as Out;
	for (const item of arr) {
		const k = item[key] as Key;
		// TS can’t refine the dynamic assignment perfectly; types above guarantee safety.
		// If you prefer zero-errors builds, keep this one line:
		// @ts-expect-error - assignment is safe by construction
		out[k] = item;
	}
	return out;
}
