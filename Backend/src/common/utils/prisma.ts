import { Prisma } from '@prisma/client';

export const getStringFilter = <
	T extends
		| {
				field: string;
				action: string;
				value: string;
		  }
		| undefined
>(
	val: T
): Prisma.StringFilter | undefined => {
	if (!val) {
		return undefined;
	}
	return {
		[val.action]: val.value,
		mode: val.action === 'contains' ? 'insensitive' : undefined
	};
};
export const getDateFilter = <
	T extends
		| {
				field: string;
				action: string;
				value: string;
		  }
		| undefined
>(
	val: T
): Prisma.DateTimeFilter | undefined => {
	if (!val) {
		return undefined;
	}
	return {
		[val.action]: val.value
	};
};

export const getNumberFilter = <
	T extends
		| {
				field: string;
				action: string;
				value: string;
		  }
		| undefined
>(
	val: T
): Prisma.IntFilter | undefined => {
	if (!val) {
		return undefined;
	}
	return {
		[val.action]: Number(val.value)
	};
};

export const getEnumFilter = <
	U extends string,
	T extends
		| {
				field: string;
				action: string;
				value: U;
		  }
		| undefined
>(
	val: T
): Prisma.StringFilter | undefined => {
	if (!val) {
		return undefined;
	}
	return {
		[val.action]: val.value,
		mode: val.action === 'contains' ? 'insensitive' : undefined
	};
};

export const getSort = <
	TSort extends { field: string; order: Prisma.SortOrder } | undefined,
	TField extends TSort extends { field: infer F } ? F : never
>(
	val: TSort,
	field: TField
): Prisma.SortOrder | undefined => {
	if (!val || val.field !== field) {
		return undefined;
	}
	return val.order;
};

export const buildRelationUpdate = <T extends string>(
	ids: T[] | undefined
): { set: { id: T }[] } | undefined => {
	if (ids === undefined) {
		return undefined;
	}
	return {
		set: ids.map((id) => ({ id }))
	};
};

export const buildRelationCreate = <T extends string>(
	ids: T[] | undefined
): { connect: { id: T }[] } | undefined => {
	if (ids === undefined || ids.length === 0) {
		return undefined;
	}
	return {
		connect: ids.map((id) => ({ id }))
	};
};
