import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class S2SAuthGuard implements CanActivate {
	constructor(private readonly configService: ConfigService) {}

	canActivate(context: ExecutionContext): boolean {
		const request: Request = context.switchToHttp().getRequest();
		const authHeader = request.headers['authorization'];

		if (!authHeader) {
			throw new UnauthorizedException('Invalid token');
		}

		const [type, token] = authHeader.split(' ');

		if (type !== 'Bearer' || !token) {
			throw new UnauthorizedException('Invalid token');
		}

		const expectedToken = this.configService.get<string>('S2S_SECRET');
		if (token !== expectedToken) {
			throw new UnauthorizedException('Invalid token');
		}

		return true;
	}
}
