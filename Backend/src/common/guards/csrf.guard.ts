import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import type { Request } from 'express';

@Injectable()
export class CsrfGuard implements CanActivate {
	private readonly allowedOrigins = ['http://localhost'];

	canActivate(ctx: ExecutionContext): boolean {
		const req = ctx.switchToHttp().getRequest<Request>();

		const origin = req.headers['origin'];
		const referer = req.headers['referer'];

		let source: URL | undefined;
		try {
			if (origin) {
				source = new URL(origin);
			} else if (referer) {
				source = new URL(referer);
			}
		} catch {
			throw new ForbiddenException('Invalid origin');
		}

		if (!source) {
			if (req.method === 'GET') return true;
			throw new ForbiddenException('Missing origin');
		}

		const isAllowed = this.allowedOrigins.some((o) => {
			try {
				const u = new URL(o);
				return u.host === source.host && u.protocol === source.protocol;
			} catch {
				return false;
			}
		});

		if (!isAllowed) {
			throw new ForbiddenException('Origin mismatch');
		}

		return true;
	}
}
