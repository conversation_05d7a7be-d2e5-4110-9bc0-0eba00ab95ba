import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { z } from 'zod';

@Injectable()
export class ZodPipe<S extends z.ZodTypeAny> implements PipeTransform<z.input<S>, z.output<S>> {
	constructor(private readonly schema: S) {}

	transform(value: unknown): z.output<S> {
		try {
			const r = this.schema.safeParse(value);
			if (!r.success) {
				const e = new BadRequestException('Validation failed') as BadRequestException & {
					cause: z.ZodError;
				};
				e.cause = r.error; // ZodError
				throw e;
			}
			return r.data;
		} catch (error) {
			// Handle errors thrown during transform functions or other processing
			if (error instanceof BadRequestException) {
				throw error; // Re-throw validation errors as-is
			}

			// Handle ZodError or other errors thrown during transform
			if (error instanceof z.ZodError) {
				const e = new BadRequestException('Validation failed') as BadRequestException & {
					cause: z.ZodError;
				};
				e.cause = error;
				throw e;
			}

			// Handle any other errors (like from transform functions)
			const e = new BadRequestException('Invalid input format') as BadRequestException & {
				cause: unknown;
			};
			e.cause = error;
			throw e;
		}
	}
}
