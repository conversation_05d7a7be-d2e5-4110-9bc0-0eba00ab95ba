import type { ErrorResponseDto, FieldErrorGroup } from '@common/dto/api.dto';
import { AppHttpError, I18nMsg } from '@common/errors';
import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import type { Response } from 'express';
import { ZodError } from 'zod';

type Issue = ZodError['issues'][number];
type ExceptionResponse = string | { message?: string | string[]; [key: string]: unknown };

@Catch()
export class AppExceptionFilter implements ExceptionFilter {
	catch(exception: unknown, host: ArgumentsHost) {
		console.error(exception);

		const res = host.switchToHttp().getResponse<Response>();

		const isHttp = exception instanceof HttpException;
		const cause =
			isHttp && (exception as HttpException & { cause?: unknown }).cause !== undefined
				? (exception as HttpException & { cause?: unknown }).cause
				: undefined;

		const zodErr: ZodError | undefined = cause instanceof ZodError ? cause : undefined;
		const message =
			typeof (exception as { message?: unknown }).message === 'string'
				? (exception as { message: string }).message
				: 'unknown';

		if (zodErr) {
			const fieldsMap = new Map<string, I18nMsg[]>();
			const globalMsgs: I18nMsg[] = [];

			for (const issue of zodErr.issues) {
				const path = this.formatPath(issue);
				const key = issue.message;
				const params = this.extractParams(issue);
				const msg: I18nMsg = { messageKey: key, params };

				if (!path) globalMsgs.push(msg);
				else {
					const list = fieldsMap.get(path) ?? [];
					list.push(msg);
					fieldsMap.set(path, list);
				}
			}

			const fields: FieldErrorGroup[] = Array.from(fieldsMap.entries()).map(
				([field, issues]) => ({
					field,
					issues: this.dedup(issues)
				})
			);

			const payload: ErrorResponseDto = {
				ok: false,
				status: HttpStatus.BAD_REQUEST,
				message,
				errors: {
					fields
				}
			};

			return res.status(HttpStatus.BAD_REQUEST).json(payload);
		}

		if (exception instanceof AppHttpError) {
			const status = exception.getStatus();
			return res.status(status).json({
				ok: false,
				status,
				errors: { global: [exception.i18n] }
			});
		}

		if (isHttp) {
			const status = exception.getStatus();
			const resp = exception.getResponse();
			const global = this.httpToGlobalMessage(status, resp);
			const payload: ErrorResponseDto = {
				ok: false,
				status,
				message,
				errors: { global: [global] }
			};
			return res.status(status).json(payload);
		}

		const status = HttpStatus.INTERNAL_SERVER_ERROR;
		const payload: ErrorResponseDto = {
			ok: false,
			status,
			message,
			errors: { global: [{ messageKey: 'global.errors.http.internal' }] }
		};
		return res.status(status).json(payload);
	}

	private formatPath(issue: Issue): string | null {
		if (!issue.path || issue.path.length === 0) return null;
		const parts = issue.path.map((p) => (typeof p === 'number' ? `[${p}]` : (p as string)));
		return parts.reduce(
			(acc, cur) => (cur.startsWith('[') ? acc + cur : acc ? `${acc}.${cur}` : cur),
			''
		);
	}

	private extractParams(issue: Issue): Record<string, unknown> | undefined {
		const anyIssue = issue as unknown as Record<string, unknown>;
		if (typeof anyIssue.minimum === 'number') return { min: anyIssue.minimum };
		if (typeof anyIssue.maximum === 'number') return { max: anyIssue.maximum };
		if (anyIssue.expected !== undefined && anyIssue.received !== undefined) {
			return { expected: anyIssue.expected, received: anyIssue.received };
		}
		if (typeof anyIssue.validation === 'string') return { validation: anyIssue.validation };
		if (Array.isArray(anyIssue.keys)) return { keys: anyIssue.keys };
		return undefined;
	}

	private dedup(arr: I18nMsg[]): I18nMsg[] {
		const seen = new Set<string>();
		return arr.filter((i) => {
			const sig = JSON.stringify([i.messageKey, i.params ?? null]);
			if (seen.has(sig)) return false;
			seen.add(sig);
			return true;
		});
	}

	private httpToGlobalMessage(status: number, resp: unknown): I18nMsg {
		const response = resp as ExceptionResponse;

		let fromResp: string | undefined;

		if (typeof response === 'string') {
			fromResp = response;
		} else if (Array.isArray(response?.message)) {
			fromResp = response.message[0];
		} else if (typeof response?.message === 'string') {
			fromResp = response.message;
		}

		if (fromResp && /^global\.[a-zA-Z0-9._]+$/.test(fromResp)) {
			return { messageKey: fromResp };
		}
		switch (status) {
			case 400:
				return { messageKey: 'global.errors.http.bad_request' };
			case 401:
				return { messageKey: 'global.errors.http.unauthorized' };
			case 403:
				return { messageKey: 'global.errors.http.forbidden' };
			case 404:
				return { messageKey: 'global.errors.http.not_found' };
			case 409:
				return { messageKey: 'global.errors.http.conflict' };
			case 422:
				return { messageKey: 'global.errors.http.unprocessable' };
			case 429:
				return { messageKey: 'global.errors.http.too_many_requests' };
			default:
				return { messageKey: 'global.errors.http.internal' };
		}
	}
}
