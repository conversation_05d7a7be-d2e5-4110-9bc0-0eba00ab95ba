export interface I18nMsg {
	messageKey: string; // ex. "user.email.invalid"
	params?: Record<string, unknown>; // ex. { min: 8 }
}

export interface FieldErrorGroup {
	field: string; // "email", "address.street", "items[0].name"...
	issues: I18nMsg[];
}

export interface ErrorEnvelope {
	global?: I18nMsg[];
	fields?: FieldErrorGroup[];
}

export interface ErrorResponseDto {
	ok: false;
	message: string;
	status: number;
	errors: ErrorEnvelope;
}

export interface SuccessResponseDto<TData = unknown, TMeta = unknown> {
	ok: true;
	status: number;
	data: TData;
	meta?: TMeta;
	messages?: I18nMsg[];
}

export type ApiResponseDto<T = unknown, M = unknown> = SuccessResponseDto<T, M> | ErrorResponseDto;
