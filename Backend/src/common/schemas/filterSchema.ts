import z from 'zod';

export const FilterSchema = <
	Fields extends readonly [string, ...string[]],
	Actions extends readonly [string, ...string[]]
>(
	fields: Fields,
	actions: Actions
) => {
	const test = z.string().transform((val) => {
		const [field, action, ...value] = val.split(':');
		const valueStr = value.join(':');
		return {
			field: z.enum(fields).parse(field),
			action: z.enum(actions).parse(action),
			value: valueStr
		};
	});
	return z.union([test, z.array(test)]).transform((val) => (Array.isArray(val) ? val : [val]));
};
