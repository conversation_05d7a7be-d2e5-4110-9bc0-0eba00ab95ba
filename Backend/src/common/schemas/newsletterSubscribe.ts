import { z } from 'zod';

export const NewsletterSubscribeSchema = z.object({
	email: z.email({ message: 'global.errors.validation.string_email' }).trim().default(''),
	locale: z.string().optional().default('cs')
	// TODO captcha
	// turnstile: z.string(),
});

export type NewsletterSubscribeInput = z.input<typeof NewsletterSubscribeSchema>;
export type NewsletterSubscribeValues = z.output<typeof NewsletterSubscribeSchema>;
