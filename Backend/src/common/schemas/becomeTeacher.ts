import { z } from 'zod/v4';

export const BecomeTeacherSchema = z.object({
	name: z.string('global.errors.validation.required'),
	school: z.string('global.errors.validation.required'),
	phone: z
		.string()
		.regex(/^(\+420)? ?\d{3} ?\d{3} ?\d{3}$/, 'global.errors.validation.string_phone'),
	email: z.email('global.errors.validation.string_email'),
	message: z.string('global.errors.validation.required'),

	consent: z
		.boolean()
		.refine((consent) => consent, {
			message: 'global.errors.validation.required'
		})
		.default(false)
});

export type BecomeTeacherInput = z.input<typeof BecomeTeacherSchema>;
