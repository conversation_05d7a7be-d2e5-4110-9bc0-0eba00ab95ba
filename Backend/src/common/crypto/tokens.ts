import { createHash, randomBytes, timingSafeEqual } from 'crypto';

export function createEmailToken(ttlHours = 24) {
	const raw = randomBytes(32); // 256-bit
	const token = raw.toString('base64url');
	const tokenHash = createHash('sha256').update(raw).digest('base64url');

	const expiresAt = new Date(Date.now() + ttlHours * 3600_000);
	return { token, tokenHash, expiresAt };
}

export function verifyToken(providedToken: string, storedHash: string) {
	const candidateHash = createHash('sha256')
		.update(Buffer.from(providedToken, 'base64url'))
		.digest();

	const saved = Buffer.from(storedHash, 'base64url');
	if (candidateHash.length !== saved.length) return false;
	return timingSafeEqual(candidateHash, saved);
}
