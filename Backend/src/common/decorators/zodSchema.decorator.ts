import { Body, Param, Query } from '@nestjs/common';
import { z } from 'zod';

import { ZodPipe } from '../pipes/zod.pipe';

export const ZodBody = <S extends z.ZodTypeAny>(schema: S) => Body(new ZodPipe(schema));
export const ZodQuery = <S extends z.ZodTypeAny>(schema: S) => Query(new ZodPipe(schema));
export const ZodParam = <S extends z.ZodTypeAny>(schema: S) => Param(new ZodPipe(schema));
export const UuidParam = (name: string) => Param(name, new ZodPipe(z.uuid()));
