import {
	Call<PERSON><PERSON><PERSON>,
	ExecutionContext,
	Inject,
	Injectable,
	mixin,
	NestInterceptor,
	Type
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FileInterceptor } from '@nestjs/platform-express';
import type { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { randomUUID } from 'crypto';
import MulterGoogleCloudStorage from 'multer-cloud-storage';
import path from 'path';

export type TFilenameFunction = (
	req: Request,
	file: Express.Multer.File,
	cb: (error: unknown, filename: string) => void
) => void;

export function GcsFileInterceptor(
	fieldName: string,
	dest: string,
	opts?: MulterOptions
): Type<NestInterceptor> {
	@Injectable()
	class MixinInterceptor implements NestInterceptor {
		private readonly delegate: NestInterceptor;

		constructor(@Inject(ConfigService) private readonly config: ConfigService) {
			const bucket = this.config.get<string>('GCP_BUCKET_NAME', '');
			const projectId = this.config.get<string>('GCP_PROJECT_ID', '');
			const keyFile = this.config.get<string>('GCP_KEYFILE', '');
			console.log('GCS BUCKET', bucket, projectId, keyFile);
			const storage = new MulterGoogleCloudStorage({
				bucket,
				projectId,
				keyFilename: keyFile,
				uniformBucketLevelAccess: true,
				contentType: 'auto',
				destination: dest,
				filename: ((_, file: Express.Multer.File, cb) => {
					cb(null, `${randomUUID()}${path.extname(file.originalname)}`);
				}) as TFilenameFunction
			});

			this.delegate = new (FileInterceptor(fieldName, {
				storage,
				...opts
			}))();
		}

		intercept(ctx: ExecutionContext, next: CallHandler) {
			return this.delegate.intercept(ctx, next);
		}
	}

	return mixin(MixinInterceptor);
}
