import { serialize } from 'cookie';
import type { Request, Response } from 'express';

type SameSite = 'lax' | 'strict' | 'none';

type CookieSpec = {
	name: string;
	value: string;
	options?: {
		maxAge?: number;
		expires?: Date;
		path?: string;
		domain?: string;
		httpOnly?: boolean;
		secure?: boolean;
		sameSite?: SameSite;
	};
};

export function setCookies(
	res: Response,
	cookies: CookieSpec[],
	{
		defaultSecure = process.env.NODE_ENV === 'production',
		defaultSameSite = 'strict' as SameSite
	} = {}
) {
	const serialized = cookies.map((c) =>
		serialize(c.name, c.value, {
			path: c.options?.path ?? '/',
			domain: c.options?.domain,
			httpOnly: c.options?.httpOnly ?? true,
			secure: c.options?.secure ?? defaultSecure,
			sameSite: c.options?.sameSite ?? defaultSameSite,
			maxAge: c.options?.maxAge,
			expires: c.options?.expires
		})
	);

	res.setHeader('Set-Cookie', serialized);
}

export function removeCookies(
	res: Response,
	specs: Array<{
		name: string;
		options?: {
			path?: string;
			domain?: string;
			sameSite?: SameSite;
			httpOnly?: boolean;
			secure?: boolean;
		};
	}>
) {
	const expires = new Date(0);
	setCookies(
		res,
		specs.map((s) => ({
			name: s.name,
			value: '',
			options: { ...s.options, maxAge: 0, expires }
		}))
	);
}

export function getCookieFromReq(req: Request, name: string): string | undefined {
	const cookiesUnknown = req.cookies as unknown;
	if (typeof cookiesUnknown !== 'object' || cookiesUnknown === null) {
		return undefined;
	}
	const raw = (cookiesUnknown as Record<string, unknown>)[name];
	return typeof raw === 'string' ? raw : undefined;
}
