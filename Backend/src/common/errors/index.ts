import { HttpException, HttpStatus } from '@nestjs/common';

export type I18nMsg = { messageKey: string; params?: Record<string, unknown> };

export abstract class AppHttpError extends HttpException {
	readonly code: string;
	readonly i18n: I18nMsg;

	protected constructor(status: number, code: string, i18n: I18nMsg, cause?: unknown) {
		super(code, status, { cause });
		this.code = code;
		this.i18n = i18n;
	}
}

export class InvalidCredentialsError extends AppHttpError {
	constructor() {
		super(HttpStatus.UNAUTHORIZED, 'INVALID_CREDENTIALS', {
			messageKey: 'global.errors.auth.invalid_credentials'
		});
	}
}
export class EmailRegisteredError extends AppHttpError {
	constructor() {
		super(HttpStatus.CONFLICT, 'EMAIL_REGISTERED', {
			messageKey: 'global.errors.auth.email_registered'
		});
	}
}

export class ForbiddenActionError extends AppHttpError {
	constructor(
		messageKey: string = 'global.errors.http.forbidden',
		params?: Record<string, unknown>
	) {
		super(HttpStatus.FORBIDDEN, 'FORBIDDEN', { messageKey, params });
	}
}

export class ResourceNotFoundError extends AppHttpError {
	constructor(
		messageKey: string = 'global.errors.http.not_found',
		params?: Record<string, unknown>
	) {
		super(HttpStatus.NOT_FOUND, 'NOT_FOUND', { messageKey, params });
	}
}
