import { Module } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { UsersModule } from '../users/users.module';
import { StudentsController } from './controllers/students.controller';
import { StudentsRepo } from './students.repo';
import { StudentsService } from './students.service';
import { CreateStudentUseCase, GetStudentUseCase, ListStudentsUseCase } from './useCases';

@Module({
	imports: [UsersModule, PrismaModule],
	providers: [
		StudentsRepo,
		ListStudentsUseCase,
		CreateStudentUseCase,
		GetStudentUseCase,
		StudentsService
	],
	controllers: [StudentsController],
	exports: [StudentsRepo, StudentsService]
})
export class StudentsModule {}
