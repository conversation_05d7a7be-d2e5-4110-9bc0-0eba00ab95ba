import { Injectable } from '@nestjs/common';
import { TransactionService } from 'src/infra/prisma/transaction.service';
import { UsersRepo } from 'src/modules/users/users.repo';

import { CreateStudentRequestBody } from '../schemas';
import { StudentsRepo } from '../students.repo';

@Injectable()
export class CreateStudentUseCase {
	constructor(
		private students: StudentsRepo,
		private users: UsersRepo,
		private transaction: TransactionService
	) {}
	async execute({
		email,
		emailStudent,
		billingDetails,
		firstName,
		lastName
	}: CreateStudentRequestBody) {
		const createdStudent = await this.transaction.run(async (tx) => {
			const createdUser = await this.users.create(
				{
					email,
					firstName,
					lastName,
					//TODO: send email to set password
					passwordHash: '',
					role: 'student'
				},
				tx
			);

			const {
				address,
				city,
				zip,
				email: emailBilling,
				firstName: billingFirstName,
				lastName: billingLastName,
				phone: billingPhone
			} = billingDetails || {};
			const createdStudent = await this.students.create(
				{
					emailStudent,
					userId: createdUser.id,
					billingAddress: address,
					billingCity: city,
					billingZip: zip,
					billingEmail: emailBilling,
					billingFirstName: billingFirstName,
					billingLastName: billingLastName,
					billingPhone: billingPhone
				},
				tx
			);

			return createdStudent;
		});

		return createdStudent;
	}
}
