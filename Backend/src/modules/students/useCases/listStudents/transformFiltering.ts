import { getStringFilter, indexBy } from '@common/utils';
import { Prisma } from '@prisma/client';

import { ListStudentsRequestBody } from '../../schemas/listStudents';

export const transformFiltering = (
	args: NonNullable<ListStudentsRequestBody['filters']>
): Prisma.StudentWhereInput => {
	const {
		billingAddress,
		billingCity,
		billingEmail,
		billingFirstName,
		billingLastName,
		billingPhone,
		billingZip,
		emailStudent,
		email,
		firstName,
		lastName
	} = indexBy(args, 'field');

	const studentWhereInput: Prisma.StudentWhereInput = {
		billingAddress: getStringFilter(billingAddress),
		billingCity: getStringFilter(billingCity),
		billingEmail: getStringFilter(billingEmail),
		billingFirstName: getStringFilter(billingFirstName),
		billingLastName: getStringFilter(billingLastName),
		billingPhone: getStringFilter(billingPhone),
		billingZip: getStringFilter(billingZip),
		emailStudent: getStringFilter(emailStudent)
	};
	const userWhereInput: Prisma.UserWhereInput = {
		email: getStringFilter(email),
		firstName: getStringFilter(firstName),
		lastName: getStringFilter(lastName)
	};
	const someStudentFilter = Object.values(studentWhereInput).some((v) => v !== undefined);
	const someUserFilter = Object.values(userWhereInput).some((v) => v !== undefined);

	return {
		...(someStudentFilter ? studentWhereInput : {}),
		...(someUserFilter ? { user: userWhereInput } : {})
	};
};
