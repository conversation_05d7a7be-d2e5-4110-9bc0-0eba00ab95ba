import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { ListStudentsRequestBody } from '../../schemas/listStudents';
import { StudentsRepo } from '../../students.repo';
import { transformFiltering } from './transformFiltering';
import { transformSorting } from './transformSorting';

@Injectable()
export class ListStudentsUseCase {
	constructor(private students: StudentsRepo) {}
	async execute({ skip, filters, sort, take }: ListStudentsRequestBody) {
		const findArgs: Prisma.StudentFindManyArgs = {};

		if (sort) {
			findArgs.orderBy = transformSorting(sort);
		}

		if (filters) {
			findArgs.where = transformFiltering(filters);
		}

		return this.students.list({
			skip,
			take,
			...findArgs
		});
	}
}
