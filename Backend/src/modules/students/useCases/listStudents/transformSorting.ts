import { Prisma } from '@prisma/client';
import { getSort } from 'src/common/utils';

import { ListStudentsRequestBody } from '../../schemas/listStudents';

export const transformSorting = (
	sort: NonNullable<ListStudentsRequestBody['sort']>
): Prisma.StudentOrderByWithRelationInput => {
	const studentSortInput: Prisma.StudentOrderByWithRelationInput = {
		billingAddress: getSort(sort, 'billingAddress'),
		billingCity: getSort(sort, 'billingCity'),
		billingEmail: getSort(sort, 'billingEmail'),
		billingFirstName: getSort(sort, 'billingFirstName'),
		billingLastName: getSort(sort, 'billingLastName'),
		billingPhone: getSort(sort, 'billingPhone'),
		billingZip: getSort(sort, 'billingZip')
	};
	const userSortInput: Prisma.UserOrderByWithRelationInput = {
		email: getSort(sort, 'email'),
		firstName: getSort(sort, 'firstName'),
		lastName: getSort(sort, 'lastName')
	};

	const someStudentSort = Object.values(studentSortInput).some((v) => v !== undefined);
	const someUserSort = Object.values(userSortInput).some((v) => v !== undefined);
	return {
		...(someStudentSort ? studentSortInput : {}),
		...(someUserSort ? { user: userSortInput } : {})
	};
};
