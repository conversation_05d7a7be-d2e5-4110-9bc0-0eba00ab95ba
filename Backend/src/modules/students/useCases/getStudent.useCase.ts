import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { StudentsRepo } from '../students.repo';

export type GetStudentUseCaseArgs = {
	studentId?: string;
	userId?: string;
};

@Injectable()
export class GetStudentUseCase {
	constructor(private students: StudentsRepo) {}
	async execute({ studentId, userId }: GetStudentUseCaseArgs, tx?: Prisma.TransactionClient) {
		if (!studentId && !userId) {
			throw new Error('Either studentId or userId must be provided');
		}

		const student = studentId
			? await this.students.getById(studentId, tx)
			: await this.students.getByUserId(userId!, tx);

		return student;
	}
}
