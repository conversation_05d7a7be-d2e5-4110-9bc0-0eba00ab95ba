import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, Student } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';

@Injectable()
export class StudentsRepo {
	constructor(private prisma: PrismaService) {}

	async list(
		{ include, ...args }: Prisma.StudentFindManyArgs,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.student.findMany({
			...args,
			include: {
				user: { omit: { passwordHash: true } },
				...(include ?? {})
			}
		});
	}

	async getById(studentId: string, tx: Prisma.TransactionClient = this.prisma) {
		return await tx.student.findUnique({
			where: {
				id: studentId
			}
		});
	}

	async getByUserId(userId: string, tx: Prisma.TransactionClient = this.prisma) {
		return await tx.student.findFirst({
			where: {
				userId
			}
		});
	}

	async create(
		newStudent: Prisma.StudentUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.student.create({
			data: newStudent
		});
	}

	async delete(studentId: string, tx: Prisma.TransactionClient = this.prisma) {
		try {
			return await tx.student.delete({
				where: {
					id: studentId
				}
			});
		} catch {
			// If the student doesn't exist, return null
			return null;
		}
	}

	async patch(
		{ id: studentId, ...data }: Partial<Student> & Pick<Student, 'id'>,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.student.update({
			where: {
				id: studentId
			},
			data
		});
	}
}
