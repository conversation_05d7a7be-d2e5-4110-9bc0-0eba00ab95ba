import { <PERSON>uidParam, ZodBody, ZodQuery } from '@common/decorators/zodSchema.decorator';
import { created, ok } from '@common/utils/responses';
import {
	type CreateStudentRequestBody,
	CreateStudentRequestBodySchema
} from '@modules/students/schemas';
import {
	type ListStudentsRequestBody,
	ListStudentsRequestBodySchema
} from '@modules/students/schemas/listStudents';
import {
	CreateStudentUseCase,
	GetStudentUseCase,
	ListStudentsUseCase
} from '@modules/students/useCases';
import {
	Body,
	Controller,
	Delete,
	Get,
	HttpStatus,
	NotFoundException,
	Patch,
	Post,
	Res
} from '@nestjs/common';
import type { Response } from 'express';

@Controller('students')
export class StudentsController {
	constructor(
		private listStudentsUc: ListStudentsUseCase,
		private createStudentUc: CreateStudentUseCase,
		private getStudentUc: GetStudentUseCase
	) {}

	@Get()
	async listStudents(
		@ZodQuery(ListStudentsRequestBodySchema) dto: ListStudentsRequestBody,
		@Res() res: Response
	) {
		const students = await this.listStudentsUc.execute(dto);
		const studentsTransformed = students.map(
			({ user: { id: _id, passwordHash: _passwordHash, ...user }, ...student }) => ({
				...student,
				...user
			})
		);
		return res.status(HttpStatus.OK).json(ok(studentsTransformed));
	}

	@Get(':studentId')
	async getStudent(@UuidParam('studentId') studentId: string, @Res() res: Response) {
		const student = await this.getStudentUc.execute({ studentId });
		if (!student) {
			throw new NotFoundException(`Student with id ${studentId} not found`);
		}

		return res.status(HttpStatus.OK).json(ok(student));
	}

	@Post()
	async createStudent(
		@ZodBody(CreateStudentRequestBodySchema) dto: CreateStudentRequestBody,
		@Res() res: Response
	) {
		const createdStudent = await this.createStudentUc.execute(dto);

		res.status(HttpStatus.CREATED).json(created(createdStudent));
	}

	@Patch(':studentId')
	patchStudent(
		@UuidParam('studentId') studentId: string,
		@Body() studentData: unknown,
		@Res() res: Response
	) {
		res.sendStatus(501);
	}

	@Delete(':studentId')
	deleteStudent(@UuidParam('studentId') studentId: string, @Res() res: Response) {
		res.sendStatus(501);
	}
}
