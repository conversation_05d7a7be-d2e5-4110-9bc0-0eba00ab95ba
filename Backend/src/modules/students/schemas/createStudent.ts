import z from 'zod';

export const CreateStudentRequestBodySchema = z.object({
	emailStudent: z.email(),
	billingDetails: z
		.object({
			firstName: z.string().min(1).optional(),
			lastName: z.string().min(1).optional(),
			address: z.string().min(1).optional(),
			zip: z.string().min(1).optional(),
			postalCode: z.string().min(1).optional(),
			phone: z.string().min(1).optional(),
			email: z.email().optional(),
			city: z.string().min(1).optional()
		})
		.optional(),
	firstName: z.string().min(1),
	lastName: z.string().min(1),
	email: z.email()
});
export type CreateStudentRequestBody = z.infer<typeof CreateStudentRequestBodySchema>;
