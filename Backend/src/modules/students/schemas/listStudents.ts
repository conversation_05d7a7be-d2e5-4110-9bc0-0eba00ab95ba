import { FilterSchema, SortSchema } from 'src/common/schemas';
import z from 'zod';

const FilterSchemaLocal = FilterSchema(
	[
		'firstName',
		'lastName',
		'email',
		'emailStudent',
		'billingFirstName',
		'billingLastName',
		'billingAddress',
		'billingCity',
		'billingZip',
		'billingPhone',
		'billingEmail'
	] as const,
	['equals', 'contains', 'lt', 'gt', 'lte', 'gte'] as const
);

export const ListStudentsRequestBodySchema = z
	.object({
		skip: z.coerce.number().optional(),
		take: z.coerce.number().optional(),
		sort: SortSchema([
			'firstName',
			'lastName',
			'email',
			'emailStudent',
			'billingFirstName',
			'billingLastName',
			'billingAddress',
			'billingCity',
			'billingZip',
			'billingPhone',
			'billingEmail'
		] as const),
		filters: FilterSchemaLocal.optional()
	})
	.strict();
export type ListStudentsRequestBody = z.infer<typeof ListStudentsRequestBodySchema>;
