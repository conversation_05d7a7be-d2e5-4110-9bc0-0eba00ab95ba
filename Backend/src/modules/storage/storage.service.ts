// src/storage/storage.service.ts
import { GetSignedUrlConfig, Storage } from '@google-cloud/storage';
import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class StorageService {
	private readonly bucketName: string;

	constructor(
		private readonly storage: Storage,
		private readonly config: ConfigService
	) {
		this.bucketName = this.config.get<string>('GCP_BUCKET_NAME', '');
	}

	private bucket() {
		return this.storage.bucket(this.bucketName);
	}

	async uploadBuffer(
		buffer: Buffer,
		destPath: string,
		contentType?: string
	): Promise<{ key: string; gsUri: string }> {
		try {
			const file = this.bucket().file(destPath);
			await file.save(buffer, {
				resumable: false,
				contentType,
				metadata: contentType ? { contentType } : undefined
			});
			return { key: destPath, gsUri: `gs://${this.bucketName}/${destPath}` };
		} catch {
			throw new InternalServerErrorException('GCS upload failed');
		}
	}

	async getSignedUrl(
		destPath: string,
		expiresInSeconds = 600,
		method: 'read' | 'write' = 'read',
		contentType?: string
	): Promise<string> {
		const action = method === 'read' ? 'read' : 'write';
		const cfg: GetSignedUrlConfig = {
			version: 'v4',
			action,
			expires: Date.now() + expiresInSeconds * 1000,
			contentType: action === 'write' ? contentType : undefined
		};
		const [url] = await this.bucket().file(destPath).getSignedUrl(cfg);
		return url;
	}

	async downloadFile(destPath: string): Promise<Buffer> {
		try {
			const file = this.bucket().file(destPath);
			const [buffer] = await file.download();
			return buffer;
		} catch (err) {
			console.log('ERROR', err);
			if (err instanceof Error && 'code' in err && err.code === 404) {
				throw new NotFoundException('File not found');
			}
			throw new InternalServerErrorException('GCS download failed');
		}
	}

	async deleteObject(destPath: string) {
		try {
			await this.bucket().file(destPath).delete();
		} catch (err) {
			if (err instanceof Error && 'code' in err && err.code === 404) {
				throw new NotFoundException('File not found');
			}
			throw new InternalServerErrorException('GCS delete failed');
		}
	}
}
