// src/storage/storage.module.ts
import { Storage } from '@google-cloud/storage';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { StorageService } from './storage.service';

@Module({
	imports: [ConfigModule.forRoot({ isGlobal: true })],
	providers: [
		{
			provide: Storage,
			useFactory: (config: ConfigService) => {
				return new Storage({
					projectId: config.get<string>('GCP_PROJECT_ID'),
					keyFilename: config.get<string>('GCP_KEYFILE')
				});
			},
			inject: [ConfigService]
		},
		StorageService
	],
	exports: [StorageService]
})
export class StorageModule {}
