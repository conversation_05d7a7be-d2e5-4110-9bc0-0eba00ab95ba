import { Injectable } from '@nestjs/common';
import { createLocalJWKSet, jwtVerify, SignJWT } from 'jose';

import { ATClaims } from '../../types/user';
import { Keystore } from '../jwks/keystore.service';

export type RTClaims = { sub: string; sid: string; family: string; rot: number };

@Injectable()
export class TokensService {
	constructor(private ks: Keystore) {}

	async signAT(c: ATClaims, ttlSec = 600) {
		const now = Math.floor(Date.now() / 1000);
		const jti = crypto.randomUUID();
		const { privateKey, kid, iss, apiAud } = this.ks.current();

		const token = await new SignJWT({
			sub: c.sub,
			sid: c.sid,
			permsVer: c.permsVer,
			acr: c.acr,
			amr: c.amr,
			role: c.role,
			firstname: c.firstname,
			lastname: c.lastname,
			email: c.email
		})
			.setProtectedHeader({ alg: '<PERSON><PERSON><PERSON>', kid, typ: 'JWT' })
			.setIssuer(iss)
			.setAudience(apiAud)
			.setSubject(c.sub)
			.setJti(jti)
			.setIssuedAt(now)
			.setNotBefore(now)
			.setExpirationTime(now + ttlSec)
			.sign(privateKey);

		return { token, jti, maxAgeSec: ttlSec };
	}

	async signRT(c: RTClaims, ttlSec = 60 * 60 * 24 * 60) {
		const now = Math.floor(Date.now() / 1000);
		const jti = crypto.randomUUID();
		const { privateKey, kid, iss, authAud } = this.ks.current();

		const token = await new SignJWT({
			sub: c.sub,
			sid: c.sid,
			family: c.family,
			rot: c.rot
		})
			.setProtectedHeader({ alg: 'EdDSA', kid, typ: 'JWT' })
			.setIssuer(iss)
			.setAudience(authAud)
			.setSubject(c.sub)
			.setJti(jti)
			.setIssuedAt(now)
			.setExpirationTime(now + ttlSec)
			.sign(privateKey);

		return { token, jti, maxAgeSec: ttlSec };
	}

	async verify<T>(token: string) {
		const jwks = createLocalJWKSet(this.ks.allPublic());
		return await jwtVerify<T>(token, jwks, {
			issuer: this.ks.iss,
			audience: [this.ks.apiAud, this.ks.authAud],
			clockTolerance: 5
		});
	}
}
