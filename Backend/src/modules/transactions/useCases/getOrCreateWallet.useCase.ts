import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { CreateWalletUseCase } from './createWallet.useCase';
import { GetWalletUseCase } from './getWallet.useCase';

export type GetOrCreateWalletUseCaseArgs = {
	studentId: string;
};

@Injectable()
export class GetOrCreateWalletUseCase {
	constructor(
		private getWalletUc: GetWalletUseCase,
		private createWalletUc: CreateWalletUseCase
	) {}
	async execute({ studentId }: GetOrCreateWalletUseCaseArgs, tx?: Prisma.TransactionClient) {
		const existingWallet = await this.getWalletUc.execute({ studentId }, tx);
		if (existingWallet) {
			return existingWallet;
		}

		const newWallet = await this.createWalletUc.execute({ studentId }, tx);
		return newWallet;
	}
}
