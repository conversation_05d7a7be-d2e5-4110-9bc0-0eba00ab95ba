import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';

import { CreditTransactionsRepo } from '../transactions.repo';

export type GetWalletUseCaseArgs = {
	studentId?: string;
	walletId?: string;
};

@Injectable()
export class GetWalletUseCase {
	constructor(
		private transactionsRepo: CreditTransactionsRepo,
		private prisma: PrismaService
	) {}

	async execute(
		{ studentId, walletId }: GetWalletUseCaseArgs,
		tx: Prisma.TransactionClient = this.prisma
	) {
		if (!studentId && !walletId) {
			throw new Error('Either studentId or walletId must be provided');
		}
		const wallet = await this.transactionsRepo.getWallet({ studentId, id: walletId }, tx);

		return wallet;
	}
}
