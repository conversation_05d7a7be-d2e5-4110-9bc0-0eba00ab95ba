import { Injectable, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';

import { CreditTransactionsRepo } from '../transactions.repo';
import { GetWalletUseCase } from './getWallet.useCase';

export type PatchWalletUseCaseArgs = {
	id: string;
	balance: number;
};

@Injectable()
export class PatchWalletUseCase {
	constructor(
		private transactionsRepo: CreditTransactionsRepo,
		private getWalletUc: GetWalletUseCase,
		private prisma: PrismaService
	) {}
	async execute(
		{ id, balance }: PatchWalletUseCaseArgs,
		tx: Prisma.TransactionClient = this.prisma
	) {
		const wallet = await this.getWalletUc.execute({ walletId: id }, tx);
		if (!wallet) {
			throw new NotFoundException(`Wallet with id ${id} not found`);
		}
		if (balance < 0) {
			throw new Error('Balance cannot be negative');
		}

		return await this.transactionsRepo.patchWallet({ id, balance }, tx);
	}
}
