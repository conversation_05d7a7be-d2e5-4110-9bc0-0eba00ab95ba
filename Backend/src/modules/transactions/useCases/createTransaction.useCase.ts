import { StudentsService } from '@modules/students/students.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreditTransactionDirection, CreditTransactionReason } from '@prisma/client';

import { TransactionService } from '@/infra/prisma/transaction.service';

import { CreditTransactionsRepo } from '../transactions.repo';
import { GetOrCreateWalletUseCase } from './getOrCreateWallet.useCase';
import { PatchWalletUseCase } from './patchWallet.useCase';

export type CreateTransactionUseCaseArgs = {
	studentId: string;
	amount: number;
	reason: CreditTransactionReason;
	direction: CreditTransactionDirection;
};

@Injectable()
export class CreateTransactionUseCase {
	constructor(
		private transactionsRepo: CreditTransactionsRepo,
		private studentsService: StudentsService,
		private transactionService: TransactionService,
		private getOrCreateWalletUc: GetOrCreateWalletUseCase,
		private patchWalletUc: PatchWalletUseCase
	) {}

	async execute({ studentId, amount, reason, direction }: CreateTransactionUseCaseArgs) {
		const createdTransaction = await this.transactionService.run(async (tx) => {
			const student = await this.studentsService.getStudent({ studentId }, tx);
			if (!student) {
				throw new NotFoundException(`Student with id ${studentId} not found`);
			}

			const { balance, id: walletId } = await this.getOrCreateWalletUc.execute(
				{ studentId },
				tx
			);

			const getBalancerAfter = () => {
				if (direction === 'credit') {
					return +balance + amount;
				}
				if (direction === 'debit') {
					return +balance - amount;
				}
				throw new Error('Invalid direction');
			};
			const balanceAfter = getBalancerAfter();

			if (balanceAfter < 0) {
				throw new Error('Insufficient balance');
			}

			const transaction = await this.transactionsRepo.createTransaction(
				{
					amount,
					direction,
					reason,
					wallet: { connect: { id: walletId } },
					balanceAfter
				},
				tx
			);
			const _updatedWallet = await this.patchWalletUc.execute(
				{
					id: walletId,
					balance: balanceAfter
				},
				tx
			);

			return transaction;
		});

		return createdTransaction;
	}
}
