import { StudentsService } from '@modules/students/students.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';

import { CreditTransactionsRepo } from '../transactions.repo';
import { GetWalletUseCase } from './getWallet.useCase';

export type CreateWalletUseCaseArgs = {
	studentId: string;
};

@Injectable()
export class CreateWalletUseCase {
	constructor(
		private transactionsRepo: CreditTransactionsRepo,
		private studentsService: StudentsService,
		private getWalletUc: GetWalletUseCase,
		private prisma: PrismaService
	) {}

	async execute(
		{ studentId }: CreateWalletUseCaseArgs,
		tx: Prisma.TransactionClient = this.prisma
	) {
		const student = await this.studentsService.getStudent({ studentId }, tx);
		if (!student) throw new NotFoundException(`Student with id ${studentId} not found`);

		const existingWallet = await this.getWalletUc.execute({ studentId }, tx);
		if (existingWallet) {
			throw new Error(`Wallet for student ${studentId} already exists`);
		}

		const wallet = await this.transactionsRepo.createWallet(
			{
				student: { connect: { id: student.id } }
			},
			tx
		);

		return wallet;
	}
}
