import { StudentsModule } from '@modules/students/students.module';
import { Modu<PERSON> } from '@nestjs/common';

import { PrismaModule } from '@/infra/prisma/prisma.module';

import { CreditTransactionsRepo } from './transactions.repo';
import { CreditTransactionsService } from './transactions.service';
import { CreateTransactionUseCase } from './useCases/createTransaction.useCase';
import { CreateWalletUseCase } from './useCases/createWallet.useCase';
import { GetOrCreateWalletUseCase } from './useCases/getOrCreateWallet.useCase';
import { GetWalletUseCase } from './useCases/getWallet.useCase';
import { PatchWalletUseCase } from './useCases/patchWallet.useCase';

@Module({
	imports: [PrismaModule, StudentsModule],
	providers: [
		CreateTransactionUseCase,
		CreditTransactionsRepo,
		CreateWalletUseCase,
		GetOrCreateWalletUseCase,
		GetWalletUseCase,
		PatchWalletUseCase,
		CreditTransactionsService
	],
	exports: [CreditTransactionsService]
})
export class CreditTransactionsModule {}
