import { Injectable } from '@nestjs/common';
import { CreditWallet, Prisma } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';

@Injectable()
export class CreditTransactionsRepo {
	constructor(private prisma: PrismaService) {}

	async listTransactions(
		where: Prisma.CreditTransactionWhereInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.creditTransaction.findMany({ where });
	}

	async createTransaction(
		data: Prisma.CreditTransactionCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		const createdTransaction = await tx.creditTransaction.create({ data });

		return createdTransaction;
	}

	async getWallet(
		where: Prisma.CreditWalletWhereUniqueInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.creditWallet.findUnique({ where });
	}

	async createWallet(
		data: Prisma.CreditWalletCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		const createdWallet = await tx.creditWallet.create({ data });

		return createdWallet;
	}

	async patchWallet(
		{ id, ...data }: Prisma.CreditWalletUpdateInput & Pick<CreditWallet, 'id'>,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.creditWallet.update({
			where: {
				id
			},
			data
		});
	}
}
