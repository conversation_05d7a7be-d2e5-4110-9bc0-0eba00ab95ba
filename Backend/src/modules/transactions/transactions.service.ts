import { Injectable } from '@nestjs/common';

import {
	CreateTransactionUseCase,
	CreateWalletUseCase,
	GetOrCreateWalletUseCase,
	GetWalletUseCase,
	PatchWalletUseCase
} from './useCases';

@Injectable()
export class CreditTransactionsService {
	public createTransaction: CreateTransactionUseCase['execute'];
	public createWallet: CreateWalletUseCase['execute'];
	public getOrCreateWallet: GetOrCreateWalletUseCase['execute'];
	public getWallet: GetWalletUseCase['execute'];
	public patchWallet: PatchWalletUseCase['execute'];

	constructor(
		private createTransactionUc: CreateTransactionUseCase,
		private createWalletUc: CreateWalletUseCase,
		private getOrCreateWalletUc: GetOrCreateWalletUseCase,
		private getWalletUc: GetWalletUseCase,
		private patchWalletUc: PatchWalletUseCase
	) {
		this.createTransaction = (...args) => this.createTransactionUc.execute(...args);
		this.createWallet = (...args) => this.createWalletUc.execute(...args);
		this.getOrCreateWallet = (...args) => this.getOrCreateWalletUc.execute(...args);
		this.getWallet = (...args) => this.getWalletUc.execute(...args);
		this.patchWallet = (...args) => this.patchWalletUc.execute(...args);
	}
}
