import { Module } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { PasswordResetTokensPrismaRepo } from './passwordResetTokens.prisma.repo';
import { PasswordResetTokensRepo } from './passwordResetTokens.repo';

@Module({
	imports: [PrismaModule],
	providers: [{ provide: PasswordResetTokensRepo, useClass: PasswordResetTokensPrismaRepo }],
	exports: [PasswordResetTokensRepo]
})
export class PasswordResetTokensModule {}
