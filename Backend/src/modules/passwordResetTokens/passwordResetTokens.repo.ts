import { PasswordResetToken } from '@prisma/client';

export abstract class PasswordResetTokensRepo {
	abstract create(p: {
		userId: string;
		tokenHash: string;
		expiresAt: Date;
		requestIp?: string | null;
		userAgent?: string | null;
	}): Promise<{ id: string }>;

	abstract revokeActiveByUser(userId: string): Promise<void>;
	abstract findActiveById(id: string): Promise<PasswordResetToken | null>;

	abstract markUsed(id: string): Promise<void>;

	abstract revokeAllByUser(userId: string): Promise<void>;

	abstract gc(): Promise<void>;
}
