import { Injectable } from '@nestjs/common';
import { PasswordResetToken } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { PasswordResetTokensRepo } from './passwordResetTokens.repo';

@Injectable()
export class PasswordResetTokensPrismaRepo implements PasswordResetTokensRepo {
	constructor(private prisma: PrismaService) {}

	async create(p: {
		userId: string;
		tokenHash: string;
		expiresAt: Date;
		requestIp?: string | null;
		userAgent?: string | null;
	}): Promise<{ id: string }> {
		const row = await this.prisma.passwordResetToken.create({
			data: {
				userId: p.userId,
				tokenHash: p.tokenHash,
				expiresAt: p.expiresAt,
				requestIp: p.requestIp ?? null,
				userAgent: p.userAgent ?? null
			},
			select: {
				id: true
			}
		});
		return { id: row.id };
	}

	async revokeActiveByUser(userId: string): Promise<void> {
		const now = new Date();
		await this.prisma.passwordResetToken.updateMany({
			where: {
				userId,
				usedAt: null,
				revokedAt: null,
				expiresAt: {
					gt: now
				}
			},
			data: {
				revokedAt: now
			}
		});
	}

	async findActiveById(id: string): Promise<PasswordResetToken | null> {
		const now = new Date();
		return await this.prisma.passwordResetToken.findFirst({
			where: {
				id,
				usedAt: null,
				revokedAt: null,
				expiresAt: {
					gt: now
				}
			}
		});
	}

	async markUsed(id: string): Promise<void> {
		await this.prisma.passwordResetToken.update({
			where: {
				id
			},
			data: {
				usedAt: new Date()
			}
		});
	}

	async revokeAllByUser(userId: string): Promise<void> {
		await this.prisma.passwordResetToken.updateMany({
			where: {
				userId
			},
			data: {
				revokedAt: new Date()
			}
		});
	}

	async gc(): Promise<void> {
		const sevenDaysAgo = new Date();
		sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

		await this.prisma.passwordResetToken.deleteMany({
			where: {
				OR: [
					{
						usedAt: {
							not: null,
							lt: sevenDaysAgo
						}
					},
					{
						revokedAt: {
							not: null,
							lt: sevenDaysAgo
						}
					},
					{
						expiresAt: {
							lt: sevenDaysAgo
						}
					}
				]
			}
		});
	}
}
