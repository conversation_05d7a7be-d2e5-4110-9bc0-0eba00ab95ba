import { Body, Controller, Delete, Get, HttpStatus, Patch, Post, Res } from '@nestjs/common';
import type { Response } from 'express';
import { UuidParam, ZodBody, ZodQuery } from 'src/common/decorators/zodSchema.decorator';
import { created, ok } from 'src/common/utils/responses';

import {
	type CreateManagerRequestBody,
	CreateManagerRequestBodySchema,
	type ListManagersRequestBody,
	ListManagersRequestBodySchema
} from '../schemas';
import { CreateManagerUseCase, GetManagerUseCase, ListManagersUseCase } from '../useCases';

@Controller('managers')
export class ManagersController {
	constructor(
		private listManagersUc: ListManagersUseCase,
		private createManagerUc: CreateManagerUseCase,
		private getManagerUc: GetManagerUseCase
	) {}

	@Get()
	async listManagers(
		@ZodQuery(ListManagersRequestBodySchema) dto: ListManagersRequestBody,
		@Res() res: Response
	) {
		const managers = await this.listManagersUc.execute(dto);
		const managersTransformed = managers.map(
			({ user: { id: _id, passwordHash: _passwordHash, ...user }, ...manager }) => ({
				...manager,
				...user
			})
		);
		return res.status(HttpStatus.OK).json(ok(managersTransformed));
	}

	@Get(':managerId')
	async getManager(@UuidParam('managerId') managerId: string, @Res() res: Response) {
		const manager = await this.getManagerUc.execute(managerId);
		return res.status(HttpStatus.OK).json(ok(manager));
	}

	@Post()
	async createManager(
		@ZodBody(CreateManagerRequestBodySchema) dto: CreateManagerRequestBody,
		@Res() res: Response
	) {
		const createdManager = await this.createManagerUc.execute(dto);

		res.status(HttpStatus.CREATED).json(created(createdManager));
	}

	@Patch(':managerId')
	patchManager(
		@UuidParam('managerId') managerId: string,
		@Body() managerData: unknown,
		@Res() res: Response
	) {
		res.sendStatus(501);
	}

	@Delete(':managerId')
	deleteManager(@UuidParam('managerId') managerId: string, @Res() res: Response) {
		res.sendStatus(501);
	}
}
