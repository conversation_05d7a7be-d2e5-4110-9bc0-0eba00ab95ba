import { <PERSON>, Prisma, User } from '@prisma/client';

export abstract class ManagersRepo {
	abstract getById(managerId: string): Promise<Manager | null>;
	abstract getByUserId(userId: string): Promise<Manager | null>;
	abstract create(
		u: Prisma.ManagerUncheckedCreateInput,
		tx?: Prisma.TransactionClient
	): Promise<Manager>;
	abstract patch(u: Partial<Manager> & Pick<Manager, 'id'>): Promise<Manager>;
	abstract delete(managerId: string): Promise<Manager | null>;
	abstract list(args?: Prisma.ManagerFindManyArgs): Promise<
		(Manager & {
			user: User;
		})[]
	>;
}
