import { FilterSchema, SortSchema } from 'src/common/schemas';
import z from 'zod';

const managerFields = ['firstName', 'lastName', 'email'] as const;

const FilterSchemaLocal = FilterSchema(managerFields, [
	'equals',
	'contains',
	'lt',
	'gt',
	'lte',
	'gte'
] as const);

export const ListManagersRequestBodySchema = z
	.object({
		skip: z.coerce.number().optional(),
		take: z.coerce.number().optional(),
		sort: SortSchema(managerFields),
		filters: FilterSchemaLocal.optional()
	})
	.strict();
export type ListManagersRequestBody = z.infer<typeof ListManagersRequestBodySchema>;
