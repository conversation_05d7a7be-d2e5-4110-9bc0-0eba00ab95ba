import { getStringFilter, indexBy } from '@common/utils';
import { Prisma } from '@prisma/client';

import { ListManagersRequestBody } from '../../schemas/listManagers';

export const transformFiltering = (
	args: NonNullable<ListManagersRequestBody['filters']>
): Prisma.ManagerWhereInput => {
	const { email, firstName, lastName } = indexBy(args, 'field');

	const userWhereInput: Prisma.UserWhereInput = {
		email: getStringFilter(email),
		firstName: getStringFilter(firstName),
		lastName: getStringFilter(lastName)
	};
	const someUserFilter = Object.values(userWhereInput).some((v) => v !== undefined);

	return {
		...(someUserFilter ? { user: userWhereInput } : {})
	};
};
