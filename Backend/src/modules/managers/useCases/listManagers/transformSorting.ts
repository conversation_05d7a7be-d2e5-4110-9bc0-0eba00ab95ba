import { Prisma } from '@prisma/client';
import { getSort } from 'src/common/utils';

import { ListManagersRequestBody } from '../../schemas/listManagers';

export const transformSorting = (
	sort: NonNullable<ListManagersRequestBody['sort']>
): Prisma.ManagerOrderByWithRelationInput => {
	const userSortInput: Prisma.UserOrderByWithRelationInput = {
		email: getSort(sort, 'email'),
		firstName: getSort(sort, 'firstName'),
		lastName: getSort(sort, 'lastName')
	};

	const someUserSort = Object.values(userSortInput).some((v) => v !== undefined);
	return {
		...(someUserSort ? { user: userSortInput } : {})
	};
};
