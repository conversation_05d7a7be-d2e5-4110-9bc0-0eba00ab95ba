import { Injectable } from '@nestjs/common';
import { TransactionService } from 'src/infra/prisma/transaction.service';
import { UsersRepo } from 'src/modules/users/users.repo';

import { ManagersRepo } from '../managers.repo';
import { CreateManagerRequestBody } from '../schemas';

@Injectable()
export class CreateManagerUseCase {
	constructor(
		private managers: ManagersRepo,
		private users: UsersRepo,
		private transaction: TransactionService
	) {}
	async execute({ email, firstName, lastName }: CreateManagerRequestBody) {
		const createdManager = await this.transaction.run(async (tx) => {
			const createdUser = await this.users.create(
				{
					email,
					firstName,
					lastName,
					//TODO: send email to set password
					passwordHash: '',
					role: 'manager'
				},
				tx
			);

			const createdManager = await this.managers.create(
				{
					userId: createdUser.id
				},
				tx
			);

			return createdManager;
		});

		return createdManager;
	}
}
