import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { UsersModule } from '../users/users.module';
import { ManagersController } from './controllers/managers.controller';
import { ManagersPrismaRepo } from './managers.prisma.repo';
import { ManagersRepo } from './managers.repo';
import { CreateManagerUseCase, GetManagerUseCase, ListManagersUseCase } from './useCases';

@Module({
	imports: [UsersModule, PrismaModule],
	providers: [
		{ provide: ManagersRepo, useClass: ManagersPrismaRepo },
		ListManagersUseCase,
		CreateManagerUseCase,
		GetManagerUseCase
	],
	controllers: [ManagersController],
	exports: [ManagersRepo]
})
export class ManagersModule {}
