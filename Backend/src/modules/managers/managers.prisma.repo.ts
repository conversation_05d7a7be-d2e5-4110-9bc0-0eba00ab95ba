import { Injectable } from '@nestjs/common';
import { <PERSON>, Prisma, User } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { ManagersRepo } from './managers.repo';

@Injectable()
export class ManagersPrismaRepo implements ManagersRepo {
	constructor(private prisma: PrismaService) {}

	async list({
		include,
		...args
	}: Prisma.ManagerFindManyArgs): Promise<(Manager & { user: User })[]> {
		return await this.prisma.manager.findMany({
			...args,
			include: {
				user: true,
				...(include ?? {})
			}
		});
	}

	async getById(managerId: string): Promise<Manager | null> {
		return await this.prisma.manager.findUnique({
			where: {
				id: managerId
			}
		});
	}

	async getByUserId(userId: string): Promise<Manager | null> {
		return await this.prisma.manager.findFirst({
			where: {
				userId
			}
		});
	}

	async create(
		newManager: Prisma.ManagerUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<Manager> {
		return await tx.manager.create({
			data: newManager
		});
	}

	async delete(managerId: string): Promise<Manager | null> {
		try {
			return await this.prisma.manager.delete({
				where: {
					id: managerId
				}
			});
		} catch {
			// If the manager doesn't exist, return null
			return null;
		}
	}

	async patch({
		id: managerId,
		...data
	}: Partial<Manager> & Pick<Manager, 'id'>): Promise<Manager> {
		return await this.prisma.manager.update({
			where: {
				id: managerId
			},
			data
		});
	}
}
