import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { importJWK, JWK } from 'jose';

type PrivateJwk = JWK & { d: string; kid: string };

@Injectable()
export class Keystore {
	private currentKid: string;
	iss: string;
	apiAud: string;
	authAud: string;

	private signingKey?: CryptoKey | Uint8Array<ArrayBufferLike>;
	private verifyingKeys = new Map<string, CryptoKey | Uint8Array<ArrayBufferLike>>();
	private publicJwks: Record<string, JWK> = {};

	constructor(private readonly cfg: ConfigService) {}

	async init() {
		this.iss = this.cfg.get<string>('ISS')!;
		this.apiAud = this.cfg.get<string>('API_AUD')!;
		this.authAud = this.cfg.get<string>('AUTH_AUD')!;
		this.currentKid = this.cfg.get<string>('JWKS_KID_CURRENT')!;

		const set = this.cfg.get<Record<string, PrivateJwk>>('privateSet')!;

		for (const [kid, jwk] of Object.entries(set)) {
			if (jwk.kty !== 'OKP' || jwk.crv !== 'Ed25519') continue;

			const pub: JWK = {
				kty: 'OKP',
				crv: 'Ed25519',
				x: jwk.x,
				kid: kid,
				alg: 'EdDSA',
				use: 'sig'
			};
			const pubKey = await importJWK(pub, 'EdDSA');

			this.verifyingKeys.set(kid, pubKey);
			this.publicJwks[kid] = pub;

			if (kid === this.currentKid) {
				this.signingKey = await importJWK(jwk, 'EdDSA');
			}
		}

		if (!this.signingKey) {
			throw new Error(`Missing private key for current kid=${this.currentKid}`);
		}
	}

	current() {
		if (!this.signingKey) throw new Error('Signing key not initialized');
		return {
			kid: this.currentKid,
			privateKey: this.signingKey,
			publicKey: this.verifyingKeys.get(this.currentKid)!,
			iss: this.iss,
			apiAud: this.apiAud,
			authAud: this.authAud
		};
	}

	allPublic() {
		return { keys: Object.values(this.publicJwks) };
	}

	getPublicKeyByKid(kid: string) {
		const key = this.verifyingKeys.get(kid);
		if (!key) throw new Error(`Unknown kid: ${kid}`);
		return key;
	}
}
