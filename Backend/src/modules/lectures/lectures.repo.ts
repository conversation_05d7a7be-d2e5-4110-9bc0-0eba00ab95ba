import { Injectable } from '@nestjs/common';
import { Lecture, LectureReservation, Prisma } from '@prisma/client';
import { merge } from 'lodash';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { TransactionService } from '@/infra/prisma/transaction.service';

import { getReservationInclude } from './utils';

@Injectable()
export class LecturesRepo {
	constructor(
		private prisma: PrismaService,
		private transaction: TransactionService
	) {}

	async getLectureById(lectureId: string, tx: Prisma.TransactionClient = this.prisma) {
		return await tx.lecture.findUnique({
			where: {
				id: lectureId
			}
		});
	}

	async createLecture(
		data: Prisma.LectureCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.lecture.create({
			data
		});
	}

	async createLectures(lectures: Prisma.LectureCreateInput[], tx?: Prisma.TransactionClient) {
		const transactionAction = async (tx: Prisma.TransactionClient) => {
			const promises = lectures.map((lecture) => this.createLecture(lecture, tx));
			return Promise.all(promises);
		};

		return tx ? await transactionAction(tx) : await this.transaction.run(transactionAction);
	}

	async patchLecture(
		{ id, ...data }: Prisma.LectureUpdateInput & Pick<Lecture, 'id'>,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.lecture.update({
			where: {
				id
			},
			data
		});
	}

	async patchLectures(
		lectures: (Prisma.LectureUpdateInput & Pick<Lecture, 'id'>)[],
		tx?: Prisma.TransactionClient
	) {
		const tA = async (tx: Prisma.TransactionClient) => {
			const promises = lectures.map((lecture) => this.patchLecture(lecture, tx));
			return Promise.all(promises);
		};

		return tx ? await tA(tx) : await this.transaction.run(tA);
	}

	async listLectures(
		{ where, include, ...args }: Prisma.LectureFindManyArgs,
		tx?: Prisma.TransactionClient
	) {
		const tA = async (tx: Prisma.TransactionClient) => {
			const dataPromise = tx.lecture.findMany({
				...args,
				where,
				include: merge(
					{},
					{
						_count: { select: { reservations: true } }
					},
					include
				)
			});
			const countPromise = tx.lecture.count({ where });

			const [data, count] = await Promise.all([dataPromise, countPromise]);
			return { data, count };
		};
		return tx ? await tA(tx) : await this.transaction.run(tA);
	}

	async getReservationById(reservationId: string, tx: Prisma.TransactionClient = this.prisma) {
		return await tx.lectureReservation.findUnique({
			where: {
				id: reservationId
			},
			include: getReservationInclude()
		});
	}

	async findReservation(
		where: Prisma.LectureReservationWhereInput,
		tx: Prisma.TransactionClient = this.prisma
	) {
		return await tx.lectureReservation.findFirst({
			where,
			include: getReservationInclude()
		});
	}

	async listReservations(
		{ where, include, ...args }: Prisma.LectureReservationFindManyArgs,
		tx?: Prisma.TransactionClient
	) {
		const tA = async (tx: Prisma.TransactionClient) => {
			const dataPromise = tx.lectureReservation.findMany({
				where,
				include: merge({}, getReservationInclude(), include),
				...args
			});
			const countPromise = tx.lectureReservation.count({ where });

			const [data, count] = await Promise.all([dataPromise, countPromise]);
			return { data, count };
		};
		return tx ? await tA(tx) : await this.transaction.run(tA);
	}

	async createReservation(
		data: Prisma.LectureReservationCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<LectureReservation> {
		return await tx.lectureReservation.create({
			data,
			include: getReservationInclude()
		});
	}

	async cancelReservation(
		reservationId: string,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<void> {
		await tx.lectureReservation.delete({
			where: { id: reservationId }
		});
		return;
	}
}
