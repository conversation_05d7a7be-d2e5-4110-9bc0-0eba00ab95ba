import { GetLocationUseCase } from '@modules/locations/useCases';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { LecturesRepo } from '../../lectures.repo';
import { type CreateLecture } from '../../schemas';
import { generateSchedule } from './generateSchedule';

@Injectable()
export class CreateLectureUseCase {
	constructor(
		private getLocationUc: GetLocationUseCase,
		private lecturesRepo: LecturesRepo
	) {}
	async execute({
		createdById,
		dateEnd,
		dateStart,
		lang,
		name,
		price,
		type,
		locationId,
		notes,
		capacity,
		repeatDays,
		repeatUntil
	}: CreateLecture) {
		if (locationId) {
			await this.getLocationUc.execute(locationId);
		}
		if (dateEnd.isSameOrBefore(dateStart)) {
			throw new Error('dateEnd must be after dateStart');
		}
		if (repeatUntil && repeatUntil.isSameOrBefore(dateEnd)) {
			throw new Error('repeatUntil must be after dateEnd');
		}

		const schedule = generateSchedule({
			dateStart,
			dateEnd,
			repeatDays,
			repeatUntil
		});

		const groupId = crypto.randomUUID();
		const lectures: Prisma.LectureCreateInput[] = schedule.map(({ dateEnd, dateStart }) => {
			return {
				lang,
				name,
				price,
				type,
				notes,
				capacity,
				groupId,
				dateEnd: dateEnd.toISOString(),
				dateStart: dateStart.toISOString(),
				createdBy: { connect: { id: createdById } },
				updatedBy: { connect: { id: createdById } },
				location: locationId ? { connect: { id: locationId } } : undefined
			};
		});

		const createdLectures = await this.lecturesRepo.createLectures(lectures);
		return createdLectures;
	}
}
