import { CreateLecture } from '@modules/lectures/schemas';
import { Moment } from 'moment';

type Weekday = 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday';

const WEEKDAY_TO_INDEX: Record<Weekday, number> = {
	sunday: 0,
	monday: 1,
	tuesday: 2,
	wednesday: 3,
	thursday: 4,
	friday: 5,
	saturday: 6
};

export interface ScheduleItem {
	dateStart: Moment;
	dateEnd: Moment;
}

export function generateSchedule({
	dateStart,
	dateEnd,
	repeatDays,
	repeatUntil
}: Pick<CreateLecture, 'dateStart' | 'dateEnd' | 'repeatDays' | 'repeatUntil'>): ScheduleItem[] {
	// No repetition → single instance
	if (!repeatDays?.length || !repeatUntil) {
		return [{ dateStart, dateEnd }];
	}

	// Defensive clones so we never mutate inputs
	const start = dateStart.clone();
	const until = repeatUntil.clone().endOf('day');

	// Helper: copy time-of-day from `from` to `to`
	const withTimeFrom = (to: Moment, from: Moment) =>
		to
			.clone()
			.hour(from.hour())
			.minute(from.minute())
			.second(from.second())
			.millisecond(from.millisecond());

	const results: ScheduleItem[] = [];

	// Deduplicate days if user passed duplicates
	const uniqueDays = Array.from(new Set(repeatDays));

	for (const day of uniqueDays) {
		const dayIndex = WEEKDAY_TO_INDEX[day];

		// First occurrence of `day` on/after `start`
		let cur = start.clone().day(dayIndex);
		if (cur.isBefore(start, 'day')) cur.add(1, 'week');

		// Walk forward weekly on that weekday
		while (!cur.isAfter(until, 'day')) {
			// Build end by copying time from original dateEnd
			const curEnd = withTimeFrom(cur, dateEnd);
			results.push({ dateStart: cur.clone(), dateEnd: curEnd });
			cur = cur.add(1, 'week');
		}
	}

	// Keep output stable and chronological
	results.sort((a, b) => a.dateStart.valueOf() - b.dateStart.valueOf());
	return results;
}
