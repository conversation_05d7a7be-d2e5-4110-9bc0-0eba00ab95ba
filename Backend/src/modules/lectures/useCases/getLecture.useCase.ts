import { Injectable, NotFoundException } from '@nestjs/common';

import { LecturesRepo } from '../lectures.repo';

@Injectable()
export class GetLectureUseCase {
	constructor(private lecturesRepo: LecturesRepo) {}
	async execute(lectureId: string) {
		const lecture = await this.lecturesRepo.getLectureById(lectureId);
		if (!lecture) throw new NotFoundException(`Lecture with id ${lectureId} not found`);

		return lecture;
	}
}
