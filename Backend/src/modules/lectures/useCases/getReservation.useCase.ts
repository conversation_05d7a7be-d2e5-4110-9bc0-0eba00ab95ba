import { Injectable, NotFoundException } from '@nestjs/common';

import { LecturesRepo } from '../lectures.repo';

@Injectable()
export class GetReservationUseCase {
	constructor(private lecturesRepo: LecturesRepo) {}

	async execute(reservationId: string) {
		const reservation = await this.lecturesRepo.getReservationById(reservationId);
		if (!reservation) {
			throw new NotFoundException(`Reservation with id ${reservationId} not found`);
		}

		return reservation;
	}
}
