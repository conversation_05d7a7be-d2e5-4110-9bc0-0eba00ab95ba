import { getSort } from '@common/utils';
import { ListLecturesRequestQuery } from '@modules/lectures/schemas';
import { Prisma } from '@prisma/client';

export const transformSorting = (
	sort: NonNullable<ListLecturesRequestQuery['sort']>
): Prisma.LectureOrderByWithRelationInput => {
	const lectureSortInput: Prisma.LectureOrderByWithRelationInput = {
		name: getSort(sort, 'name'),
		price: getSort(sort, 'price'),
		dateStart: getSort(sort, 'dateStart'),
		dateEnd: getSort(sort, 'dateEnd'),
		lang: getSort(sort, 'lang'),
		type: getSort(sort, 'type'),
		capacity: getSort(sort, 'capacity')
	};
	return lectureSortInput;
};
