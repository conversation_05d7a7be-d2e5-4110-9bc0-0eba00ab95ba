import { getNumberFilter, getStringFilter } from '@common/utils';
import { ListLectures } from '@modules/lectures/schemas';
import { Prisma } from '@prisma/client';

export const transformFiltering = (
	args: NonNullable<ListLectures['filters']>
): Prisma.LectureWhereInput => {
	const filters: Partial<Record<(typeof args)[number]['field'], (typeof args)[number]>> = {};
	for (const filter of args) {
		filters[filter.field] = filter;
	}
	const { capacity, dateEnd, dateStart, lang, locationId, name, price, type } = filters;

	return {
		capacity: getNumberFilter(capacity),
		name: getStringFilter(name),
		price: getNumberFilter(price),
		dateEnd: getStringFilter(dateEnd),
		dateStart: getStringFilter(dateStart),
		locationId: getStringFilter(locationId),
		type: getStringFilter(type) as Prisma.EnumLectureTypeFilter | undefined,
		lang: getStringFilter(lang) as Prisma.EnumLangFilter | undefined
	};
};
