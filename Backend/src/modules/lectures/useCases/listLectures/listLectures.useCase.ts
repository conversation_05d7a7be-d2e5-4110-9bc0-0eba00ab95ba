import { ListLectures } from '@modules/lectures/schemas';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { merge } from 'lodash';

import { LecturesRepo } from '../../lectures.repo';
import { transformFiltering } from './transformFiltering';
import { transformSorting } from './transformSorting';

@Injectable()
export class ListLecturesUseCase {
	constructor(private lecturesRepo: LecturesRepo) {}
	async execute({ skip, filters, sort, take }: ListLectures) {
		const findArgs: Prisma.LectureFindManyArgs = {
			where: {
				cancelledAt: null
			}
		};

		if (sort) {
			findArgs.orderBy = transformSorting(sort);
		}

		if (filters) {
			findArgs.where = merge(findArgs.where, transformFiltering(filters));
		}

		return this.lecturesRepo.listLectures({
			...findArgs,
			skip,
			take
		});
	}
}
