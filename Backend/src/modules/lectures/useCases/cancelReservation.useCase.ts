import { StudentsRepo } from '@modules/students/students.repo';
import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import moment from 'moment';

import { TransactionService } from '@/infra/prisma/transaction.service';

import { LecturesRepo } from '../lectures.repo';
import { CancelReservation } from '../schemas';

@Injectable()
export class CancelReservationUseCase {
	constructor(
		private lecturesRepo: LecturesRepo,
		private studentsRepo: StudentsRepo,
		private transaction: TransactionService
	) {}
	async execute({ lectureId, userId }: CancelReservation) {
		await this.transaction.run(async (tx) => {
			const lecture = await this.lecturesRepo.getLectureById(lectureId, tx);
			if (!lecture) throw new NotFoundException(`Lecture with id ${lectureId} not found`);

			if (lecture.cancelledAt) {
				throw new HttpException('Cannot cancel reservation for a cancelled lecture', 400);
			}

			const student = await this.studentsRepo.getByUserId(userId, tx);
			if (!student) throw new NotFoundException(`Student with id ${userId} not found`);

			const existingReservation = await this.lecturesRepo.findReservation(
				{
					lectureId,
					studentId: student.id
				},
				tx
			);
			if (!existingReservation) {
				throw new HttpException('No reservation found to cancel', 404);
			}

			// Check if the cancellation is being made at least 1 hour before the lecture starts
			if (moment.utc().isAfter(moment.utc(lecture.dateStart).subtract(1, 'hour'))) {
				throw new HttpException(
					'Cannot cancel reservation less than 1 hour before the lecture starts',
					400
				);
			}

			await this.lecturesRepo.cancelReservation(existingReservation.id, tx);
		});
	}
}
