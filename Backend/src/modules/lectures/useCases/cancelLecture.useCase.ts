import { Injectable, NotFoundException } from '@nestjs/common';

import { TransactionService } from '@/infra/prisma/transaction.service';

import { LecturesRepo } from '../lectures.repo';
import { CancelLecture } from '../schemas/cancelLecture';
import { LecturesScope } from '../types';

@Injectable()
export class CancelLectureUseCase {
	constructor(
		private lecturesRepo: LecturesRepo,
		private transaction: TransactionService
	) {}
	async execute({ canceledById, scope, lectureId }: CancelLecture) {
		const updatedLectures = await this.transaction.run(async (tx) => {
			const targetLecture = await this.lecturesRepo.getLectureById(lectureId, tx);
			if (!targetLecture) {
				throw new NotFoundException(`Lecture with id ${lectureId} not found`);
			}

			// Determine which lectures to update based on the scope
			const lecturesToUpdate: Set<string> = new Set([targetLecture.id]);
			if (scope === LecturesScope.ALL) {
				const { data: allLectures } = await this.lecturesRepo.listLectures(
					{
						where: { groupId: targetLecture.groupId }
					},
					tx
				);
				allLectures.forEach(({ id }) => lecturesToUpdate.add(id));
			}
			if (scope === LecturesScope.CURRENT_AND_FUTURE) {
				const { data: futureLectures } = await this.lecturesRepo.listLectures(
					{
						where: {
							groupId: targetLecture.groupId,
							dateStart: { gt: targetLecture.dateStart }
						}
					},
					tx
				);
				futureLectures.forEach(({ id }) => lecturesToUpdate.add(id));
			}

			// Prepare update data for each lecture
			const lectureUpdates = Array.from(lecturesToUpdate).map((id) => ({
				id,
				cancelledAt: new Date(),
				cancelledBy: { connect: { id: canceledById } }
			}));

			return await this.lecturesRepo.patchLectures(lectureUpdates, tx);
		});

		return updatedLectures;
	}
}
