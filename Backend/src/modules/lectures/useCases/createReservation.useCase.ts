import { StudentsRepo } from '@modules/students/students.repo';
import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import moment from 'moment';

import { TransactionService } from '@/infra/prisma/transaction.service';

import { LecturesRepo } from '../lectures.repo';
import { CreateReservation } from '../schemas';

@Injectable()
export class CreateReservationUseCase {
	constructor(
		private lecturesRepo: LecturesRepo,
		private studentsRepo: StudentsRepo,
		private transaction: TransactionService
	) {}
	async execute({ lectureId, userId }: CreateReservation) {
		return await this.transaction.run(async (tx) => {
			const lecture = await this.lecturesRepo.getLectureById(lectureId, tx);
			if (!lecture) throw new NotFoundException(`Lecture with id ${lectureId} not found`);

			if (lecture.cancelledAt) {
				throw new HttpException('Cannot create reservation for a cancelled lecture', 400);
			}

			const student = await this.studentsRepo.getByUserId(userId, tx);
			if (!student) throw new NotFoundException(`Student with id ${userId} not found`);

			const existingReservation = await this.lecturesRepo.findReservation(
				{
					lectureId,
					studentId: student.id
				},
				tx
			);
			if (existingReservation) {
				throw new HttpException('You have already reserved a spot for this lecture', 400);
			}

			// Check if the reservation is being made at least 1 hour before the lecture starts
			if (moment.utc().isAfter(moment.utc(lecture.dateStart).subtract(1, 'hour'))) {
				throw new HttpException(
					'Cannot create reservation less than 1 hour before the lecture starts',
					400
				);
			}

			const lectureReservations = await this.lecturesRepo.listReservations(
				{
					where: {
						lectureId
					}
				},
				tx
			);
			if (lecture.capacity && lectureReservations.count >= lecture.capacity) {
				throw new HttpException('Lecture is full', 400);
			}

			const reservation = await this.lecturesRepo.createReservation(
				{
					lecture: { connect: { id: lectureId } },
					student: { connect: { id: student.id } }
				},
				tx
			);

			return reservation;
		});
	}
}
