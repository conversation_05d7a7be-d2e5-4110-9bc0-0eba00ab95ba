import { ListReservations } from '@modules/lectures/schemas';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { LecturesRepo } from '../../lectures.repo';
import { transformFiltering } from './transformFiltering';

@Injectable()
export class ListReservationsUseCase {
	constructor(private lecturesRepo: LecturesRepo) {}
	async execute({ filters, studentId, lectureId }: ListReservations) {
		const findArgs: Prisma.LectureReservationFindManyArgs = {};

		// Build where clause
		const whereConditions: Prisma.LectureReservationWhereInput[] = [];

		// Add studentId filter if provided
		if (studentId) {
			whereConditions.push({ studentId: studentId });
		}

		// Add lectureId filter if provided
		if (lectureId) {
			whereConditions.push({ lectureId: lectureId });
		}

		// Add date filters if provided
		if (filters) {
			whereConditions.push(transformFiltering(filters));
		}

		// Combine all where conditions
		if (whereConditions.length > 0) {
			findArgs.where = {
				AND: whereConditions
			};
		}

		return this.lecturesRepo.listReservations({
			...findArgs
		});
	}
}
