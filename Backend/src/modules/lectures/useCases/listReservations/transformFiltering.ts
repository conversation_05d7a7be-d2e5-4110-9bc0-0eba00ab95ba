import { getStringFilter } from '@common/utils';
import { ListReservations } from '@modules/lectures/schemas';
import { Prisma } from '@prisma/client';

export const transformFiltering = (
	args: NonNullable<ListReservations['filters']>
): Prisma.LectureReservationWhereInput => {
	const filters: Partial<Record<(typeof args)[number]['field'], (typeof args)[number]>> = {};
	for (const filter of args) {
		filters[filter.field] = filter;
	}
	const { dateEnd, dateStart } = filters;

	return {
		lecture: {
			dateEnd: getStringFilter(dateEnd),
			dateStart: getStringFilter(dateStart)
		}
	};
};
