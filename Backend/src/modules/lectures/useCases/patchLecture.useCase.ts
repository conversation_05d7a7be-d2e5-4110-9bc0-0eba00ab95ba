import { Injectable, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { isNumber } from 'lodash';
import moment from 'moment';

import { TransactionService } from '@/infra/prisma/transaction.service';

import { LecturesRepo } from '../lectures.repo';
import { PatchLecture } from '../schemas';
import { LecturesScope } from '../types';

@Injectable()
export class PatchLectureUseCase {
	constructor(
		private lecturesRepo: LecturesRepo,
		private transaction: TransactionService
	) {}
	async execute({
		lectureId,
		name,
		notes,
		updatedById,
		scope,
		timeEnd,
		timeStart
	}: PatchLecture) {
		const updatedLectures = await this.transaction.run(async (tx) => {
			const targetLecture = await this.lecturesRepo.getLectureById(lectureId, tx);
			if (!targetLecture) {
				throw new NotFoundException(`Lecture with id ${lectureId} not found`);
			}

			// Determine which lectures to update based on the scope
			const lecturesToUpdate: Set<string> = new Set([targetLecture.id]);
			if (scope === LecturesScope.ALL) {
				const { data: allLectures } = await this.lecturesRepo.listLectures(
					{
						where: { groupId: targetLecture.groupId }
					},
					tx
				);
				allLectures.forEach(({ id }) => lecturesToUpdate.add(id));
			}
			if (scope === LecturesScope.CURRENT_AND_FUTURE) {
				const { data: futureLectures } = await this.lecturesRepo.listLectures(
					{
						where: {
							groupId: targetLecture.groupId,
							dateStart: { gt: targetLecture.dateStart }
						}
					},
					tx
				);
				futureLectures.forEach(({ id }) => lecturesToUpdate.add(id));
			}

			// Prepare update data for each lecture
			const lectureUpdates = await Promise.all(
				Array.from(lecturesToUpdate).map(async (id) => {
					//console.log('Updating lecture ID:', id);
					const lecture = await this.lecturesRepo.getLectureById(id, tx);
					if (!lecture) throw new NotFoundException(`Lecture with id ${id} not found`);

					const updateValues: Prisma.LectureUpdateInput = {
						name,
						notes,
						updatedBy: { connect: { id: updatedById } }
					};

					if (timeStart) {
						const [hours, minutes] = timeStart.split(':');
						if (!isNumber(+hours)) {
							throw new Error('Invalid timeStart format');
						}
						if (!isNumber(+minutes)) {
							throw new Error('Invalid timeStart format');
						}
						updateValues.dateStart = moment
							.utc(targetLecture.dateStart, moment.ISO_8601)
							.hours(+hours)
							.minutes(+minutes)
							.toISOString();
					}

					if (timeEnd) {
						const [hours, minutes] = timeEnd.split(':');
						if (!isNumber(+hours)) {
							throw new Error('Invalid timeEnd format');
						}
						if (!isNumber(+minutes)) {
							throw new Error('Invalid timeEnd format');
						}
						updateValues.dateEnd = moment
							.utc(targetLecture.dateEnd, moment.ISO_8601)
							.hours(+hours)
							.minutes(+minutes)
							.toISOString();
					}

					return { ...updateValues, id };
				})
			);

			return await this.lecturesRepo.patchLectures(lectureUpdates, tx);
		});

		return updatedLectures;
	}
}
