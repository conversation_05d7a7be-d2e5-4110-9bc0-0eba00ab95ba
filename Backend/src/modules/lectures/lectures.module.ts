import { LocationsModule } from '@modules/locations/locations.module';
import { StorageModule } from '@modules/storage/storage.module';
import { StudentsModule } from '@modules/students/students.module';
import { Module } from '@nestjs/common';

import { PrismaModule } from '@/infra/prisma/prisma.module';

import { LecturesController } from './lectures.controller';
import { LecturesRepo } from './lectures.repo';
import {
	CancelLectureUseCase,
	CancelReservationUseCase,
	CreateLectureUseCase,
	CreateReservationUseCase,
	GetLectureUseCase,
	GetReservationUseCase,
	ListLecturesUseCase,
	ListReservationsUseCase,
	PatchLectureUseCase
} from './useCases';

@Module({
	imports: [PrismaModule, StorageModule, LocationsModule, StudentsModule],
	providers: [
		LecturesRepo,
		CreateLectureUseCase,
		PatchLectureUseCase,
		GetLectureUseCase,
		ListLecturesUseCase,
		ListReservationsUseCase,
		CancelLectureUseCase,
		CreateReservationUseCase,
		CancelReservationUseCase,
		GetReservationUseCase
	],
	controllers: [LecturesController],
	exports: []
})
export class LecturesModule {}
