import { zodMomentOptional, zodMomentRequired } from '@common/utils';
import { Lang, LectureType } from '@prisma/client';
import z from 'zod';

export const CreateLectureSchema = z.object({
	type: z.enum(LectureType),
	name: z.string().min(1),
	lang: z.enum(Lang),
	price: z.number().min(0),
	dateStart: zodMomentRequired(),
	dateEnd: zodMomentRequired(),
	notes: z.string().optional(),
	capacity: z.number().min(1).optional(),
	locationId: z.uuid().optional(),

	repeatDays: z
		.array(
			z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
		)
		.min(1)
		.optional(),
	repeatUntil: zodMomentOptional()
});
export const CreateLectureRequestBodySchema = CreateLectureSchema;

export type CreateLecture = z.infer<typeof CreateLectureSchema> & {
	createdById: string;
};
export type CreateLectureRequestBody = z.infer<typeof CreateLectureSchema>;
