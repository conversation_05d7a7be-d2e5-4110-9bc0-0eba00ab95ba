import z from 'zod';

import { LecturesScope } from '../types';

export const PatchLectureRequestBodySchema = z.object({
	scope: z.enum(LecturesScope).optional(),
	name: z.string().min(1).optional(),
	timeStart: z.string().optional(),
	timeEnd: z.string().optional(),
	notes: z.string().optional()
});

export type PatchLecture = z.infer<typeof PatchLectureRequestBodySchema> & {
	updatedById: string;
	lectureId: string;
};
export type PatchLectureRequestBody = z.infer<typeof PatchLectureRequestBodySchema>;
