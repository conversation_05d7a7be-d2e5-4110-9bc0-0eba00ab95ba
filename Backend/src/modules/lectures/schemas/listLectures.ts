import { FilterSchema, SortSchema } from '@common/schemas';
import z from 'zod';

const lectureFields = [
	'name',
	'type',
	'price',
	'lang',
	'dateStart',
	'dateEnd',
	'canceled',
	'capacity',
	'locationId'
] as const;

const FilterSchemaLocal = FilterSchema(lectureFields, [
	'equals',
	'contains',
	'lt',
	'gt',
	'lte',
	'gte'
] as const);

export const ListLecturesRequestQuerySchema = z
	.object({
		skip: z.coerce.number().optional(),
		take: z.coerce.number().optional(),
		sort: SortSchema(lectureFields),
		filters: FilterSchemaLocal.optional()
	})
	.strict();
export type ListLecturesRequestQuery = z.infer<typeof ListLecturesRequestQuerySchema>;
export type ListLectures = z.infer<typeof ListLecturesRequestQuerySchema>;
