import { FilterSchema } from '@common/schemas';
import z from 'zod';

const reservationFields = ['dateStart', 'dateEnd'] as const;

const FilterSchemaLocal = FilterSchema(reservationFields, [
	'equals',
	'contains',
	'lt',
	'gt',
	'lte',
	'gte'
] as const);

export const ListReservationsRequestQuerySchema = z
	.object({
		filters: FilterSchemaLocal.optional(),
		studentId: z.uuid().optional(),
		lectureId: z.uuid().optional()
	})
	.strict();
export type ListReservationsRequestQuery = z.infer<typeof ListReservationsRequestQuerySchema>;
export type ListReservations = z.infer<typeof ListReservationsRequestQuerySchema>;
