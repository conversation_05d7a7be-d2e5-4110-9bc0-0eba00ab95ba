import { assertAuthenticated } from '@common/utils';
import { RequireAuth } from '@modules/auth/security/decorators/access.decorator';
import { StudentsService } from '@modules/students/students.service';
import {
	Body,
	Controller,
	Get,
	HttpException,
	HttpStatus,
	NotFoundException,
	Patch,
	Post,
	Req,
	Res,
	UnauthorizedException
} from '@nestjs/common';
import type { Request, Response } from 'express';
import { UuidParam, ZodBody, ZodQuery } from 'src/common/decorators/zodSchema.decorator';
import { created, ok } from 'src/common/utils/responses';

import {
	type CancelLectureRequestBody,
	CancelLectureRequestBodySchema,
	type CreateLectureRequestBody,
	CreateLectureRequestBodySchema,
	type ListLecturesRequestQuery,
	ListLecturesRequestQuerySchema,
	type ListReservationsRequestQuery,
	ListReservationsRequestQuerySchema,
	type PatchLectureRequestBody,
	PatchLectureRequestBodySchema
} from './schemas';
import {
	CancelLectureUseCase,
	CancelReservationUseCase,
	CreateLectureUseCase,
	CreateReservationUseCase,
	GetLectureUseCase,
	GetReservationUseCase,
	ListLecturesUseCase,
	ListReservationsUseCase,
	PatchLectureUseCase
} from './useCases';

@Controller('lectures')
export class LecturesController {
	constructor(
		private studentsService: StudentsService,
		private createLectureUs: CreateLectureUseCase,
		private patchLectureUc: PatchLectureUseCase,
		private cancelLectureUc: CancelLectureUseCase,
		private getLectureUc: GetLectureUseCase,
		private listLecturesUc: ListLecturesUseCase,
		private createReservationUc: CreateReservationUseCase,
		private cancelReservationUc: CancelReservationUseCase,
		private listReservationsUc: ListReservationsUseCase,
		private getReservationUc: GetReservationUseCase
	) {}

	@RequireAuth()
	@Get('reservation')
	async listReservations(
		@ZodQuery(ListReservationsRequestQuerySchema)
		{ filters, lectureId, studentId }: ListReservationsRequestQuery,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		let studentIdFilter = studentId;
		if (req.user.role === 'student') {
			if (studentId) {
				throw new HttpException('Students cannot filter by studentId', 400);
			}
			const student = await this.studentsService.getStudent({
				userId: req.user.sub
			});
			if (!student) {
				throw new NotFoundException(`Student with userId ${req.user.sub} not found`);
			}
			studentIdFilter = student.id;
		}

		const reservations = await this.listReservationsUc.execute({
			lectureId,
			filters,
			studentId: studentIdFilter
		});
		res.status(HttpStatus.OK).json(ok(reservations));
	}

	@RequireAuth()
	@Get('reservation/:reservationId')
	async getReservation(
		@UuidParam('reservationId') reservationId: string,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		const reservation = await this.getReservationUc.execute(reservationId);

		if (req.user.role === 'student' && reservation.student.userId !== req.user.sub) {
			throw new UnauthorizedException('Students can only access their own reservations');
		}

		res.status(HttpStatus.OK).json(ok(reservation));
	}

	@RequireAuth()
	@Post()
	async createLecture(
		@ZodBody(CreateLectureRequestBodySchema) dto: CreateLectureRequestBody,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		const lectures = await this.createLectureUs.execute({ ...dto, createdById: req.user.sub });
		res.status(HttpStatus.CREATED).json(created(lectures));
	}

	@RequireAuth()
	@Patch(':lectureId')
	async patchLecture(
		@UuidParam('lectureId') lectureId: string,
		@ZodBody(PatchLectureRequestBodySchema) dto: PatchLectureRequestBody,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		const updatedLectures = await this.patchLectureUc.execute({
			...dto,
			lectureId,
			updatedById: req.user.sub
		});
		res.status(HttpStatus.OK).json(ok(updatedLectures));
	}

	@RequireAuth()
	@Patch(':lectureId/cancel')
	async cancelLecture(
		@UuidParam('lectureId') lectureId: string,
		@ZodBody(CancelLectureRequestBodySchema) dto: CancelLectureRequestBody,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		const canceledLectures = await this.cancelLectureUc.execute({
			...dto,
			lectureId,
			canceledById: req.user.sub
		});
		res.status(HttpStatus.OK).json(ok(canceledLectures));
	}

	@RequireAuth()
	@Get(':lectureId')
	async getLecture(
		@UuidParam('lectureId') lectureId: string,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		const lecture = await this.getLectureUc.execute(lectureId);
		res.status(HttpStatus.OK).json(ok(lecture));
	}

	@RequireAuth()
	@Get()
	async listLectures(
		@Req() req: Request,
		@Res() res: Response,
		@ZodQuery(ListLecturesRequestQuerySchema) query: ListLecturesRequestQuery
	) {
		assertAuthenticated(req.user);

		const lectures = await this.listLecturesUc.execute(query);
		res.status(HttpStatus.OK).json(ok(lectures));
	}

	@RequireAuth()
	@Post(':lectureId/reservation')
	async createReservation(
		@UuidParam('lectureId') lectureId: string,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		await this.createReservationUc.execute({ lectureId, userId: req.user.sub });

		res.sendStatus(HttpStatus.CREATED);
	}

	@RequireAuth()
	@Patch(':lectureId/reservation/cancel')
	async cancelReservation(
		@UuidParam('lectureId') lectureId: string,
		@Req() req: Request,
		@Res() res: Response
	) {
		assertAuthenticated(req.user);

		await this.cancelReservationUc.execute({ lectureId, userId: req.user.sub });

		res.sendStatus(HttpStatus.NO_CONTENT);
	}
}
