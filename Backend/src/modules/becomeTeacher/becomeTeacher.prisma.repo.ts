import { Injectable } from '@nestjs/common';
import { BecomeTeacher, Prisma } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { BecomeTeacherRepo } from './becomeTeacher.repo';

@Injectable()
export class BecomeTeacherPrismaRepo implements BecomeTeacherRepo {
	constructor(private prisma: PrismaService) {}

	async findByEmail(email: string): Promise<BecomeTeacher | null> {
		return await this.prisma.becomeTeacher.findUnique({
			where: {
				email
			}
		});
	}

	async create(row: Prisma.BecomeTeacherUncheckedCreateInput): Promise<BecomeTeacher> {
		return await this.prisma.becomeTeacher.create({
			data: row
		});
	}
}
