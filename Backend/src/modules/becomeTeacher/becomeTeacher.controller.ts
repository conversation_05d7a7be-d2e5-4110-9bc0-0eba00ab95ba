import { <PERSON>, <PERSON>, Res } from '@nestjs/common';
import type { Response } from 'express';

import { ZodBody } from '../../common/decorators/zodSchema.decorator';
import { type BecomeTeacherInput, BecomeTeacherSchema } from '../../common/schemas/becomeTeacher';
import { created } from '../../common/utils/responses';
import { CreateBecomeTeacherUseCase } from './useCases/create.useCase';

@Controller('become-teacher')
export class BecomeTeacherController {
	constructor(private readonly createUc: CreateBecomeTeacherUseCase) {}

	@Post()
	async create(@ZodBody(BecomeTeacherSchema) dto: BecomeTeacherInput, @Res() res: Response) {
		const result = await this.createUc.execute(dto);
		return res.status(201).json(created(result));
	}
}
