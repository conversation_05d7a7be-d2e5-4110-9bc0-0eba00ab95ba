import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { BecomeTeacherController } from './becomeTeacher.controller';
import { BecomeTeacherPrismaRepo } from './becomeTeacher.prisma.repo';
import { BecomeTeacherRepo } from './becomeTeacher.repo';
import { CreateBecomeTeacherUseCase } from './useCases/create.useCase';

@Module({
	imports: [PrismaModule],
	controllers: [BecomeTeacherController],
	providers: [
		CreateBecomeTeacherUseCase,
		{ provide: BecomeTeacherRepo, useClass: BecomeTeacherPrismaRepo }
	],
	exports: [BecomeTeacherRepo]
})
export class BecomeTeacherModule {}
