import { Injectable } from '@nestjs/common';

import { type BecomeTeacherInput } from '../../../common/schemas/becomeTeacher';
import { BecomeTeacherRepo } from '../becomeTeacher.repo';

export type CreateBecomeTeacherInput = BecomeTeacherInput;
export type CreateBecomeTeacherOutput = { id: string };

@Injectable()
export class CreateBecomeTeacherUseCase {
	constructor(private repo: BecomeTeacherRepo) {}

	async execute(input: CreateBecomeTeacherInput): Promise<CreateBecomeTeacherOutput> {
		const name = input.name.trim();
		const school = input.school.trim();
		const phone = input.phone.trim();
		const email = input.email.trim().toLowerCase();
		const message = input.message.trim();

		const row = await this.repo.create({
			name,
			school,
			phone,
			email,
			message
		});
		return { id: row.id };
	}
}
