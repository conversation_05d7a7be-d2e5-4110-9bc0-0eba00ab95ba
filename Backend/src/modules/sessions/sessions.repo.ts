import { Prisma, Session } from '@prisma/client';

export abstract class SessionsRepo {
	abstract create(
		s: Prisma.SessionUncheckedCreateInput,
		tx?: Prisma.TransactionClient
	): Promise<void>;
	abstract revoke(sessionId: string): Promise<void>;
	abstract revokeAllByUserId(userId: string): Promise<void>;
	abstract touch(sessionId: string): Promise<void>;
	abstract get(sessionId: string): Promise<Session | null>;
	abstract findActiveByUserAndDid(userId: string, did: string): Promise<Session | null>;
	abstract reuse(sessionId: string, patch: { ip?: string; userAgent?: string }): Promise<void>;
}
