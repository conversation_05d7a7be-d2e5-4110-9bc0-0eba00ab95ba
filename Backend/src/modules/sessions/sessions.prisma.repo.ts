import { Injectable } from '@nestjs/common';
import { Prisma, Session } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { SessionsRepo } from './sessions.repo';

@Injectable()
export class SessionsPrismaRepo implements SessionsRepo {
	constructor(private prisma: PrismaService) {}

	async create(
		s: Prisma.SessionUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<void> {
		await tx.session.create({
			data: s
		});
	}

	async revoke(id: string): Promise<void> {
		await this.prisma.session.update({
			where: {
				id
			},
			data: {
				revokedAt: new Date()
			}
		});
	}

	async revokeAllByUserId(userId: string): Promise<void> {
		await this.prisma.session.updateMany({
			where: {
				userId
			},
			data: {
				revokedAt: new Date()
			}
		});
	}

	async touch(id: string): Promise<void> {
		await this.prisma.session.update({
			where: {
				id
			},
			data: {
				lastSeen: new Date()
			}
		});
	}

	async get(id: string): Promise<Session | null> {
		return await this.prisma.session.findUnique({
			where: {
				id
			}
		});
	}

	async findActiveByUserAndDid(userId: string, did: string): Promise<Session | null> {
		return await this.prisma.session.findFirst({
			where: {
				userId,
				did,
				revokedAt: null
			}
		});
	}

	async reuse(id: string, patch: { ip?: string; userAgent?: string }): Promise<void> {
		await this.prisma.session.updateMany({
			where: {
				id,
				revokedAt: null
			},
			data: {
				...patch,
				lastSeen: new Date()
			}
		});
	}
}
