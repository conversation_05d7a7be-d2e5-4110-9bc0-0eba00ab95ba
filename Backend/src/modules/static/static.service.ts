import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { promises as fs } from 'fs';
import path from 'path';

@Injectable()
export class StaticService {
	private readonly staticPath: string;

	constructor(private configService: ConfigService) {
		this.staticPath =
			this.configService.get<string>('STATIC_FILES_PATH') ??
			path.join(process.cwd(), 'static');
	}

	async getFileAsBuffer(filePath: string): Promise<Buffer> {
		const fullPath = this.resolveFilePath(filePath);
		await this.validateFileExists(fullPath);
		return fs.readFile(fullPath);
	}

	async fileExists(filePath: string): Promise<boolean> {
		try {
			const fullPath = this.resolveFilePath(filePath);
			await fs.access(fullPath);
			return true;
		} catch {
			return false;
		}
	}

	async getFileInfo(filePath: string): Promise<{
		exists: boolean;
		size?: number;
		extension?: string;
	}> {
		const fullPath = this.resolveFilePath(filePath);

		try {
			const stats = await fs.stat(fullPath);
			return {
				exists: true,
				size: stats.size,
				extension: path.extname(filePath)
			};
		} catch {
			return { exists: false };
		}
	}

	getStaticPath(): string {
		return this.staticPath;
	}

	private resolveFilePath(filePath: string): string {
		// Zabezpečení proti path traversal útokům
		const normalizedPath = path.normalize(filePath).replace(/^(\.\.[/\\])+/, '');
		return path.join(this.staticPath, normalizedPath);
	}

	private async validateFileExists(fullPath: string): Promise<void> {
		try {
			await fs.access(fullPath);
		} catch {
			throw new NotFoundException(
				`Soubor nebyl nalezen: ${path.relative(this.staticPath, fullPath)}`
			);
		}
	}
}
