import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

type Primitive = string | number | boolean;
type QueryValue = Primitive | Primitive[] | null | undefined;
export type QueryParams = Record<string, QueryValue>;

@Injectable()
export class LinkService {
	private readonly base: URL;

	constructor(private readonly config: ConfigService) {
		const raw = this.config.get<string>('FRONTEND_URL');
		if (!raw) {
			throw new Error('FRONTEND_URL is not defined');
		}
		try {
			this.base = new URL(raw);
		} catch {
			throw new Error(`FRONTEND_URL must be a valid absolute URL. Received: "${raw}"`);
		}
	}

	build(path = '/', params?: QueryParams): string {
		const url = new URL(path, this.base);

		if (params && Object.keys(params).length) {
			const merged = new URLSearchParams(url.search);
			for (const [key, value] of Object.entries(params)) {
				if (value === null || value === undefined) continue;

				if (Array.isArray(value)) {
					for (const item of value) merged.append(key, String(item));
				} else if (typeof value === 'object') {
					merged.set(key, JSON.stringify(value));
				} else {
					merged.set(key, String(value));
				}
			}
			url.search = merged.toString();
		}

		return url.toString();
	}

	getLink(params?: QueryParams): string {
		return this.build('/', params);
	}

	get baseUrl(): string {
		return this.base.toString();
	}
}
