import { Prisma, User } from '@prisma/client';

export abstract class UsersRepo {
	abstract findByEmail(email: string): Promise<User | null>;
	abstract getById(id: string): Promise<User | null>;
	abstract create(
		u: Prisma.UserUncheckedCreateInput,
		tx?: Prisma.TransactionClient
	): Promise<User>;
	abstract emailExists(email: string): Promise<boolean>;
	abstract changePassword(id: string, newPasswordHash: string): Promise<void>;
	abstract activateEmail(id: string): Promise<void>;
}
