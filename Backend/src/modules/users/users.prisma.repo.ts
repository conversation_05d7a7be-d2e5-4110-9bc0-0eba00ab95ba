import { Injectable } from '@nestjs/common';
import { Prisma, User } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { UsersRepo } from './users.repo';

@Injectable()
export class UsersPrismaRepo implements UsersRepo {
	constructor(private prisma: PrismaService) {}

	async findByEmail(email: string): Promise<User | null> {
		return await this.prisma.user.findUnique({
			where: {
				email
			}
		});
	}

	async getById(id: string): Promise<User | null> {
		return await this.prisma.user.findUnique({
			where: {
				id
			}
		});
	}

	async create(
		u: Prisma.UserUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<User> {
		return await tx.user.create({
			data: u
		});
	}

	async emailExists(email: string): Promise<boolean> {
		const user = await this.prisma.user.findUnique({
			where: {
				email
			},
			select: {
				id: true
			}
		});
		return !!user;
	}

	async changePassword(id: string, newPasswordHash: string): Promise<void> {
		await this.prisma.user.update({
			where: {
				id
			},
			data: {
				passwordHash: newPasswordHash,
				passwordChangedAt: new Date()
			}
		});
	}

	async activateEmail(id: string): Promise<void> {
		await this.prisma.user.update({
			where: {
				id
			},
			data: {
				emailVerifiedAt: new Date()
			}
		});
	}
}
