import { S2SAuthGuard } from '@common/guards/s2s.guard';
import { BadRequestException, Controller, Get, Req, UseGuards } from '@nestjs/common';
import type { Request } from 'express';

import { AbilityService } from '../ability/ability.service';
import { PermissionsService } from '../ability/permissions.service';
import { RequireAuth } from '../security/decorators/access.decorator';

@Controller('auth')
export class AbilityController {
	constructor(
		private readonly policy: PermissionsService,
		private readonly abilityService: AbilityService
	) {}

	@Get('permissions')
	@UseGuards(S2SAuthGuard)
	getAll() {
		return this.policy.getAll(); // { version, ruleset }
	}

	@RequireAuth()
	@Get('me/permissions')
	myPermissions(@Req() req: Request) {
		if (!req.user) {
			throw new BadRequestException('No user provided');
		}
		const ability = this.abilityService.forUser(req.user);
		return { version: this.policy.getVersion(), rules: ability.rules };
	}
}
