import {
	Body,
	Controller,
	Get,
	HttpCode,
	HttpStatus,
	Post,
	Req,
	Res,
	UseFilters
} from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';
import type { Request, Response } from 'express';

import { ZodBody } from '../../../common/decorators/zodSchema.decorator';
import { getCookieFromReq } from '../../../common/http/cookies';
import {
	ForgotPasswordSchema,
	type ForgotPasswordValues,
	type LoginInput,
	LoginSchema,
	RegisterSchema,
	type RegisterValues,
	ResetPasswordSchema,
	type ResetPasswordValues
} from '../../../common/schemas';
import { created, ok } from '../../../common/utils/responses';
import { AuthCookiesCleanupFilter } from '../filters/AuthCookiesCleanup.filter';
import { RequirePermissions } from '../security/decorators/access.decorator';
import { ForgotPasswordUseCase } from '../useCases/forgotPassword.useCase';
import { LoginUseCase } from '../useCases/login.useCase';
import { LogoutUseCase } from '../useCases/logout.useCase';
import { RefreshUseCase } from '../useCases/refresh.useCase';
import { RegisterUseCase } from '../useCases/register.useCase';
import { ResetPasswordUseCase } from '../useCases/resetPassword.useCase';

@Controller('auth')
export class AuthController {
	constructor(
		private registerUc: RegisterUseCase,
		private loginUc: LoginUseCase,
		private refreshUc: RefreshUseCase,
		private logoutUc: LogoutUseCase,
		private forgotPasswordUc: ForgotPasswordUseCase,
		private resetPasswordUc: ResetPasswordUseCase
	) {}

	@Post('register')
	@Throttle({ options: { limit: 3, ttl: 300 } })
	async register(
		@ZodBody(RegisterSchema) dto: RegisterValues,
		@Req() req: Request,
		@Res() res: Response
	) {
		const r = await this.registerUc.execute(dto, req);
		r.setCookies(res);
		return res.status(HttpStatus.CREATED).json(
			created({
				data: undefined
			})
		);
	}

	@Post('login')
	@Throttle({ options: { limit: 5, ttl: 60 } })
	async login(@ZodBody(LoginSchema) dto: LoginInput, @Req() req: Request, @Res() res: Response) {
		const r = await this.loginUc.execute(dto.email, dto.password, req);
		r.setCookies(res);
		return res.status(200).json(ok(undefined));
	}

	@UseFilters(AuthCookiesCleanupFilter)
	@Post('refresh')
	async refresh(@Req() req: Request, @Res() res: Response) {
		const rt = getCookieFromReq(req, 'rt');
		const r = await this.refreshUc.execute(rt);
		r.setCookies(res);
		return res.status(200).json(ok(undefined));
	}

	@Post('logout')
	async logout(@Req() req: Request, @Res() res: Response) {
		const at = getCookieFromReq(req, 'at');
		const r = await this.logoutUc.execute(at);
		r.removeCookies(res);
		return res.status(200).json(ok(undefined));
	}

	@Post('forgot-password')
	@Throttle({ options: { limit: 3, ttl: 300 } })
	async forgotPassword(
		@ZodBody(ForgotPasswordSchema) dto: ForgotPasswordValues,
		@Req() req: Request,
		@Res() res: Response
	) {
		await this.forgotPasswordUc.execute(dto.email, req);
		return res.status(200).json(ok(undefined));
	}

	@Post('reset-password')
	@Throttle({ options: { limit: 3, ttl: 300 } })
	@HttpCode(204)
	async reset(@ZodBody(ResetPasswordSchema) dto: ResetPasswordValues, @Res() res: Response) {
		await this.resetPasswordUc.viaToken(dto.v, dto.rpt, dto.newPassword);
		return res.status(200).json(ok(undefined));
	}

	@RequirePermissions(['read', 'User'])
	@Get('me')
	me(@Req() req: Request) {
		return {
			sub: req.user!.sub,
			sid: req.user!.sid
		};
	}
}
