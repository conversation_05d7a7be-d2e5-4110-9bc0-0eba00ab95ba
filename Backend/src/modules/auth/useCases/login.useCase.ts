import { Inject, Injectable } from '@nestjs/common';
import * as argon2 from 'argon2';
import { randomUUID } from 'crypto';
import type { Request, Response } from 'express';

import { InvalidCredentialsError } from '../../../common/errors';
import { getClientIp, getUserAgent } from '../../../common/http/client';
import { setCookies } from '../../../common/http/cookies';
import { RefreshTokensRepo } from '../../refreshTokens/refreshTokens.repo';
import { SessionsRepo } from '../../sessions/sessions.repo';
import { TokensService } from '../../tokens/tokens.service';
import { UsersRepo } from '../../users/users.repo';
import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from '../auth.config';

@Injectable()
export class LoginUseCase {
	constructor(
		private users: UsersRepo,
		private sessions: SessionsRepo,
		private tokens: TokensService,
		private rts: RefreshTokensRepo,
		@Inject(AUTH_MODULE_OPTIONS) private opts: Required<AuthModuleOptions>
	) {}

	async execute(email: string, password: string, req: Request) {
		const user = await this.users.findByEmail(email.trim().toLowerCase());
		if (!user) throw new InvalidCredentialsError();
		const ok = await argon2.verify(user.passwordHash, password);
		if (!ok) throw new InvalidCredentialsError();

		const ip = req ? getClientIp(req) : undefined;
		const ua = req ? getUserAgent(req)?.slice(0, 512) : undefined;

		const didCookie =
			typeof req?.cookies?.[this.opts.deviceIdCookieName] === 'string'
				? (req.cookies[this.opts.deviceIdCookieName] as string)
				: undefined;
		let did: string | undefined = typeof didCookie === 'string' ? didCookie : undefined;
		if (!did) {
			did = randomUUID();
		}

		const existing = await this.sessions.findActiveByUserAndDid(user.id, did);
		let sid: string;
		if (existing) {
			sid = existing.id;
			await this.rts.revokeAllBySession(sid);
			await this.sessions.reuse(sid, { ip, userAgent: ua });
		} else {
			sid = randomUUID();
			await this.sessions.create({
				id: sid,
				userId: user.id,
				did,
				ip,
				userAgent: ua,
				mfaLevel: 0
			});
		}

		const at = await this.tokens.signAT(
			{
				sub: user.id,
				sid,
				permsVer: user.permsVersion,
				role: user.role,
				firstname: user.firstName,
				lastname: user.lastName,
				email: user.email
			},
			this.opts.accessTokenTtlSec
		);
		const family = randomUUID();
		const rt = await this.tokens.signRT(
			{ sub: user.id, sid, family, rot: 0 },
			this.opts.refreshTokenTtlSec
		);

		await this.rts.insert({
			jti: rt.jti,
			userId: user.id,
			sessionId: sid,
			familyId: family,
			rot: 0,
			expiresAt: new Date(Date.now() + rt.maxAgeSec * 1000)
		});

		return {
			setCookies: (res: Response) => {
				setCookies(res, [
					{
						name: this.opts.refreshTokenCookieName,
						value: rt.token,
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenCookiePath,
							maxAge: rt.maxAgeSec
						}
					},
					{
						name: this.opts.refreshTokenPresentCookieName,
						value: '1',
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenPresentCookiePath,
							maxAge: rt.maxAgeSec,
							sameSite: 'lax'
						}
					},
					{
						name: this.opts.accessTokenCookieName,
						value: at.token,
						options: {
							httpOnly: true,
							path: this.opts.accessTokenCookiePath,
							maxAge: at.maxAgeSec,
							sameSite: 'lax'
						}
					},
					{
						name: this.opts.deviceIdCookieName,
						value: did,
						options: {
							httpOnly: true,
							path: this.opts.deviceIdCookiePath,
							maxAge: this.opts.deviceIdCookieTtlSec
						}
					}
				]);
			}
		};
	}
}
