import {
	Inject,
	Injectable,
	InternalServerErrorException,
	UnauthorizedException
} from '@nestjs/common';
import { Response } from 'express';

import { removeCookies } from '../../../common/http/cookies';
import { RefreshTokensRepo } from '../../refreshTokens/refreshTokens.repo';
import { SessionsRepo } from '../../sessions/sessions.repo';
import { TokensService } from '../../tokens/tokens.service';
import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from '../auth.config';

@Injectable()
export class LogoutUseCase {
	constructor(
		private tokens: TokensService,
		private sessions: SessionsRepo,
		private rts: RefreshTokensRepo,
		@Inject(AUTH_MODULE_OPTIONS) private opts: Required<AuthModuleOptions>
	) {}
	async execute(atCookie?: string) {
		if (!atCookie) throw new UnauthorizedException('No AT cookie provided');
		try {
			const { payload } = await this.tokens.verify(atCookie);
			await this.sessions.revoke(payload['sid'] as string);
			await this.rts.revokeBySID(payload['sid'] as string);
		} catch {
			throw new InternalServerErrorException();
		}
		return {
			removeCookies: (res: Response) => {
				removeCookies(res, [
					{
						name: this.opts.accessTokenCookieName,
						options: {
							httpOnly: true,
							path: this.opts.accessTokenCookiePath,
							sameSite: 'strict'
						}
					},
					{
						name: this.opts.refreshTokenPresentCookieName,
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenPresentCookiePath
						}
					},
					{
						name: this.opts.refreshTokenCookieName,
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenCookiePath,
							sameSite: 'strict'
						}
					}
				]);
			}
		};
	}
}
