import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import type { Response } from 'express';
import { TransactionService } from 'src/infra/prisma/transaction.service';

import { setCookies } from '../../../common/http/cookies';
import { RefreshTokensRepo } from '../../refreshTokens/refreshTokens.repo';
import { TokensService } from '../../tokens/tokens.service';
import { UsersRepo } from '../../users/users.repo';
import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from '../auth.config';

@Injectable()
export class RefreshUseCase {
	constructor(
		private tokens: TokensService,
		private rts: RefreshTokensRepo,
		private users: UsersRepo,
		private transaction: TransactionService,
		@Inject(AUTH_MODULE_OPTIONS) private opts: Required<AuthModuleOptions>
	) {}

	async execute(rtCookie?: string) {
		if (!rtCookie) throw new UnauthorizedException('No RT');
		const jwtResult = await this.tokens.verify(rtCookie).catch(() => null);

		if (!jwtResult) throw new UnauthorizedException('Token verification failed');
		const { payload } = jwtResult;
		const jti = payload['jti'] as string;
		const sid = payload['sid'] as string;
		const sub = payload['sub'] as string;
		const family = payload['family'] as string;

		const user = await this.users.getById(sub);
		if (!user) throw new UnauthorizedException('Unknown user');

		const { accessToken, newRt } = await this.transaction.run(async (tx) => {
			// Find the refresh token
			const refreshToken = await tx.refreshToken.findUnique({
				where: { jti }
			});

			if (!refreshToken || refreshToken.revokedAt || refreshToken.usedAt) {
				// Revoke all tokens in the family and the session due to token reuse detection
				await tx.refreshToken.updateMany({
					where: { familyId: family },
					data: { revokedAt: new Date() }
				});
				await tx.session.update({
					where: { id: sid },
					data: { revokedAt: new Date() }
				});
				throw new UnauthorizedException('RT reuse detected');
			}

			const at = await this.tokens.signAT(
				{
					sub,
					sid,
					permsVer: user.permsVersion,
					role: user.role,
					firstname: user.firstName,
					lastname: user.lastName,
					email: user.email
				},
				this.opts.accessTokenTtlSec
			);
			const fresh = await this.tokens.signRT(
				{ sub, sid, family, rot: (refreshToken.rot ?? 0) + 1 },
				this.opts.refreshTokenTtlSec
			);

			// Mark the current refresh token as used and set its replacement
			await tx.refreshToken.updateMany({
				where: {
					jti: jti,
					usedAt: null
				},
				data: {
					usedAt: new Date(),
					replacedBy: fresh.jti
				}
			});

			// Create the new refresh token
			await tx.refreshToken.create({
				data: {
					jti: fresh.jti,
					userId: user.id,
					sessionId: sid,
					familyId: family,
					rot: (refreshToken.rot ?? 0) + 1,
					expiresAt: new Date(Date.now() + fresh.maxAgeSec * 1000)
				}
			});

			return { accessToken: at, newRt: fresh };
		});

		await this.rts.gcOld(family);

		return {
			setCookies: (res: Response) => {
				setCookies(res, [
					{
						name: this.opts.refreshTokenCookieName,
						value: newRt.token,
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenCookiePath,
							maxAge: newRt.maxAgeSec
						}
					},
					{
						name: this.opts.refreshTokenPresentCookieName,
						value: '1',
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenPresentCookiePath,
							maxAge: newRt.maxAgeSec,
							sameSite: 'lax'
						}
					},
					{
						name: this.opts.accessTokenCookieName,
						value: accessToken.token,
						options: {
							httpOnly: true,
							path: this.opts.accessTokenCookiePath,
							maxAge: accessToken.maxAgeSec,
							sameSite: 'lax'
						}
					}
				]);
			}
		};
	}
}
