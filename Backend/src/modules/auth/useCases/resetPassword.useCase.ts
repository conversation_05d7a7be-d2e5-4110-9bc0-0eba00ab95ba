import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import * as argon2 from 'argon2';

import { verifyToken } from '../../../common/crypto/tokens';
import { LinkService } from '../../link/link.service';
import { MailerService } from '../../mailer/mailer.service';
import { PasswordResetTokensRepo } from '../../passwordResetTokens/passwordResetTokens.repo';
import { RefreshTokensRepo } from '../../refreshTokens/refreshTokens.repo';
import { SessionsRepo } from '../../sessions/sessions.repo';
import { UsersRepo } from '../../users/users.repo';

@Injectable()
export class ResetPasswordUseCase {
	constructor(
		private users: UsersRepo,
		private sessions: SessionsRepo,
		private rts: RefreshTokensRepo,
		private prt: PasswordResetTokensRepo,
		private mailer: MailerService,
		private link: LinkService
	) {}

	// 1) Token flow (z e-mailu)
	async viaToken(verificationId: string, token: string, newPassword: string) {
		if (!verificationId || !token) throw new BadRequestException('Missing parameters');

		const row = await this.prt.findActiveById(verificationId);
		if (!row) throw new BadRequestException();

		const ok = verifyToken(token, row.tokenHash);
		if (!ok) throw new BadRequestException();

		const user = await this.users.getById(row.userId);
		if (!user) throw new UnauthorizedException();

		const passwordHash = await argon2.hash(newPassword, {
			type: argon2.argon2id,
			timeCost: 3,
			memoryCost: 65_536,
			parallelism: 1
		});

		await this.users.changePassword(user.id, passwordHash);

		await this.prt.markUsed(row.id);
		await this.prt.revokeActiveByUser(user.id);

		await this.sessions.revokeAllByUserId(user.id);
		await this.rts.revokeAllByUserId(user.id);

		await this.mailer.sendTemplate({ email: user.email }, 'password.resetSuccess', 'cs', {
			loginUrl: this.link.build('/prihlaseni')
		});
	}
}
