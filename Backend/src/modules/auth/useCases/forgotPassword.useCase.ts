import { createEmailToken } from '@common/crypto/tokens';
import { getClientIp, getUserAgent } from '@common/http/client';
import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from '@modules/auth/auth.config';
import { LinkService } from '@modules/link/link.service';
import { MailerService } from '@modules/mailer/mailer.service';
import { PasswordResetTokensRepo } from '@modules/passwordResetTokens/passwordResetTokens.repo';
import { UsersRepo } from '@modules/users/users.repo';
import { Inject, Injectable } from '@nestjs/common';
import type { Request } from 'express';

@Injectable()
export class ForgotPasswordUseCase {
	constructor(
		private prt: PasswordResetTokensRepo,
		private users: UsersRepo,
		private mailer: MailerService,
		private readonly links: LinkService,
		@Inject(AUTH_MODULE_OPTIONS) private opts: Required<AuthModuleOptions>
	) {}

	async execute(email: string, req?: Request) {
		const norm = email.trim().toLowerCase();
		const user = await this.users.findByEmail(norm);

		if (user) {
			await this.prt.revokeActiveByUser(user.id);

			const { token, tokenHash, expiresAt } = createEmailToken(
				this.opts.passwordResetTtlHours
			);
			await this.prt.revokeActiveByUser(user.id);
			const { id: verificationId } = await this.prt.create({
				userId: user.id,
				tokenHash,
				expiresAt,
				requestIp: req ? getClientIp(req) : undefined,
				userAgent: req ? getUserAgent(req)?.slice(0, 512) : undefined
			});

			const path = this.opts.passwordResetPath;
			const link = this.links.build(path, { v: verificationId, rpt: token });

			await this.mailer.sendTemplate({ email: user.email }, 'password.forgot', 'cs', {
				resetUrl: link
			});
		}
		return null;
	}
}
