import { ActionEnum, type RolePermissions, SubjectEnum } from './types';

const adminPermissions: RolePermissions = {
	[SubjectEnum.All]: {
		[ActionEnum.Manage]: true
	}
};

const managerPermissions: RolePermissions = {
	[SubjectEnum.Student]: {
		[ActionEnum.Manage]: true
	},
	[SubjectEnum.Teacher]: {
		[ActionEnum.Read]: true
	},
	[SubjectEnum.Location]: {
		[ActionEnum.Manage]: true
	}
};

const teacherPermissions: RolePermissions = {
	[SubjectEnum.Student]: {
		[ActionEnum.Read]: true
	}
};

const studentPermissions: RolePermissions = {};

export const PERMISSIONS = {
	admin: { ...adminPermissions },
	manager: { ...managerPermissions },
	student: { ...studentPermissions },
	teacher: { ...teacherPermissions }
};
