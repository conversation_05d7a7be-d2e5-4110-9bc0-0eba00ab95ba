import { createMongoAbility, RawRuleOf } from '@casl/ability';
import { Injectable } from '@nestjs/common';

import { ATClaims } from '@/types/user';

import { PermissionsService } from './permissions.service';
import type { Action, AppAbility, AppTuple, RolePermissions } from './types';

@Injectable()
export class AbilityService {
	private cache = new Map<string, AppAbility>();

	constructor(private readonly policy: PermissionsService) {}

	forUser(jwt: ATClaims): AppAbility {
		const version = this.policy.getVersion?.() ?? '0';
		const key = `${jwt.role}:${version}`;

		const cached = this.cache.get(key);
		if (cached) return cached;

		const perms: RolePermissions = this.policy.getRulesFor(jwt.role);
		const rules = this.buildRules(perms);

		const ability = createMongoAbility<AppTuple>(rules) as AppAbility;
		this.cache.set(key, ability);
		return ability;
	}

	buildRules(perms: RolePermissions): RawRuleOf<AppAbility>[] {
		const rules: RawRuleOf<AppAbility>[] = [];

		for (const [subject, actions] of Object.entries(perms)) {
			if (!actions) continue;

			for (const [action, allowed] of Object.entries(actions) as [Action, boolean][]) {
				if (allowed) {
					rules.push({ action, subject } as RawRuleOf<AppAbility>);
				}
			}
		}

		return rules;
	}
}
