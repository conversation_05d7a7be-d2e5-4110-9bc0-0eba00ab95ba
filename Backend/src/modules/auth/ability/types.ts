import type { MongoAbility } from '@casl/ability';

import type { UserRole } from '@/types/user';

export type AppTuple = [Action, Subject];
export type AppAbility = MongoAbility<AppTuple>;

export type RolePermissions = Partial<Record<Subject, Partial<Record<Action, boolean>>>>;
export type Ruleset = Record<UserRole, RolePermissions>;

export type Permission = readonly [Action, Subject];

export type PolicyCallback = (ability: AppAbility) => boolean;

export enum ActionEnum {
	Manage = 'manage', // CRUD
	Create = 'create', // C
	Read = 'read', // R
	Update = 'update', // U
	Delete = 'delete' // D
}

export type Action = `${ActionEnum}`;

export enum SubjectEnum {
	All = 'all',
	User = 'User', // All user entities -> student, teacher, manager, admin
	Location = 'Location', // Location entity
	Lecture = 'Lecture', // Lecture entity
	Student = 'Student', // Student user entity
	Teacher = 'Teacher', // Teacher user entity
	Manager = 'Manager', // Manager user entity
	Admin = 'Admin' // Admin user entity,
}

export type Subject = `${SubjectEnum}`;
