import { Injectable } from '@nestjs/common';
import { Roles } from '@prisma/client';
import { createHash } from 'crypto';

import { PERMISSIONS } from './permissions';
import { RolePermissions, Ruleset } from './types';

@Injectable()
export class PermissionsService {
	private readonly ruleset = PERMISSIONS;
	private readonly version = createHash('sha1')
		.update(JSON.stringify(PERMISSIONS))
		.digest('hex')
		.slice(0, 12);

	getVersion() {
		return this.version;
	}

	getAll(): { version: string; ruleset: Ruleset } {
		return { version: this.version, ruleset: this.ruleset };
	}

	getRulesFor(role: Roles): RolePermissions {
		return this.ruleset[role] ?? {};
	}
}
