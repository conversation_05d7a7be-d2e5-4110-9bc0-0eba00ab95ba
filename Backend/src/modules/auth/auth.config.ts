export type AuthModuleOptions = {
	accessTokenTtlSec?: number; // default 600s
	refreshTokenTtlSec?: number; // default 60d
	deviceIdCookieName?: string; // default 'did'

	accessTokenCookieName?: string; // default 'at'
	accessTokenCookiePath?: string; // default '/'

	refreshTokenPresentCookieName?: string; // default 'rt'
	refreshTokenPresentCookiePath?: string; // default '/'

	refreshTokenCookieName?: string; // default 'rt'
	refreshTokenCookiePath?: string; // default '/api/auth/refresh'
	deviceIdCookiePath?: string; // default '/'
	deviceIdCookieTtlSec?: number; // default 10 years
	passwordResetTtlHours?: number; // default 24
	emailVerificationTtlHours?: number; // default 48
	emailVerifyBaseUrl?: string; // e.g., /verify
	passwordResetPath?: string; // e.g., /reset
};

export const AUTH_MODULE_OPTIONS = 'AUTH_MODULE_OPTIONS';

export const defaultAuthOptions: Required<AuthModuleOptions> = {
	accessTokenTtlSec: 600,
	refreshTokenTtlSec: 60 * 24 * 60 * 60,
	deviceIdCookieName: 'did',
	accessTokenCookieName: 'at',
	refreshTokenCookieName: 'rt',
	refreshTokenPresentCookieName: 'rt_present',
	refreshTokenPresentCookiePath: '/',
	accessTokenCookiePath: '/',
	refreshTokenCookiePath: '/api/auth/refresh',
	deviceIdCookiePath: '/',
	deviceIdCookieTtlSec: 60 * 60 * 24 * 365 * 10,
	passwordResetTtlHours: 24,
	emailVerificationTtlHours: 48,
	emailVerifyBaseUrl: '/verify',
	passwordResetPath: '/reset'
};
