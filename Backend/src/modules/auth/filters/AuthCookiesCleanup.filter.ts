import {
	ArgumentsHost,
	Catch,
	ExceptionFilter,
	Inject,
	UnauthorizedException
} from '@nestjs/common';
import type { Response } from 'express';

import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from '../auth.config';

@Catch(UnauthorizedException)
export class AuthCookiesCleanupFilter implements ExceptionFilter {
	constructor(@Inject(AUTH_MODULE_OPTIONS) private opts: Required<AuthModuleOptions>) {}

	catch(exception: UnauthorizedException, host: ArgumentsHost) {
		const res = host.switchToHttp().getResponse<Response>();

		console.log('REMOVING COOKIES');
		res.clearCookie(this.opts.refreshTokenCookieName, {
			path: this.opts.refreshTokenCookiePath,
			httpOnly: true,
			sameSite: 'strict',
			secure: true
		});
		res.clearCookie(this.opts.refreshTokenPresentCookieName, {
			path: this.opts.refreshTokenPresentCookiePath,
			httpOnly: false,
			sameSite: 'lax',
			secure: true
		});
		res.clearCookie(this.opts.accessTokenCookieName, {
			path: this.opts.accessTokenCookiePath,
			httpOnly: true,
			sameSite: 'lax',
			secure: true
		});

		throw exception;
	}
}
