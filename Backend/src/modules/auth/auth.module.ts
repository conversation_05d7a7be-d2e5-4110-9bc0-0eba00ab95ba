import { AbilityModule } from '@modules/auth/ability/ability.module';
import {
	AUTH_MODULE_OPTIONS,
	AuthModuleOptions,
	defaultAuthOptions
} from '@modules/auth/auth.config';
import { AbilityController } from '@modules/auth/controllers/ability.controller';
import { AuthController } from '@modules/auth/controllers/auth.controller';
import { ForgotPasswordUseCase } from '@modules/auth/useCases/forgotPassword.useCase';
import { LoginUseCase } from '@modules/auth/useCases/login.useCase';
import { LogoutUseCase } from '@modules/auth/useCases/logout.useCase';
import { RefreshUseCase } from '@modules/auth/useCases/refresh.useCase';
import { RegisterUseCase } from '@modules/auth/useCases/register.useCase';
import { ResetPasswordUseCase } from '@modules/auth/useCases/resetPassword.useCase';
import { EmailVerificationModule } from '@modules/emailVerification/emailVerification.module';
import { EmailVerificationTokensService } from '@modules/emailVerification/emailVerificationTokens.service';
import { MailerModule } from '@modules/mailer/mailer.module';
import { PasswordResetTokensModule } from '@modules/passwordResetTokens/passwordResetTokens.module';
import { RefreshTokensModule } from '@modules/refreshTokens/refreshTokens.module';
import { SessionsModule } from '@modules/sessions/sessions.module';
import { StudentsModule } from '@modules/students/students.module';
import { TokensModule } from '@modules/tokens/tokens.module';
import { UsersModule } from '@modules/users/users.module';
import { DynamicModule, Module } from '@nestjs/common';

import { PrismaModule } from '@/infra/prisma/prisma.module';

@Module({
	imports: [
		UsersModule,
		StudentsModule,
		PrismaModule,
		SessionsModule,
		RefreshTokensModule,
		TokensModule,
		PasswordResetTokensModule,
		MailerModule,
		EmailVerificationModule,
		AbilityModule
	],
	controllers: [AuthController, AbilityController],
	providers: [
		RegisterUseCase,
		LoginUseCase,
		RefreshUseCase,
		LogoutUseCase,
		ForgotPasswordUseCase,
		ResetPasswordUseCase,
		EmailVerificationTokensService
	]
})
export class AuthModule {
	static forRoot(options: AuthModuleOptions = {}): DynamicModule {
		return {
			module: AuthModule,
			providers: [
				{
					provide: AUTH_MODULE_OPTIONS,
					useValue: { ...defaultAuthOptions, ...options }
				}
			]
		};
	}
}
