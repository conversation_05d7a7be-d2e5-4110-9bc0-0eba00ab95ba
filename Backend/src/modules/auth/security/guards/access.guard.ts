import {
	CanActivate,
	ExecutionContext,
	ForbiddenException,
	Injectable,
	UnauthorizedException
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import type { Request } from 'express';

import { ATClaims } from '@/types/user';

import { TokensService } from '../../../tokens/tokens.service';
import { AbilityService } from '../../ability/ability.service';
import type { AppAbility, Permission, PolicyCallback } from '../../ability/types';
import {
	AUTH_KEY,
	PERMISSIONS_ALL_KEY,
	PERMISSIONS_ANY_KEY,
	POLICIES_CB_KEY
} from '../decorators/access.decorator';

type AugmentedRequest = Request & {
	user?: ATClaims;
	ability?: AppAbility;
};

@Injectable()
export class AccessGuard implements CanActivate {
	constructor(
		private readonly reflector: Reflector,
		private readonly tokens: TokensService,
		private readonly abilities: AbilityService
	) {}

	async canActivate(ctx: ExecutionContext): Promise<boolean> {
		const req = ctx.switchToHttp().getRequest<AugmentedRequest>();
		const { authOnly, permsAll, permsAny, policies } = this.getRequirements(ctx);

		//public
		if (!authOnly && permsAll.length === 0 && permsAny.length === 0 && policies.length === 0) {
			return true;
		}

		const token = this.extractToken(req);
		if (!token) throw new UnauthorizedException('Invalid token');

		let user: ATClaims;
		try {
			const { payload } = await this.tokens.verify<ATClaims>(token);
			const { sub, sid, permsVer, role, acr, amr, firstname, lastname, email } =
				payload || {};
			if (!sub || !sid) throw new UnauthorizedException('Invalid token');
			user = { sub, sid, permsVer, role, acr, amr, firstname, lastname, email };
		} catch {
			throw new UnauthorizedException('Invalid token');
		}

		req.user = user;

		const ability = this.abilities.forUser(user);
		req.ability = ability;

		if (authOnly && permsAll.length === 0 && permsAny.length === 0 && policies.length === 0) {
			return true;
		}

		if (!this.satisfiesAll(ability, permsAll)) {
			throw new ForbiddenException('Insufficient permissions');
		}
		if (!this.satisfiesAny(ability, permsAny)) {
			throw new ForbiddenException('Insufficient permissions');
		}
		if (!this.satisfiesPolicies(ability, policies)) {
			throw new ForbiddenException('Insufficient permissions');
		}

		return true;
	}

	private getRequirements(ctx: ExecutionContext) {
		const authOnly =
			this.reflector.getAllAndOverride<boolean>(AUTH_KEY, [
				ctx.getHandler(),
				ctx.getClass()
			]) ?? false;

		const permsAll =
			this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_ALL_KEY, [
				ctx.getHandler(),
				ctx.getClass()
			]) ?? [];

		const permsAny =
			this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_ANY_KEY, [
				ctx.getHandler(),
				ctx.getClass()
			]) ?? [];

		const policies =
			this.reflector.getAllAndOverride<PolicyCallback[]>(POLICIES_CB_KEY, [
				ctx.getHandler(),
				ctx.getClass()
			]) ?? [];

		return { authOnly, permsAll, permsAny, policies };
	}
	private extractToken(req: Request): string | null {
		const cookies = req.cookies;
		if (cookies && typeof cookies === 'object' && typeof cookies.at === 'string') {
			return cookies.at;
		}
		return null;
	}

	private satisfiesAll(ability: AppAbility, permsAll: Permission[]): boolean {
		return permsAll.length === 0 || permsAll.every(([a, s]) => ability.can(a, s));
	}

	private satisfiesAny(ability: AppAbility, permsAny: Permission[]): boolean {
		return permsAny.length === 0 || permsAny.some(([a, s]) => ability.can(a, s));
	}

	private satisfiesPolicies(ability: AppAbility, policies: PolicyCallback[]): boolean {
		return policies.length === 0 || policies.every((cb) => cb(ability));
	}
}
