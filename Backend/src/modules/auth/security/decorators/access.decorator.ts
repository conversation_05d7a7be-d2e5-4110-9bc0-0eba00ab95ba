import { SetMetadata } from '@nestjs/common';

import { Permission, PolicyCallback } from '../../ability/types';

export const AUTH_KEY = 'access:auth';
export const PERMISSIONS_ALL_KEY = 'access:permsAll'; // AND
export const PERMISSIONS_ANY_KEY = 'access:permsAny'; // OR
export const POLICIES_CB_KEY = 'access:policiesCb'; // callback

export const RequireAuth = () => SetMetadata(AUTH_KEY, true);

export const RequirePermissions = (...perms: Permission[]) =>
	SetMetadata(PERMISSIONS_ALL_KEY, perms);

export const RequirePermissionsAny = (...perms: Permission[]) =>
	SetMetadata(PERMISSIONS_ANY_KEY, perms);

export const CheckPolicies = (...cbs: PolicyCallback[]) => SetMetadata(POLICIES_CB_KEY, cbs);
