import { FilterSchema, SortSchema } from 'src/common/schemas';
import z from 'zod';

const teacherFields = [
	'firstName',
	'lastName',
	'email',
	'phone',
	'description',
	'wageTax'
] as const;

const FilterSchemaLocal = FilterSchema(teacherFields, [
	'equals',
	'contains',
	'lt',
	'gt',
	'lte',
	'gte'
] as const);

export const ListTeachersRequestBodySchema = z
	.object({
		skip: z.coerce.number().optional(),
		take: z.coerce.number().optional(),
		sort: SortSchema(teacherFields),
		filters: FilterSchemaLocal.optional()
	})
	.strict();
export type ListTeachersRequestBody = z.infer<typeof ListTeachersRequestBodySchema>;
