import { Body, Controller, Delete, Get, HttpStatus, Patch, Post, Res } from '@nestjs/common';
import type { Request, Response } from 'express';
import { UuidParam, ZodBody, ZodQuery } from 'src/common/decorators/zodSchema.decorator';
import { created, ok } from 'src/common/utils/responses';

import {
	type CreateTeacherRequestBody,
	CreateTeacherRequestBodySchema,
	type ListTeachersRequestBody,
	ListTeachersRequestBodySchema
} from '../schemas';
import { CreateTeacherUseCase, GetTeacherUseCase, ListTeachersUseCase } from '../useCases';

@Controller('teachers')
export class TeachersController {
	constructor(
		private listTeachersUc: ListTeachersUseCase,
		private createTeacherUc: CreateTeacherUseCase,
		private getTeacherUc: GetTeacherUseCase
	) {}

	@Get()
	async listTeachers(
		@ZodQuery(ListTeachersRequestBodySchema) dto: ListTeachersRequestBody,
		@Res() res: Response
	) {
		const teachers = await this.listTeachersUc.execute(dto);
		const teachersTransformed = teachers.map(
			({ user: { id: _id, passwordHash: _passwordHash, ...user }, ...teacher }) => ({
				...teacher,
				...user
			})
		);
		return res.status(HttpStatus.OK).json(ok(teachersTransformed));
	}

	@Get(':teacherId')
	async getTeacher(@UuidParam('teacherId') teacherId: string, @Res() res: Response) {
		const teacher = await this.getTeacherUc.execute(teacherId);
		return res.status(HttpStatus.OK).json(ok(teacher));
	}

	@Post()
	async createTeacher(
		@ZodBody(CreateTeacherRequestBodySchema) dto: CreateTeacherRequestBody,
		@Res() res: Response
	) {
		const createdTeacher = await this.createTeacherUc.execute(dto);

		res.status(HttpStatus.CREATED).json(created(createdTeacher));
	}

	@Patch(':teacherId')
	patchTeacher(
		@UuidParam('teacherId') teacherId: string,
		@Body() teacherData: unknown,
		@Res() res: Response
	) {
		res.sendStatus(501);
	}

	@Delete(':teacherId')
	deleteTeacher(@UuidParam('teacherId') teacherId: string, @Res() res: Response) {
		res.sendStatus(501);
	}
}
