import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, Teacher, User } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { TeachersRepo } from './teachers.repo';

@Injectable()
export class TeachersPrismaRepo implements TeachersRepo {
	constructor(private prisma: PrismaService) {}

	async list({
		include,
		...args
	}: Prisma.TeacherFindManyArgs): Promise<(Teacher & { user: User })[]> {
		return await this.prisma.teacher.findMany({
			...args,
			include: {
				user: true,
				...(include ?? {})
			}
		});
	}

	async getById(teacherId: string): Promise<Teacher | null> {
		return await this.prisma.teacher.findUnique({
			where: {
				id: teacherId
			}
		});
	}

	async getByUserId(userId: string): Promise<Teacher | null> {
		return await this.prisma.teacher.findFirst({
			where: {
				userId
			}
		});
	}

	async create(
		newTeacher: Prisma.TeacherUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<Teacher> {
		return await tx.teacher.create({
			data: newTeacher
		});
	}

	async delete(teacherId: string): Promise<Teacher | null> {
		try {
			return await this.prisma.teacher.delete({
				where: {
					id: teacherId
				}
			});
		} catch {
			// If the teacher doesn't exist, return null
			return null;
		}
	}

	async patch({
		id: teacherId,
		...data
	}: Partial<Teacher> & Pick<Teacher, 'id'>): Promise<Teacher> {
		return await this.prisma.teacher.update({
			where: {
				id: teacherId
			},
			data
		});
	}
}
