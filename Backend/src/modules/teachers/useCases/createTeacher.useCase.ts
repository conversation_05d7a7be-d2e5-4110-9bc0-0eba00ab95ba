import { Injectable } from '@nestjs/common';
import { TransactionService } from 'src/infra/prisma/transaction.service';
import { UsersRepo } from 'src/modules/users/users.repo';

import { CreateTeacherRequestBody } from '../schemas';
import { TeachersRepo } from '../teachers.repo';

@Injectable()
export class CreateTeacherUseCase {
	constructor(
		private teachers: TeachersRepo,
		private users: UsersRepo,
		private transaction: TransactionService
	) {}
	async execute({
		email,
		firstName,
		lastName,
		phone,
		description,
		wageTax
	}: CreateTeacherRequestBody) {
		const createdTeacher = await this.transaction.run(async (tx) => {
			const createdUser = await this.users.create(
				{
					email,
					firstName,
					lastName,
					//TODO: send email to set password
					passwordHash: '',
					role: 'teacher'
				},
				tx
			);

			const createdTeacher = await this.teachers.create(
				{
					userId: createdUser.id,
					phone,
					description,
					wageTax
				},
				tx
			);

			return createdTeacher;
		});

		return createdTeacher;
	}
}
