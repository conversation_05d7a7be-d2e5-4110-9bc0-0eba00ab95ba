import { Prisma } from '@prisma/client';
import { getSort } from 'src/common/utils';

import { ListTeachersRequestBody } from '../../schemas/listTeachers';

export const transformSorting = (
	sort: NonNullable<ListTeachersRequestBody['sort']>
): Prisma.TeacherOrderByWithRelationInput => {
	const teacherSortInput: Prisma.TeacherOrderByWithRelationInput = {
		phone: getSort(sort, 'phone'),
		description: getSort(sort, 'description'),
		wageTax: getSort(sort, 'wageTax')
	};
	const userSortInput: Prisma.UserOrderByWithRelationInput = {
		email: getSort(sort, 'email'),
		firstName: getSort(sort, 'firstName'),
		lastName: getSort(sort, 'lastName')
	};

	const someTeacherSort = Object.values(teacherSortInput).some((v) => v !== undefined);
	const someUserSort = Object.values(userSortInput).some((v) => v !== undefined);
	return {
		...(someTeacherSort ? teacherSortInput : {}),
		...(someUserSort ? { user: userSortInput } : {})
	};
};
