import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { ListTeachersRequestBody } from '../../schemas/listTeachers';
import { TeachersRepo } from '../../teachers.repo';
import { transformFiltering } from './transformFiltering';
import { transformSorting } from './transformSorting';

@Injectable()
export class ListTeachersUseCase {
	constructor(private teachers: TeachersRepo) {}
	async execute({ skip, filters, sort, take }: ListTeachersRequestBody) {
		const findArgs: Prisma.TeacherFindManyArgs = {};

		if (sort) {
			findArgs.orderBy = transformSorting(sort);
		}

		if (filters) {
			findArgs.where = transformFiltering(filters);
		}

		return this.teachers.list({
			skip,
			take,
			...findArgs
		});
	}
}
