import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { UsersModule } from '../users/users.module';
import { TeachersController } from './controllers/teachers.controller';
import { TeachersPrismaRepo } from './teachers.prisma.repo';
import { TeachersRepo } from './teachers.repo';
import { CreateTeacherUseCase, GetTeacherUseCase, ListTeachersUseCase } from './useCases';

@Module({
	imports: [UsersModule, PrismaModule],
	providers: [
		{ provide: TeachersRepo, useClass: TeachersPrismaRepo },
		ListTeachersUseCase,
		CreateTeacherUseCase,
		GetTeacherUseCase
	],
	controllers: [TeachersController],
	exports: [TeachersRepo]
})
export class TeachersModule {}
