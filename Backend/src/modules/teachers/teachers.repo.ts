import { <PERSON><PERSON><PERSON>, Teacher, User } from '@prisma/client';

export abstract class TeachersRepo {
	abstract getById(teacherId: string): Promise<Teacher | null>;
	abstract getByUserId(userId: string): Promise<Teacher | null>;
	abstract create(
		u: Prisma.TeacherUncheckedCreateInput,
		tx?: Prisma.TransactionClient
	): Promise<Teacher>;
	abstract patch(u: Partial<Teacher> & Pick<Teacher, 'id'>): Promise<Teacher>;
	abstract delete(teacherId: string): Promise<Teacher | null>;
	abstract list(args?: Prisma.TeacherFindManyArgs): Promise<
		(Teacher & {
			user: User;
		})[]
	>;
}
