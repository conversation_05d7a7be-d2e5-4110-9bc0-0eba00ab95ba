import { LocationsRepo } from '@modules/locations/locations.repo';
import type { ListLocationsRequestBody } from '@modules/locations/schemas/listLocations';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class ListLocationsUseCase {
	constructor(private locations: LocationsRepo) {}

	async execute({ sort, filters, skip, take }: ListLocationsRequestBody) {
		const findArgs: Prisma.LocationFindManyArgs = {};

		//TODO: filters and sorting

		if (sort) {
			//findArgs.orderBy = transformSorting(sort);
		}

		if (filters) {
			//findArgs.where = transformFiltering(filters);
		}
		return this.locations.list({
			skip,
			take,
			include: {
				managers: true,
				teachers: true
			},
			...findArgs
		});
	}
}
