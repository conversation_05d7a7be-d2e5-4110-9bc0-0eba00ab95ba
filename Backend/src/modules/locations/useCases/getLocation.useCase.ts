import { LocationsRepo } from '@modules/locations/locations.repo';
import { Injectable, NotFoundException } from '@nestjs/common';

@Injectable()
export class GetLocationUseCase {
	constructor(private locations: LocationsRepo) {}

	async execute(locationId: string) {
		const location = await this.locations.getById(locationId);
		if (!location) throw new NotFoundException(`Location with id ${locationId} not found`);

		return location;
	}
}
