import { LocationsRepo } from '@modules/locations/locations.repo';
import { StorageService } from '@modules/storage/storage.service';
import { Injectable } from '@nestjs/common';
import { Location } from '@prisma/client';

@Injectable()
export class RemoveLocationImgUseCase {
	constructor(
		private locations: LocationsRepo,
		private storage: StorageService
	) {}

	async execute(locationId: string): Promise<Location> {
		const location = await this.locations.getById(locationId);
		if (!location) {
			throw new Error('Location not found');
		}

		if (location.imgKey != null) {
			try {
				await this.storage.deleteObject('locations/' + location.imgKey);
			} catch (error) {
				console.warn(`Failed to delete image: ${location.imgKey}`, error);
			}
		}

		return await this.locations.patch({
			id: locationId,
			imgKey: null
		});
	}
}
