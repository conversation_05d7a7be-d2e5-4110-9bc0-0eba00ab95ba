import { LocationsRepo } from '@modules/locations/locations.repo';
import { StorageService } from '@modules/storage/storage.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Location } from '@prisma/client';

@Injectable()
export class SetLocationImgUseCase {
	constructor(
		private locations: LocationsRepo,
		private storage: StorageService
	) {}

	async execute(locationId: string, newFileName: string): Promise<Location> {
		const location = await this.locations.getById(locationId);
		if (!location) {
			try {
				await this.storage.deleteObject('locations/' + newFileName);
			} catch (error) {
				console.warn(`Failed to delete new image: ${newFileName}`, error);
			}

			throw new NotFoundException('Location not found');
		}

		const oldFileName = location.imgKey;

		const updatedLocation = await this.locations.patch({
			id: locationId,
			imgKey: newFileName
		});

		if (oldFileName != null) {
			try {
				await this.storage.deleteObject('locations/' + oldFileName);
			} catch (error) {
				console.warn(`Failed to delete old image: ${oldFileName}`, error);
			}
		}

		return updatedLocation;
	}
}
