import { LocationsRepo } from '@modules/locations/locations.repo';
import { StorageService } from '@modules/storage/storage.service';
import { Injectable, NotFoundException } from '@nestjs/common';

@Injectable()
export class GetLocationImgUseCase {
	constructor(
		private locations: LocationsRepo,
		private storage: StorageService
	) {}

	async execute(locationId: string): Promise<{ buffer: Buffer; contentType: string }> {
		const location = await this.locations.getById(locationId);
		if (!location || !location.imgKey) {
			throw new NotFoundException('Location or image not found');
		}

		const buffer = await this.storage.downloadFile('locations/' + location.imgKey);
		const contentType = this.getContentTypeFromFileName(location.imgKey);

		return { buffer, contentType };
	}

	private getContentTypeFromFileName(fileName: string): string {
		const extension = fileName.split('.').pop()?.toLowerCase();
		switch (extension) {
			case 'jpg':
			case 'jpeg':
				return 'image/jpeg';
			case 'png':
				return 'image/png';
			case 'webp':
				return 'image/webp';
			default:
				return 'image/jpeg';
		}
	}
}
