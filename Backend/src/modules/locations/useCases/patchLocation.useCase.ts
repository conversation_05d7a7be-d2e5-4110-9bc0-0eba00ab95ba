import { LocationsRepo } from '@modules/locations/locations.repo';
import { Injectable } from '@nestjs/common';
import { Location } from '@prisma/client';

@Injectable()
export class PatchLocationUseCase {
	constructor(private locations: LocationsRepo) {}

	async execute({ id, ...data }: Partial<Location> & Pick<Location, 'id'>): Promise<Location> {
		return await this.locations.patch({
			id,
			...data
		});
	}
}
