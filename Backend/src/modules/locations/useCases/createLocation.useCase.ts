import { LocationsRepo } from '@modules/locations/locations.repo';
import { CreateLocationRequestBody } from '@modules/locations/schemas/createLocation';
import { Injectable } from '@nestjs/common';

import { TransactionService } from '@/infra/prisma/transaction.service';

@Injectable()
export class CreateLocationUseCase {
	constructor(
		private transaction: TransactionService,
		private locations: LocationsRepo
	) {}

	async execute(location: CreateLocationRequestBody) {
		return await this.transaction.run(async (tx) => {
			return await this.locations.create(location, tx);
		});
	}
}
