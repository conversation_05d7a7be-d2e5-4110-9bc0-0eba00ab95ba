import { LocationsRepo } from '@modules/locations/locations.repo';
import { StorageService } from '@modules/storage/storage.service';
import { Injectable, NotFoundException } from '@nestjs/common';

@Injectable()
export class DeleteLocationUseCase {
	constructor(
		private locations: LocationsRepo,
		private storage: StorageService
	) {}

	async execute(locationId: string) {
		const location = await this.locations.getById(locationId);
		if (!location) {
			throw new NotFoundException('Location not found');
		}

		if (location.imgKey != null) {
			try {
				await this.storage.deleteObject('locations/' + location.imgKey);
			} catch (error) {
				console.warn(`Failed to delete image: ${location.imgKey}`, error);
			}
		}

		return await this.locations.delete(locationId);
	}
}
