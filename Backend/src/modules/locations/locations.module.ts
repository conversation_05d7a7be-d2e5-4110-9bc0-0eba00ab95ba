import { LocationsController } from '@modules/locations/controllers/locations.controller';
import { LocationsPrismaRepo } from '@modules/locations/location.prisma.repo';
import { LocationsRepo } from '@modules/locations/locations.repo';
import {
	CreateLocationUseCase,
	DeleteLocationUseCase,
	GetLocationUseCase,
	ListLocationsUseCase
} from '@modules/locations/useCases';
import { GetLocationImgUseCase } from '@modules/locations/useCases/img/getLocationImg.useCase';
import { RemoveLocationImgUseCase } from '@modules/locations/useCases/img/removeLocationImg.useCase';
import { SetLocationImgUseCase } from '@modules/locations/useCases/img/setLocationImg.useCase';
import { PatchLocationUseCase } from '@modules/locations/useCases/patchLocation.useCase';
import { StorageModule } from '@modules/storage/storage.module';
import { Module } from '@nestjs/common';

import { PrismaModule } from '@/infra/prisma/prisma.module';

@Module({
	imports: [PrismaModule, StorageModule],
	providers: [
		{ provide: LocationsRepo, useClass: LocationsPrismaRepo },
		ListLocationsUseCase,
		CreateLocationUseCase,
		GetLocationUseCase,
		DeleteLocationUseCase,
		PatchLocationUseCase,

		//image
		SetLocationImgUseCase,
		RemoveLocationImgUseCase,
		GetLocationImgUseCase
	],
	controllers: [LocationsController],
	exports: [GetLocationUseCase]
})
export class LocationsModule {}
