import { buildRelationCreate, buildRelationUpdate } from '@common/utils';
import { CreateLocationInput, LocationsRepo } from '@modules/locations/locations.repo';
import { Injectable } from '@nestjs/common';
import { type Location, Prisma } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';

@Injectable()
export class LocationsPrismaRepo implements LocationsRepo {
	constructor(private prisma: PrismaService) {}

	async list(args: Prisma.LocationFindManyArgs): Promise<Location[]> {
		return this.prisma.location.findMany({
			...args
		});
	}

	async getById(locationId: string): Promise<Location | null> {
		return this.prisma.location.findUnique({
			where: {
				id: locationId
			}
		});
	}

	async create(
		{ managerIds, teacherIds, ...newLocation }: CreateLocationInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<Location> {
		const createData: Prisma.LocationCreateInput = {
			...newLocation,
			managers: buildRelation<PERSON><PERSON>(managerIds),
			teachers: buildRelationCreate(teacherIds)
		};

		return tx.location.create({
			data: createData,
			include: {
				managers: true,
				teachers: true
			}
		});
	}

	async delete(locationId: string): Promise<Location | null> {
		try {
			return this.prisma.location.delete({
				where: {
					id: locationId
				}
			});
		} catch {
			// If the location doesn't exist, return null
			return null;
		}
	}

	async patch({
		id: locationId,
		managerIds,
		teacherIds,
		...data
	}: Partial<Location> &
		Pick<Location, 'id'> & {
			managerIds?: string[];
			teacherIds?: string[];
		}): Promise<Location> {
		const updateData: Prisma.LocationUpdateInput = {
			...data,
			managers: buildRelationUpdate(managerIds),
			teachers: buildRelationUpdate(teacherIds)
		};

		return this.prisma.location.update({
			where: {
				id: locationId
			},
			data: updateData,
			include: {
				managers: true,
				teachers: true
			}
		});
	}
}
