import { type Location, Prisma } from '@prisma/client';

export type CreateLocationInput = Prisma.LocationUncheckedCreateInput & {
	managerIds?: string[];
	teacherIds?: string[];
};

export abstract class LocationsRepo {
	abstract getById(locationId: string): Promise<Location | null>;
	abstract create(u: CreateLocationInput, tx?: Prisma.TransactionClient): Promise<Location>;
	abstract patch(u: Partial<Location> & Pick<Location, 'id'>): Promise<Location>;
	abstract delete(locationId: string): Promise<Location | null>;
	abstract list(args?: Prisma.LocationFindManyArgs): Promise<Location[]>;
}
