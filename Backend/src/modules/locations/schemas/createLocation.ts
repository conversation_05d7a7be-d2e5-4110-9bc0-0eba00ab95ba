import z from 'zod';

export const CreateLocationRequestBodySchema = z.object({
	name: z.string().min(1),
	address: z.string().min(1),
	description: z.string().min(1),
	capacity: z.number().min(1).default(1),
	purpose: z.enum(['prijimacky_nanecisto', 'prezenc<PERSON>_doucovani']),
	longitude: z.string().min(1),
	latitude: z.string().min(1),
	managerIds: z.array(z.uuid()).optional(),
	teacherIds: z.array(z.uuid()).optional()
});

export type CreateLocationRequestBody = z.infer<typeof CreateLocationRequestBodySchema>;
