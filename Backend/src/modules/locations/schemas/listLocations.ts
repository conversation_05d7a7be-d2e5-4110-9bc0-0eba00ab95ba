import { FilterSchema, SortSchema } from 'src/common/schemas';
import z from 'zod';

const locationFields = ['name', 'address'] as const;

const FilterSchemaLocal = FilterSchema(locationFields, [
	'equals',
	'contains',
	'lt',
	'gt',
	'lte',
	'gte'
] as const);

export const ListLocationsRequestBodySchema = z
	.object({
		skip: z.coerce.number().optional(),
		take: z.coerce.number().optional(),
		sort: SortSchema(locationFields),
		filters: FilterSchemaLocal.optional()
	})
	.strict();
export type ListLocationsRequestBody = z.infer<typeof ListLocationsRequestBodySchema>;
