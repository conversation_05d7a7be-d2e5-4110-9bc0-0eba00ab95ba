import { GcsFileInterceptor } from '@common/interceptors';
import {
	type CreateLocationRequestBody,
	CreateLocationRequestBodySchema
} from '@modules/locations/schemas/createLocation';
import {
	type ListLocationsRequestBody,
	ListLocationsRequestBodySchema
} from '@modules/locations/schemas/listLocations';
import {
	type PatchLocationRequestBody,
	PatchLocationRequestBodySchema
} from '@modules/locations/schemas/patchLocation';
import {
	CreateLocationUseCase,
	DeleteLocationUseCase,
	GetLocationUseCase,
	ListLocationsUseCase
} from '@modules/locations/useCases';
import { GetLocationImgUseCase } from '@modules/locations/useCases/img/getLocationImg.useCase';
import { RemoveLocationImgUseCase } from '@modules/locations/useCases/img/removeLocationImg.useCase';
import { SetLocationImgUseCase } from '@modules/locations/useCases/img/setLocationImg.useCase';
import { PatchLocationUseCase } from '@modules/locations/useCases/patchLocation.useCase';
import {
	Body,
	Controller,
	Delete,
	Get,
	HttpStatus,
	Patch,
	Post,
	Res,
	UploadedFile,
	UseInterceptors
} from '@nestjs/common';
import type { Response } from 'express';
import { UuidParam, ZodBody, ZodQuery } from 'src/common/decorators/zodSchema.decorator';
import { created, ok } from 'src/common/utils/responses';

@Controller('locations')
export class LocationsController {
	constructor(
		private listLocationsUc: ListLocationsUseCase,
		private createLocationUc: CreateLocationUseCase,
		private getLocationUc: GetLocationUseCase,
		private deleteLocationUc: DeleteLocationUseCase,
		private patchLocationUseCase: PatchLocationUseCase,
		//img
		private getLocationImgUseCase: GetLocationImgUseCase,
		private setLocationImgUseCase: SetLocationImgUseCase,
		private removeLocationImgUseCase: RemoveLocationImgUseCase
	) {}

	@Get()
	async listLocations(
		@ZodQuery(ListLocationsRequestBodySchema) dto: ListLocationsRequestBody,
		@Res() res: Response
	) {
		const locations = await this.listLocationsUc.execute(dto);
		return res.status(HttpStatus.OK).json(ok(locations));
	}

	@Get(':locationId')
	async getLocation(@UuidParam('locationId') locationId: string, @Res() res: Response) {
		const location = await this.getLocationUc.execute(locationId);
		return res.status(HttpStatus.OK).json(ok(location));
	}

	@Post()
	async createLocation(
		@ZodBody(CreateLocationRequestBodySchema) dto: CreateLocationRequestBody,
		@Res() res: Response
	) {
		const createdLocation = await this.createLocationUc.execute(dto);
		res.status(HttpStatus.CREATED).json(created(createdLocation));
	}

	@Patch(':locationId')
	async patchLocation(
		@UuidParam('locationId') locationId: string,
		@ZodBody(PatchLocationRequestBodySchema) locationData: PatchLocationRequestBody,
		@Res() res: Response
	) {
		const updatedLocation = await this.patchLocationUseCase.execute({
			id: locationId,
			...locationData
		});
		res.status(HttpStatus.OK).json(created(updatedLocation));
	}

	@Delete(':locationId')
	async deleteLocation(@UuidParam('locationId') locationId: string, @Res() res: Response) {
		await this.deleteLocationUc.execute(locationId);
		return res.status(HttpStatus.OK).json(ok(undefined));
	}

	//image
	@Post(':locationId/image')
	@UseInterceptors(GcsFileInterceptor('image', 'locations/'))
	async setLocationImage(
		@UuidParam('locationId') locationId: string,
		@UploadedFile() file: Express.Multer.File,
		@Res() res: Response
	) {
		const updatedLocation = await this.setLocationImgUseCase.execute(locationId, file.filename);

		return res.status(HttpStatus.OK).json(ok(updatedLocation));
	}

	@Get(':locationId/image')
	async getLocationImage(@UuidParam('locationId') locationId: string, @Res() res: Response) {
		const { buffer, contentType } = await this.getLocationImgUseCase.execute(locationId);

		res.set({
			'Content-Type': contentType,
			'Cache-Control': 'public, max-age=3600' // Cache 1 hour
		});

		res.send(buffer);
	}

	@Delete(':locationId/image')
	async deleteLocationImage(@UuidParam('locationId') locationId: string, @Res() res: Response) {
		const updatedLocation = await this.removeLocationImgUseCase.execute(locationId);
		return res.status(HttpStatus.OK).json(ok(updatedLocation));
	}
}
