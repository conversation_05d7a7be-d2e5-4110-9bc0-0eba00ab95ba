import { Injectable } from '@nestjs/common';

import {
	RequestVerificationInput,
	RequestVerificationUc
} from './useCases/requestVerification.useCase';
import { VerifyEmailInput, VerifyEmailOutput, VerifyEmailUc } from './useCases/verifyEmail.useCase';

@Injectable()
export class EmailVerificationTokensService {
	constructor(
		private readonly requestUc: RequestVerificationUc,
		private readonly verifyUc: VerifyEmailUc
	) {}

	request(input: RequestVerificationInput) {
		return this.requestUc.exec(input);
	}
	verify(input: VerifyEmailInput): Promise<VerifyEmailOutput> {
		return this.verifyUc.exec(input);
	}
	resend(input: RequestVerificationInput) {
		return this.requestUc.exec(input);
	}
}
