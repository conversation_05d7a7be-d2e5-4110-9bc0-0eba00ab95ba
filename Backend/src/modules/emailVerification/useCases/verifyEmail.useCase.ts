import { BadRequestException, Injectable } from '@nestjs/common';

import { verifyToken } from '../../../common/crypto/tokens';
import { UsersRepo } from '../../users/users.repo';
import { EmailVerificationTokensRepo } from '../repo/emailVerificationTokens/emailVerificationTokens.repo';

export type VerifyEmailInput = { verificationId: string; token: string };
export type VerifyEmailOutput = { userId: string };

@Injectable()
export class VerifyEmailUc {
	constructor(
		private readonly tokensRepo: EmailVerificationTokensRepo,
		private readonly usersRepo: UsersRepo
	) {}

	async exec({ verificationId, token }: VerifyEmailInput): Promise<VerifyEmailOutput> {
		if (!verificationId || !token) throw new BadRequestException('Missing parameters');

		const row = await this.tokensRepo.findActiveById(verificationId);
		if (!row) throw new BadRequestException('Invalid or expired');

		const ok = verifyToken(token, row.tokenHash);
		if (!ok) throw new BadRequestException('Invalid or expired');

		await this.tokensRepo.markUsed(verificationId);
		await this.usersRepo.activateEmail(row.userId);
		return { userId: row.userId };
	}
}
