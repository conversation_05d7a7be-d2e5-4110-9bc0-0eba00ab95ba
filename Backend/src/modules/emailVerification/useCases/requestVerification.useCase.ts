import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';

import { createEmailToken } from '../../../common/crypto/tokens';
import { LinkService } from '../../link/link.service';
import { MailerService } from '../../mailer/mailer.service';
import { UsersRepo } from '../../users/users.repo';
import { EmailVerificationRequestsRepo } from '../repo/emailVerificationRequests/emailVerificationRequests.repo';
import { EmailVerificationTokensRepo } from '../repo/emailVerificationTokens/emailVerificationTokens.repo';

export type RequestVerificationInput = { userId: string };

@Injectable()
export class RequestVerificationUc {
	constructor(
		private readonly emailVTRepo: EmailVerificationTokensRepo,
		private readonly sendsRepo: EmailVerificationRequestsRepo,
		private readonly userRepo: UsersRepo,
		private mailer: MailerService,
		private readonly link: LinkService
	) {}

	async exec({ userId }: RequestVerificationInput): Promise<void> {
		const user = await this.userRepo.getById(userId);
		if (!user) {
			throw new BadRequestException('Unknown user');
		}

		const now = new Date();
		const minInterval = 300;
		const maxPerDay = 5;

		// rate-limit per user
		const since = new Date(now.getTime() - minInterval * 1000);
		if ((await this.sendsRepo.countSince(userId, since)) > 0) {
			throw new ForbiddenException('Too many requests');
		}
		const sinceDay = new Date(now.getTime() - 24 * 3600 * 1000);
		if ((await this.sendsRepo.countSince(userId, sinceDay)) >= maxPerDay) {
			throw new ForbiddenException('Daily limit reached');
		}

		const { token, tokenHash, expiresAt } = createEmailToken(48);

		const { id: verificationId } = await this.emailVTRepo.insert({
			userId,
			tokenHash,
			expiresAt
		});

		await this.sendsRepo.insert({ userId });

		const verifyUrl = this.link.build('/verify', { v: verificationId, t: token });

		// Send via typed template
		await this.mailer.sendTemplate({ email: user.email }, 'emailVerify', 'cs', { verifyUrl });
	}
}
