import { <PERSON>, <PERSON>, Res } from '@nestjs/common';
import type { Response } from 'express';

import { ZodBody } from '../../../common/decorators/zodSchema.decorator';
import {
	RequestEmailSchema,
	type RequestEmailValues
} from '../../../common/schemas/requestEmailVerification';
import { VerifyEmailSchema, type VerifyEmailValues } from '../../../common/schemas/verifyEmail';
import { RequestVerificationUc } from '../useCases/requestVerification.useCase';
import { VerifyEmailUc } from '../useCases/verifyEmail.useCase';

@Controller('email-verification')
export class EmailVerificationController {
	constructor(
		private readonly requestUc: RequestVerificationUc,
		private readonly verifyUc: VerifyEmailUc
	) {}

	@Post('request')
	request(@ZodBody(RequestEmailSchema) dto: RequestEmailValues) {
		return this.requestUc.exec({ userId: dto.userId }).then(() => ({ ok: true }));
	}

	@Post('resend')
	resend(@ZodBody(RequestEmailSchema) dto: RequestEmailValues) {
		return this.requestUc.exec({ userId: dto.userId }).then(() => ({ ok: true }));
	}

	@Post('verify')
	async verifyPost(@ZodBody(VerifyEmailSchema) dto: VerifyEmailValues, @Res() res: Response) {
		await this.verifyUc.exec({ verificationId: dto.v, token: dto.t });
		return res.status(200).send();
	}
}
