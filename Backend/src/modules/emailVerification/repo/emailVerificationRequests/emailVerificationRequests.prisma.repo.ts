import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '../../../../infra/prisma/prisma.service';
import { EmailVerificationRequestsRepo } from './emailVerificationRequests.repo';

@Injectable()
export class EmailVerificationRequestsPrismaRepo implements EmailVerificationRequestsRepo {
	constructor(private prisma: PrismaService) {}

	async insert(row: Prisma.EmailVerificationRequestUncheckedCreateInput): Promise<void> {
		await this.prisma.emailVerificationRequest.create({
			data: row
		});
	}

	async countSince(userId: string, since: Date): Promise<number> {
		return await this.prisma.emailVerificationRequest.count({
			where: {
				userId,
				sentAt: {
					gt: since
				}
			}
		});
	}

	async lastSentAt(userId: string): Promise<Date | null> {
		const result = await this.prisma.emailVerificationRequest.findFirst({
			where: {
				userId
			},
			select: {
				sentAt: true
			},
			orderBy: {
				sentAt: 'desc'
			}
		});

		return result?.sentAt ?? null;
	}
}
