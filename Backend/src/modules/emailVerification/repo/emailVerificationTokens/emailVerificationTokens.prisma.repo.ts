import { Injectable } from '@nestjs/common';
import { EmailVerificationToken, Prisma } from '@prisma/client';

import { PrismaService } from '../../../../infra/prisma/prisma.service';
import { EmailVerificationTokensRepo } from './emailVerificationTokens.repo';

@Injectable()
export class EmailVerificationTokensPrismaRepo implements EmailVerificationTokensRepo {
	constructor(private prisma: PrismaService) {}

	async insert(
		row: Prisma.EmailVerificationTokenUncheckedCreateInput
	): Promise<EmailVerificationToken> {
		return await this.prisma.emailVerificationToken.create({
			data: row
		});
	}

	async findActiveById(verificationId: string): Promise<EmailVerificationToken | null> {
		const now = new Date();
		return await this.prisma.emailVerificationToken.findFirst({
			where: {
				id: verificationId,
				usedAt: null,
				revokedAt: null,
				expiresAt: {
					gt: now
				}
			}
		});
	}

	async markUsed(verificationId: string, at = new Date()): Promise<void> {
		await this.prisma.emailVerificationToken.update({
			where: {
				id: verificationId
			},
			data: {
				usedAt: at
			}
		});
	}

	async revokeAllForUser(userId: string, at = new Date()): Promise<void> {
		await this.prisma.emailVerificationToken.updateMany({
			where: {
				userId,
				usedAt: null,
				revokedAt: null
			},
			data: {
				revokedAt: at
			}
		});
	}

	async gcExpired(before: Date): Promise<number> {
		const result = await this.prisma.emailVerificationToken.deleteMany({
			where: {
				expiresAt: {
					lt: before
				},
				OR: [{ usedAt: { not: null } }, { revokedAt: { not: null } }]
			}
		});
		return result.count;
	}
}
