import { EmailVerificationToken, Prisma } from '@prisma/client';

export abstract class EmailVerificationTokensRepo {
	abstract insert(
		row: Prisma.EmailVerificationTokenUncheckedCreateInput
	): Promise<EmailVerificationToken>;

	abstract findActiveById(verificationId: string): Promise<EmailVerificationToken | null>;

	abstract markUsed(verificationId: string, at?: Date): Promise<void>;

	abstract revokeAllForUser(userId: string, at?: Date): Promise<void>;

	abstract gcExpired(before: Date): Promise<number>;
}
