import { Module } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { MailerModule } from '../mailer/mailer.module';
import { UsersPrismaRepo } from '../users/users.prisma.repo';
import { UsersRepo } from '../users/users.repo';
import { EmailVerificationController } from './controllers/emailVerificationTokens.controller';
import { EmailVerificationTokensService } from './emailVerificationTokens.service';
import { EmailVerificationRequestsPrismaRepo } from './repo/emailVerificationRequests/emailVerificationRequests.prisma.repo';
import { EmailVerificationRequestsRepo } from './repo/emailVerificationRequests/emailVerificationRequests.repo';
import { EmailVerificationTokensPrismaRepo } from './repo/emailVerificationTokens/emailVerificationTokens.prisma.repo';
import { EmailVerificationTokensRepo } from './repo/emailVerificationTokens/emailVerificationTokens.repo';
import { RequestVerificationUc } from './useCases/requestVerification.useCase';
import { VerifyEmailUc } from './useCases/verifyEmail.useCase';

@Module({
	imports: [MailerModule, PrismaModule],
	controllers: [EmailVerificationController],
	providers: [
		RequestVerificationUc,
		VerifyEmailUc,
		EmailVerificationTokensService,
		{ provide: EmailVerificationTokensRepo, useClass: EmailVerificationTokensPrismaRepo },
		{ provide: EmailVerificationRequestsRepo, useClass: EmailVerificationRequestsPrismaRepo },
		{ provide: UsersRepo, useClass: UsersPrismaRepo }
	],
	exports: [EmailVerificationTokensService, RequestVerificationUc, VerifyEmailUc]
})
export class EmailVerificationModule {}
