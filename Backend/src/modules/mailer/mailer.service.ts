import { <PERSON>MailP<PERSON>ider, MailAttachment } from './mail.interface';
import { Template<PERSON>ey, TemplateParams } from './templater/templateRegistry';
import { TwigTemplateService } from './templater/twig-template.service';
import { Locale } from './templater/types';

export class MailerService {
	constructor(
		private provider: IMailProvider,
		private templates: TwigTemplateService,
		private defaultFrom: { email: string; name?: string }
	) {}

	async sendTemplate<K extends TemplateKey>(
		to: { email: string; name?: string },
		templateKey: K,
		locale: Locale,
		data: TemplateParams<K>,
		attachments?: MailAttachment[]
	) {
		const { html, text, subject } = await this.templates.render(templateKey, locale, data);
		await this.provider.send({
			from: this.defaultFrom,
			to,
			subject,
			html,
			text,
			tags: [String(templateKey)],
			attachments
		});
	}

	async sendSimple(
		to: { email: string; name?: string },
		subject: string,
		html: string,
		text?: string,
		attachments?: MailAttachment[]
	) {
		await this.provider.send({
			from: this.defaultFrom,
			to,
			subject,
			html,
			text,
			attachments
		});
	}
}
