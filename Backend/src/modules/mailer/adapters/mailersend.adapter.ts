import { Attach<PERSON>, EmailParams, <PERSON><PERSON><PERSON><PERSON>, Recipient, Sender } from 'mailersend';

import { IMailProvider, SendMailOptions } from '../mail.interface';

export class MailerSendAdapter implements IMailProvider {
	private client: MailerSend;
	private defaultFrom: Sender;

	constructor(apiKey: string, fromEmail: string, fromName?: string) {
		this.client = new MailerSend({ apiKey });
		this.defaultFrom = new Sender(fromEmail, fromName ?? undefined);
	}

	async send(opts: SendMailOptions): Promise<void> {
		try {
			const toArr = Array.isArray(opts.to) ? opts.to : [opts.to];
			const recipients = toArr.map((t) => new Recipient(t.email, t.name));
			const emailParams = new EmailParams()
				.setFrom(opts.from ? new Sender(opts.from.email, opts.from.name) : this.defaultFrom)
				.setTo(recipients)
				.setSubject(opts.subject)
				.setHtml(opts.html)
				.setText(opts.text ?? '')
				.setTags(opts.tags ?? []);

			if (opts.attachments && opts.attachments.length > 0) {
				const attachments = opts.attachments.map((att) => {
					const content = Buffer.isBuffer(att.content)
						? att.content.toString('base64')
						: Buffer.from(att.content).toString('base64');

					return new Attachment(content, att.filename, att.disposition || 'attachment');
				});
				emailParams.setAttachments(attachments);
			}

			await this.client.email.send(emailParams);
		} catch (e) {
			console.log(e);
		}
	}
}
