export type MailAddress = { email: string; name?: string };

export interface MailAttachment {
	filename: string;
	content: Buffer | string;
	contentType?: string;
	contentId?: string;
	disposition?: 'attachment' | 'inline';
}

export interface SendMailOptions {
	from?: MailAddress;
	to: MailAddress | MailAddress[];
	subject: string;
	html: string;
	text?: string;
	tags?: string[];
	replyTo?: MailAddress;
	headers?: Record<string, string>;
	attachments?: MailAttachment[];
}

export interface IMailProvider {
	send(options: SendMailOptions): Promise<void>;
}
