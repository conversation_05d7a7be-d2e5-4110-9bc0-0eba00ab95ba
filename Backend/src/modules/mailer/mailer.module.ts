import path from 'node:path';

import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { MailerSendAdapter } from './adapters/mailersend.adapter';
import { IMailProvider } from './mail.interface';
import { MailerService } from './mailer.service';
import { TwigTemplateService } from './templater/twig-template.service';

export const MAIL_PROVIDER = 'MAIL_PROVIDER_TOKEN';

@Module({
	providers: [
		{
			provide: MAIL_PROVIDER,
			inject: [ConfigService],
			useFactory: (cfg: ConfigService): IMailProvider => {
				const provider = (cfg.get<string>('MAIL_PROVIDER') ?? 'mailersend').toLowerCase();
				switch (provider) {
					case 'mailersend': {
						const key = cfg.getOrThrow<string>('MAILERSEND_API_KEY');
						const fromEmail = cfg.getOrThrow<string>('MAIL_FROM_EMAIL');
						const fromName = cfg.get<string>('MAIL_FROM_NAME');
						return new MailerSendAdapter(key, fromEmail, fromName);
					}
					default:
						throw new Error(`Unknown MAIL_PROVIDER: ${provider}`);
				}
			}
		},
		{
			provide: TwigTemplateService,
			inject: [ConfigService],
			useFactory: (cfg: ConfigService) => {
				const baseDir =
					cfg.get<string>('MAIL_TEMPLATES_DIR') ??
					path.join(process.cwd(), 'src', 'modules', 'mailer', 'templater', 'templates');
				return new TwigTemplateService(baseDir, {
					companyName: cfg.get<string>('MAIL_COMPANY_NAME') ?? 'Example, s.r.o.',
					companyAddress: cfg.get<string>('MAIL_COMPANY_ADDRESS') ?? 'Ulice , Město',
					contactMail:
						cfg.get<string>('MAIL_CONTACT_MAIL') ??
						cfg.get<string>('MAIL_FROM_EMAIL') ??
						'',
					contactPhone: cfg.get<string>('MAIL_CONTACT_PHONE') ?? '',
					socials: {
						instagram:
							cfg.get<string>('MAIL_SOCIAL_INSTAGRAM') ?? 'https://instagram.com',
						facebook: cfg.get<string>('MAIL_SOCIAL_FACEBOOK') ?? 'https://facebook.com',
						tiktok: cfg.get<string>('MAIL_SOCIAL_TIKTOK') ?? 'https://tiktok.com'
					},
					logoUrl: cfg.get<string>('MAIL_LOGO_URL') ?? 'http://localhost/img/logo.svg',
					companyICO: cfg.get<string>('MAIL_COMPANY_ICO')
				});
			}
		},
		{
			provide: MailerService,
			inject: [MAIL_PROVIDER, TwigTemplateService, ConfigService],
			useFactory: (
				provider: IMailProvider,
				tmpl: TwigTemplateService,
				cfg: ConfigService
			) => {
				const from = {
					email: cfg.getOrThrow<string>('MAIL_FROM_EMAIL'),
					name: cfg.get<string>('MAIL_FROM_NAME')
				};
				return new MailerService(provider, tmpl, from);
			}
		}
	],
	exports: [MailerService]
})
export class MailerModule {}
