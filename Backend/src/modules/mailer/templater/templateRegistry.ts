import path from 'node:path';

import { I18nMap, Locale } from './types';

export type SubjectDef<P> = Partial<Record<Locale, string | ((params: P) => string)>> &
	Record<Locale, string | ((params: P) => string)>;

export type TemplateDef<P> = {
	file: string;
	params: P;
	i18n?: I18nMap;
	subject: SubjectDef<P>;
};

export const templateRegistry = {
	emailVerify: {
		file: path.join('emailVerify', 'emailVerify.mjml'),
		params: {} as { verifyUrl: string },
		subject: {
			cs: () => 'Ověřte svůj e-mail',
			ua: () => 'Підтвердіть свою електронну пошту'
		}
	},
	'newsletter.welcome': {
		file: path.join('newsletter', 'welcome.mjml'),
		params: {} as { unsubscribeUrl: string },
		subject: {
			cs: () => 'Vítejte v newsletteru',
			ua: () => 'Ласкаво просимо до розсилки'
		}
	},
	'password.forgot': {
		file: path.join('password', 'forgotPassword.mjml'),
		params: {} as { resetUrl: string },
		subject: {
			cs: () => 'Obnovení hesla',
			ua: () => 'Відновлення пароля'
		}
	},
	'password.resetSuccess': {
		file: path.join('password', 'passwordResetSucces.mjml'),
		params: {} as { loginUrl: string },
		subject: {
			cs: () => 'Heslo bylo změněno',
			ua: () => 'Пароль змінено'
		}
	}
} as const satisfies Record<string, TemplateDef<unknown>>;

export type TemplateKey = keyof typeof templateRegistry;
export type TemplateParams<K extends TemplateKey> = K extends keyof typeof templateRegistry
	? (typeof templateRegistry)[K]['params']
	: never;
