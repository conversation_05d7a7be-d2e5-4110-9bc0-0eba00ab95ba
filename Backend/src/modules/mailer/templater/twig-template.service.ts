import fs from 'node:fs/promises';
import path from 'node:path';

import mjml2html from 'mjml';
import Twig from 'twig';

import { SubjectDef, TemplateKey, TemplateParams, templateRegistry } from './templateRegistry';
import { Locale } from './types';

interface TwigTemplate {
	render(context: Record<string, unknown>): string;
}
interface TwigModule {
	twig(opts: { data: string; path?: string; rethrow?: boolean }): TwigTemplate;
}
const TwigMod: TwigModule = Twig as unknown as TwigModule;

type MjmlValidation = 'soft' | 'strict' | 'skip';
interface MjmlOptions {
	validationLevel?: MjmlValidation;
	filePath?: string;
}
interface MjmlError {
	message?: string;
	formattedMessage?: string;
}
interface MjmlResult {
	html: string;
	errors: ReadonlyArray<MjmlError>;
}
type MjmlFn = (input: string, options?: MjmlOptions) => MjmlResult;
const renderMjml: MjmlFn = mjml2html as unknown as MjmlFn;

export class TwigTemplateService {
	private readonly cache = new Map<string, TwigTemplate>();

	constructor(
		private readonly baseDir: string,
		private readonly defaults?: {
			companyName?: string;
			companyAddress?: string;
			contactMail?: string;
			contactPhone?: string;
			socials?: { instagram?: string; facebook?: string; tiktok?: string };
			logoUrl?: string;
			companyICO?: string;
		}
	) {}

	async render<K extends TemplateKey>(
		templateKey: K,
		locale: Locale,
		data: TemplateParams<K>
	): Promise<{ html: string; text: string; subject: string }> {
		const def = templateRegistry[templateKey];
		if (!def) {
			throw new Error(`Unknown template key: ${String(templateKey)}`);
		}

		const file = path.join(this.baseDir, def.file);

		// cache: TwigTemplate | undefined
		let compiled = this.cache.get(file);
		if (!compiled) {
			const source = await fs.readFile(file, 'utf8');
			const tpl = TwigMod.twig({ data: source, path: file, rethrow: true });
			this.cache.set(file, tpl);
			compiled = tpl;
		}

		const context: Record<string, unknown> = {
			...(data as object),
			locale,
			year: (data as { year?: string }).year ?? new Date().getFullYear().toString(),
			companyName: this.defaults?.companyName ?? '',
			companyAddress: this.defaults?.companyAddress ?? '',
			contactMail: this.defaults?.contactMail ?? '',
			contactPhone: this.defaults?.contactPhone ?? '',
			socials: this.defaults?.socials ?? { instagram: '', facebook: '', tiktok: '' },
			logoUrl: this.defaults?.logoUrl ?? '',
			companyICO: this.defaults?.companyICO ?? ''
		};

		const mjmlMarkup: string = compiled.render(context);

		const result: MjmlResult = renderMjml(mjmlMarkup, {
			validationLevel: 'soft',
			filePath: path.dirname(file)
		});

		const html: string = result.html;
		const errors: ReadonlyArray<MjmlError> = result.errors ?? [];

		if (errors.length > 0) {
			const msg = errors
				.map((e) => e.formattedMessage ?? e.message ?? 'Unknown MJML error')
				.join('; ');
			throw new Error(`MJML validation errors for template ${String(templateKey)}: ${msg}`);
		}

		const text: string = html
			.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
			.replace(/<[^>]+>/g, ' ')
			.replace(/\s+/g, ' ')
			.trim();

		const subjectDef = def.subject as SubjectDef<TemplateParams<K>>;
		const subjectValue = subjectDef[locale];
		const subject: string =
			typeof subjectValue === 'function'
				? (subjectValue as (d: TemplateParams<K>) => string)(data)
				: subjectValue;

		return { html, text, subject };
	}
}
