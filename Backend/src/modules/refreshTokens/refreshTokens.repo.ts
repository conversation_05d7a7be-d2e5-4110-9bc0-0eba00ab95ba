import { Prisma, RefreshToken } from '@prisma/client';

export abstract class RefreshTokensRepo {
	abstract findByJti(jti: string): Promise<RefreshToken | null>;

	abstract insert(
		row: Prisma.RefreshTokenUncheckedCreateInput,
		tx?: Prisma.TransactionClient
	): Promise<void>;

	abstract markUsedAndReplaced(oldJti: string, newJti: string): Promise<void>;

	abstract revokeFamily(familyId: string): Promise<void>;

	abstract revokeBySID(familyId: string): Promise<void>;

	abstract revokeAllByUserId(userId: string): Promise<void>;

	abstract gcOld(familyId: string): Promise<void>;

	abstract revokeAllBySession(sessionId: string): Promise<void>;
}
