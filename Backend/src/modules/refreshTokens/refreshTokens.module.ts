import { Module } from '@nestjs/common';

import { PrismaModule } from '../../infra/prisma/prisma.module';
import { RefreshTokensPrismaRepo } from './refreshTokens.prisma.repo';
import { RefreshTokensRepo } from './refreshTokens.repo';

@Module({
	imports: [PrismaModule],
	providers: [{ provide: RefreshTokensRepo, useClass: RefreshTokensPrismaRepo }],
	exports: [RefreshTokensRepo]
})
export class RefreshTokensModule {}
