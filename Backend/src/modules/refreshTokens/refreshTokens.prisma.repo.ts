import { Injectable } from '@nestjs/common';
import { Prisma, RefreshToken } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { RefreshTokensRepo } from './refreshTokens.repo';

@Injectable()
export class RefreshTokensPrismaRepo implements RefreshTokensRepo {
	constructor(private prisma: PrismaService) {}

	async findByJti(jti: string): Promise<RefreshToken | null> {
		return await this.prisma.refreshToken.findUnique({
			where: {
				jti
			}
		});
	}

	async insert(
		row: Prisma.RefreshTokenUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<void> {
		await tx.refreshToken.create({
			data: row
		});
	}

	async markUsedAndReplaced(oldJti: string, newJti: string): Promise<void> {
		await this.prisma.refreshToken.updateMany({
			where: {
				jti: old<PERSON>ti,
				usedAt: null
			},
			data: {
				usedAt: new Date(),
				replacedBy: newJti
			}
		});
	}

	async revokeFamily(fid: string): Promise<void> {
		await this.prisma.refreshToken.updateMany({
			where: {
				familyId: fid
			},
			data: {
				revokedAt: new Date()
			}
		});
	}

	async revokeAllByUserId(userId: string): Promise<void> {
		await this.prisma.refreshToken.updateMany({
			where: {
				userId
			},
			data: {
				revokedAt: new Date()
			}
		});
	}

	async revokeBySID(sid: string): Promise<void> {
		await this.prisma.refreshToken.updateMany({
			where: {
				sessionId: sid
			},
			data: {
				revokedAt: new Date()
			}
		});
	}

	async gcOld(fid: string): Promise<void> {
		const oneMinuteAgo = new Date();
		oneMinuteAgo.setMinutes(oneMinuteAgo.getMinutes() - 1);

		await this.prisma.refreshToken.deleteMany({
			where: {
				familyId: fid,
				OR: [
					{
						usedAt: {
							not: null,
							lt: oneMinuteAgo
						}
					},
					{
						revokedAt: {
							not: null,
							lt: oneMinuteAgo
						}
					}
				]
			}
		});
	}

	async revokeAllBySession(sessionId: string): Promise<void> {
		await this.prisma.refreshToken.updateMany({
			where: {
				sessionId,
				revokedAt: null
			},
			data: {
				revokedAt: new Date()
			}
		});
	}
}
