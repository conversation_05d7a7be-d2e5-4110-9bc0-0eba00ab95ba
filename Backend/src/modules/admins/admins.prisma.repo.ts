import { AdminsRepo } from '@modules/admins/admins.repo';
import { Injectable } from '@nestjs/common';
import { Admin, Prisma, User } from '@prisma/client';

import { PrismaService } from '@/infra/prisma/prisma.service';

@Injectable()
export class AdminsPrismaRepo implements AdminsRepo {
	constructor(private prisma: PrismaService) {}

	async list({
		include,
		...args
	}: Prisma.AdminFindManyArgs): Promise<(Admin & { user: User })[]> {
		return await this.prisma.admin.findMany({
			...args,
			include: {
				user: true,
				...(include ?? {})
			}
		});
	}

	async getById(adminId: string): Promise<Admin | null> {
		return await this.prisma.admin.findUnique({
			where: {
				id: adminId
			}
		});
	}

	async getByUserId(userId: string): Promise<Admin | null> {
		return await this.prisma.admin.findFirst({
			where: {
				userId
			}
		});
	}

	async create(
		newAdmin: Prisma.AdminUncheckedCreateInput,
		tx: Prisma.TransactionClient = this.prisma
	): Promise<Admin> {
		return await tx.admin.create({
			data: newAdmin
		});
	}

	async delete(adminId: string): Promise<Admin | null> {
		try {
			return await this.prisma.admin.delete({
				where: {
					id: adminId
				}
			});
		} catch {
			// If the admin doesn't exist, return null
			return null;
		}
	}

	async patch({ id: adminId, ...data }: Partial<Admin> & Pick<Admin, 'id'>): Promise<Admin> {
		return await this.prisma.admin.update({
			where: {
				id: adminId
			},
			data
		});
	}
}
