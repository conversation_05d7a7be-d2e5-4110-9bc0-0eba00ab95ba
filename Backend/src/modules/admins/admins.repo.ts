import { Ad<PERSON>, Prisma, User } from '@prisma/client';

export abstract class AdminsRepo {
	abstract getById(adminId: string): Promise<Admin | null>;
	abstract getByUserId(userId: string): Promise<Admin | null>;
	abstract create(
		u: Prisma.AdminUncheckedCreateInput,
		tx?: Prisma.TransactionClient
	): Promise<Admin>;
	abstract patch(u: Partial<Admin> & Pick<Admin, 'id'>): Promise<Admin>;
	abstract delete(adminId: string): Promise<Admin | null>;
	abstract list(args?: Prisma.AdminFindManyArgs): Promise<
		(Admin & {
			user: User;
		})[]
	>;
}
