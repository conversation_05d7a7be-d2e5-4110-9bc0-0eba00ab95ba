import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { AdminsRepo } from '../../admins.repo';
import { ListAdminsRequestBody } from '../../schemas/listAdmins';
import { transformFiltering } from './transformFiltering';
import { transformSorting } from './transformSorting';

@Injectable()
export class ListAdminsUseCase {
	constructor(private admins: AdminsRepo) {}
	async execute({ skip, filters, sort, take }: ListAdminsRequestBody) {
		const findArgs: Prisma.AdminFindManyArgs = {};

		if (sort) {
			findArgs.orderBy = transformSorting(sort);
		}

		if (filters) {
			findArgs.where = transformFiltering(filters);
		}

		return this.admins.list({
			skip,
			take,
			...findArgs
		});
	}
}
