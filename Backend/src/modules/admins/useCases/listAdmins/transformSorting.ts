import { Prisma } from '@prisma/client';
import { getSort } from 'src/common/utils';

import { ListAdminsRequestBody } from '../../schemas/listAdmins';

export const transformSorting = (
	sort: NonNullable<ListAdminsRequestBody['sort']>
): Prisma.AdminOrderByWithRelationInput => {
	const userSortInput: Prisma.UserOrderByWithRelationInput = {
		email: getSort(sort, 'email'),
		firstName: getSort(sort, 'firstName'),
		lastName: getSort(sort, 'lastName')
	};

	const someUserSort = Object.values(userSortInput).some((v) => v !== undefined);
	return {
		...(someUserSort ? { user: userSortInput } : {})
	};
};
