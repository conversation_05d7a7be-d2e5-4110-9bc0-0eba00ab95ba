import { getStringFilter, indexBy } from '@common/utils';
import { Prisma } from '@prisma/client';

import { ListAdminsRequestBody } from '../../schemas/listAdmins';

export const transformFiltering = (
	args: NonNullable<ListAdminsRequestBody['filters']>
): Prisma.AdminWhereInput => {
	const { email, firstName, lastName } = indexBy(args, 'field');

	const userWhereInput: Prisma.UserWhereInput = {
		email: getStringFilter(email),
		firstName: getStringFilter(firstName),
		lastName: getStringFilter(lastName)
	};
	const someUserFilter = Object.values(userWhereInput).some((v) => v !== undefined);

	return {
		...(someUserFilter ? { user: userWhereInput } : {})
	};
};
