import { AdminsRepo } from '@modules/admins/admins.repo';
import { CreateAdminRequestBody } from '@modules/admins/schemas';
import { Injectable } from '@nestjs/common';

import { TransactionService } from '@/infra/prisma/transaction.service';
import { UsersRepo } from '@/modules/users/users.repo';
@Injectable()
export class CreateAdminUseCase {
	constructor(
		private admins: AdminsRepo,
		private users: UsersRepo,
		private transaction: TransactionService
	) {}
	async execute({ email, firstName, lastName }: CreateAdminRequestBody) {
		const createdAdmin = await this.transaction.run(async (tx) => {
			const createdUser = await this.users.create(
				{
					email,
					firstName,
					lastName,
					//TODO: send email to set password
					passwordHash: '',
					role: 'admin'
				},
				tx
			);

			const createdAdmin = await this.admins.create(
				{
					userId: createdUser.id
				},
				tx
			);

			return createdAdmin;
		});

		return createdAdmin;
	}
}
