import { FilterSchema, SortSchema } from '@common/schemas';
import z from 'zod';

const adminFields = ['firstName', 'lastName', 'email'] as const;

const FilterSchemaLocal = FilterSchema(adminFields, [
	'equals',
	'contains',
	'lt',
	'gt',
	'lte',
	'gte'
] as const);

export const ListAdminsRequestBodySchema = z
	.object({
		skip: z.coerce.number().optional(),
		take: z.coerce.number().optional(),
		sort: SortSchema(adminFields),
		filters: FilterSchemaLocal.optional()
	})
	.strict();
export type ListAdminsRequestBody = z.infer<typeof ListAdminsRequestBodySchema>;
