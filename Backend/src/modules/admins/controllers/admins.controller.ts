import { Body, Controller, Delete, Get, HttpStatus, Patch, Post, Res } from '@nestjs/common';
import type { Response } from 'express';
import { UuidParam, ZodBody, ZodQuery } from 'src/common/decorators/zodSchema.decorator';
import { created, ok } from 'src/common/utils/responses';

import {
	type CreateAdminRequestBody,
	CreateAdminRequestBodySchema,
	type ListAdminsRequestBody,
	ListAdminsRequestBodySchema
} from '../schemas';
import { CreateAdminUseCase, GetAdminUseCase, ListAdminsUseCase } from '../useCases';

@Controller('admins')
export class AdminsController {
	constructor(
		private listAdminsUc: ListAdminsUseCase,
		private createAdminUc: CreateAdminUseCase,
		private getAdminUc: GetAdminUseCase
	) {}

	@Get()
	async listAdmins(
		@ZodQuery(ListAdminsRequestBodySchema) dto: ListAdminsRequestBody,
		@Res() res: Response
	) {
		const admins = await this.listAdminsUc.execute(dto);
		const adminsTransformed = admins.map(
			({ user: { id: _id, passwordHash: _passwordHash, ...user }, ...admin }) => ({
				...admin,
				...user
			})
		);
		return res.status(HttpStatus.OK).json(ok(adminsTransformed));
	}

	@Get(':adminId')
	async getAdmin(@UuidParam('adminId') adminId: string, @Res() res: Response) {
		const admin = await this.getAdminUc.execute(adminId);
		return res.status(HttpStatus.OK).json(ok(admin));
	}

	@Post()
	async createAdmin(
		@ZodBody(CreateAdminRequestBodySchema) dto: CreateAdminRequestBody,
		@Res() res: Response
	) {
		const createdAdmin = await this.createAdminUc.execute(dto);

		res.status(HttpStatus.CREATED).json(created(createdAdmin));
	}

	@Patch(':adminId')
	patchAdmin(
		@UuidParam('adminId') adminId: string,
		@Body() adminData: unknown,
		@Res() res: Response
	) {
		res.sendStatus(501);
	}

	@Delete(':adminId')
	deleteAdmin(@UuidParam('adminId') adminId: string, @Res() res: Response) {
		res.sendStatus(501);
	}
}
