import { AdminsPrismaRepo } from '@modules/admins/admins.prisma.repo';
import { AdminsRepo } from '@modules/admins/admins.repo';
import { AdminsController } from '@modules/admins/controllers/admins.controller';
import { CreateAdminUseCase, GetAdminUseCase, ListAdminsUseCase } from '@modules/admins/useCases';
import { UsersModule } from '@modules/users/users.module';
import { Module } from '@nestjs/common';

import { PrismaModule } from '@/infra/prisma/prisma.module';

@Module({
	imports: [UsersModule, PrismaModule],
	providers: [
		{ provide: AdminsRepo, useClass: AdminsPrismaRepo },
		ListAdminsUseCase,
		CreateAdminUseCase,
		GetAdminUseCase
	],
	controllers: [AdminsController],
	exports: [AdminsRepo]
})
export class AdminsModule {}
