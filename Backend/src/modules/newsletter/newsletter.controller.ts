import {
	BadRequestException,
	Controller,
	Get,
	Post,
	Query,
	Req,
	Res,
	UnauthorizedException
} from '@nestjs/common';
import { NewsletterSubscriber } from '@prisma/client';
import { createHmac, timingSafeEqual } from 'crypto';
import type { Request, Response } from 'express';

import { ZodBody } from '../../common/decorators/zodSchema.decorator';
import { getClientIp, getUserAgent } from '../../common/http/client';
import { NewsletterSubscribeSchema, type NewsletterSubscribeValues } from '../../common/schemas';
import { ok } from '../../common/utils/responses';
import { LinkService } from '../link/link.service';
import { MailerService } from '../mailer/mailer.service';
import { StaticService } from '../static/static.service';
import { SubscribersRepo } from './subscribers.repo';

const UNSUB_SECRET = process.env.UNSUB_SECRET ?? 'dev-secret';

function signUnsub(id: string) {
	return createHmac('sha256', UNSUB_SECRET).update(id).digest('base64url');
}

function verifyUnsub(id: string, mac: string) {
	const expected = signUnsub(id);
	const a = Buffer.from(mac);
	const b = Buffer.from(expected);
	return a.length === b.length && timingSafeEqual(a, b);
}

@Controller('newsletter')
export class NewsletterController {
	constructor(
		private readonly subsRepo: SubscribersRepo,
		private readonly mailer: MailerService,
		private readonly link: LinkService,
		private staticService: StaticService
	) {}

	//TODO: implement locale
	@Post('subscribe')
	async subscribe(
		@ZodBody(NewsletterSubscribeSchema) dto: NewsletterSubscribeValues,
		@Req() req: Request,
		@Res() res: Response
	) {
		try {
			const email = dto.email.trim().toLowerCase();
			const ip = getClientIp(req);
			const ua = getUserAgent(req)?.slice(0, 512) ?? '';
			const existing = await this.subsRepo.findByEmail(email);
			let subscriber: NewsletterSubscriber | null = null;
			let action: 'created' | 'resubscribed' | 'alreadyActive';

			if (!existing) {
				subscriber = await this.subsRepo.create({
					email,
					status: 'active',
					unsubscribedAt: null,
					lastIp: ip,
					lastUserAgent: ua,
					subscribedAt: new Date()
				});
				console.log('subscriber', subscriber);
				action = 'created';
			} else if (existing.status === 'blocked') {
				throw new UnauthorizedException();
			} else if (existing.status === 'unsubscribed') {
				await this.subsRepo.updateById(existing.id, {
					status: 'active',
					unsubscribedAt: null,
					lastIp: ip,
					lastUserAgent: ua
				});
				action = 'resubscribed';
			} else {
				/*
				await this.subsRepo.updateById(existing.id, {
					locale: locale ?? existing.locale ?? null,
					lastIp: ip,
					lastUserAgent: ua
				});
				 */
				action = 'alreadyActive';
			}

			if (action === 'created' && subscriber) {
				const pdfBuffer1 = await this.staticService.getFileAsBuffer('files/t-cj.pdf');
				const pdfBuffer2 = await this.staticService.getFileAsBuffer('files/t-ma.pdf');

				const attachments = [
					{
						filename: 't-cesky-jazyk.pdf',
						content: pdfBuffer1,
						contentType: 'application/pdf',
						disposition: 'attachment' as const
					},
					{
						filename: 't-matematika.pdf',
						content: pdfBuffer2,
						contentType: 'application/pdf',
						disposition: 'attachment' as const
					}
				];

				const mac = signUnsub(subscriber.id);
				/*
				const apiLink = this.link.build('/api/newsletter/unsubscribe', {
					i: subscriber.id,
					m: mac
				});
				*/

				const unsubscribeUrl = this.link.build('/unsubscribe', {
					i: subscriber.id,
					m: mac
				});
				await this.mailer.sendTemplate(
					{ email },
					'newsletter.welcome',
					'cs',
					{
						unsubscribeUrl
					},
					attachments
				);
			}
		} catch (err) {
			console.log(err);
		}
		return res.status(200).json(ok(undefined));
	}

	@Get('unsubscribe')
	async unsubscribe(@Res() res: Response, @Query('i') id?: string, @Query('m') mac?: string) {
		if (!id || !mac || !verifyUnsub(id, mac)) {
			throw new BadRequestException();
		}
		const row = await this.subsRepo.findById(id);
		if (!row) {
			return res.status(200).json(ok(undefined));
		}
		if (row.status !== 'blocked') {
			await this.subsRepo.updateById(row.id, {
				status: 'unsubscribed',
				unsubscribedAt: new Date()
			});
		}
		return res.status(200).json(ok(undefined));
	}
}
