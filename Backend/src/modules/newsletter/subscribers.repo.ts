import { NewsletterSubscriber, Prisma } from '@prisma/client';

export abstract class SubscribersRepo {
	abstract findByEmail(email: string): Promise<NewsletterSubscriber | null>;
	abstract findById(id: string): Promise<NewsletterSubscriber | null>;
	abstract create(
		row: Prisma.NewsletterSubscriberUncheckedCreateInput
	): Promise<NewsletterSubscriber>;
	abstract updateById(
		id: string,
		patch: Prisma.NewsletterSubscriberUncheckedUpdateInput
	): Promise<void>;
}
