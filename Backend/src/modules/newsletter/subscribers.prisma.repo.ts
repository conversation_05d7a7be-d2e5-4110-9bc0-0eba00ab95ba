import { Injectable } from '@nestjs/common';
import { NewsletterSubscriber, Prisma } from '@prisma/client';

import { PrismaService } from '../../infra/prisma/prisma.service';
import { SubscribersRepo } from './subscribers.repo';

@Injectable()
export class SubscribersPrismaRepo implements SubscribersRepo {
	constructor(private prisma: PrismaService) {}

	async findByEmail(email: string): Promise<NewsletterSubscriber | null> {
		return await this.prisma.newsletterSubscriber.findUnique({
			where: {
				email
			}
		});
	}

	async findById(id: string): Promise<NewsletterSubscriber | null> {
		return await this.prisma.newsletterSubscriber.findFirst({
			where: {
				id,
				unsubscribedAt: null
			}
		});
	}

	async create(row: Prisma.NewsletterSubscriberCreateInput): Promise<NewsletterSubscriber> {
		return await this.prisma.newsletterSubscriber.create({
			data: row
		});
	}

	async updateById(id: string, patch: Prisma.NewsletterSubscriberUpdateInput): Promise<void> {
		await this.prisma.newsletterSubscriber.update({
			where: {
				id
			},
			data: patch
		});
	}
}
