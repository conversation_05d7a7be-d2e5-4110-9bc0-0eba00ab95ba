// gen-ed25519.js
const { generateKeyPair, exportJWK } = require('jose');

function toBase64Url(input) {
    const b64 = Buffer.from(input, 'utf8').toString('base64');
    return b64.replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
}

(async () => {
    const kid = `${new Date().toISOString().slice(0, 7)}-ed${Math.random().toString(36).slice(2, 5)}`;

    const { publicKey, privateKey } = await generateKeyPair('EdDSA', {
        crv: 'Ed25519',
        extractable: true,
    });

    const pub = await exportJWK(publicKey);
    const priv = await exportJWK(privateKey);

    const jwk = {
        ...priv,
        kid,
        alg: 'EdDSA',
        use: 'sig',
        kty: 'OKP',
        crv: 'Ed25519',
        x: pub.x,
    };

    const json = JSON.stringify({ [kid]: jwk });
    const b64 = toBase64Url(json);

    console.log('---');
    console.log('KID:', kid);
    console.log('JSON (pretty):');
    console.log(JSON.stringify({ [kid]: jwk }, null, 2));
    console.log('---');
    console.log('Put into JWKS_PRIVATE_SET_B64URL:');
    console.log(b64);
    console.log('---');
})().catch((e) => {
    console.error(e);
    process.exit(1);
});
