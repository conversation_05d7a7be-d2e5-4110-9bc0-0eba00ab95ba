{"name": "be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build && tsc-alias -p tsconfig.build.json", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "nest start --watch -r tsconfig-paths/register", "start:debug": "nest start --debug --watch -r tsconfig-paths/register", "start": "node -r tsconfig-paths/register dist/main", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:gen": "prisma generate", "prisma:studio": "prisma studio", "prisma:migrate": "prisma migrate dev", "genkey": "node genKey.js"}, "dependencies": {"@casl/ability": "^6.7.3", "@google-cloud/storage": "^7.17.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.15.0", "argon2": "^0.44.0", "cookie": "^1.0.2", "cookie-parser": "^1.4.7", "dotenv": "^17.2.1", "helmet": "^8.1.0", "jose": "^6.0.12", "lodash": "^4.17.21", "mailersend": "^2.6.0", "mjml": "^4.15.3", "moment": "^2.30.1", "multer": "^2.0.2", "multer-cloud-storage": "^4.1.0", "nodemailer": "^7.0.5", "pg": "^8.16.3", "prisma": "^6.15.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "twig": "^1.17.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.10", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/cookie": "^0.6.0", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/pg": "^8.15.5", "@types/supertest": "^6.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^16.0.0", "jest": "^30.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}