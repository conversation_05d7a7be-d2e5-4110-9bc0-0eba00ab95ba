-- CreateEnum
CREATE TYPE "public"."CreditTransactionReason" AS ENUM ('manual_adjustment', 'lecture_cancellation', 'credit_purchase', 'lecture_purchase');

-- CreateEnum
CREATE TYPE "public"."CreditTransactionDirection" AS ENUM ('credit', 'debit');

-- CreateTable
CREATE TABLE "public"."CreditWallet" (
    "id" UUID NOT NULL,
    "studentId" UUID NOT NULL,
    "balance" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CreditWallet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CreditTransaction" (
    "id" UUID NOT NULL,
    "walletId" UUID NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "balanceAfter" DECIMAL(10,2) NOT NULL,
    "reason" "public"."CreditTransactionReason" NOT NULL,
    "direction" "public"."CreditTransactionDirection" NOT NULL,
    "reasonMetadata" TEXT,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CreditTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CreditWallet_studentId_idx" ON "public"."CreditWallet"("studentId");

-- CreateIndex
CREATE UNIQUE INDEX "CreditWallet_studentId_key" ON "public"."CreditWallet"("studentId");

-- CreateIndex
CREATE INDEX "CreditTransaction_walletId_idx" ON "public"."CreditTransaction"("walletId");

-- AddForeignKey
ALTER TABLE "public"."CreditWallet" ADD CONSTRAINT "CreditWallet_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "public"."Student"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CreditTransaction" ADD CONSTRAINT "CreditTransaction_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "public"."CreditWallet"("id") ON DELETE NO ACTION ON UPDATE CASCADE;
