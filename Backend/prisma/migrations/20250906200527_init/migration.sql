-- CreateTable
CREATE TABLE "public"."BecomeTeacher" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "school" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BecomeTeacher_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BecomeTeacher_email_key" ON "public"."BecomeTeacher"("email");
