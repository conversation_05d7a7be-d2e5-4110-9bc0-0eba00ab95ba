-- CreateEnum
CREATE TYPE "public"."LocationPurpose" AS ENUM ('prijimacky_nanecisto', 'prezencni_doucovani');

-- CreateEnum
CREATE TYPE "public"."LectureType" AS ENUM ('online', 'onsite', 'test');

-- CreateTable
CREATE TABLE "public"."Location" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "rentalCost" DECIMAL(10,2),
    "capacity" INTEGER NOT NULL,
    "purpose" "public"."LocationPurpose" NOT NULL,
    "imgKey" TEXT,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Lecture" (
    "id" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdById" UUID NOT NULL,
    "updatedById" UUID NOT NULL,
    "groupId" UUID NOT NULL,
    "type" "public"."LectureType" NOT NULL,
    "lang" "public"."Lang" NOT NULL,
    "name" TEXT NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "dateStart" TIMESTAMPTZ NOT NULL,
    "dateEnd" TIMESTAMPTZ NOT NULL,
    "notes" TEXT,
    "capacity" INTEGER,
    "cancelledAt" TIMESTAMPTZ,
    "cancelledById" UUID,
    "locationId" UUID,
    "teacherId" UUID,
    "teacherAssignedAt" TIMESTAMPTZ,

    CONSTRAINT "Lecture_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."LectureReservation" (
    "id" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lectureId" UUID NOT NULL,
    "studentId" UUID NOT NULL,
    "deleted" TIMESTAMPTZ,

    CONSTRAINT "LectureReservation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."_LocationToManager" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_LocationToManager_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "public"."_LocationToTeacher" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_LocationToTeacher_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "Lecture_type_idx" ON "public"."Lecture"("type");

-- CreateIndex
CREATE INDEX "Lecture_locationId_idx" ON "public"."Lecture"("locationId");

-- CreateIndex
CREATE INDEX "Lecture_groupId_idx" ON "public"."Lecture"("groupId");

-- CreateIndex
CREATE INDEX "LectureReservation_studentId_idx" ON "public"."LectureReservation"("studentId");

-- CreateIndex
CREATE INDEX "LectureReservation_lectureId_idx" ON "public"."LectureReservation"("lectureId");

-- CreateIndex
CREATE UNIQUE INDEX "LectureReservation_lectureId_studentId_key" ON "public"."LectureReservation"("lectureId", "studentId");

-- CreateIndex
CREATE INDEX "_LocationToManager_B_index" ON "public"."_LocationToManager"("B");

-- CreateIndex
CREATE INDEX "_LocationToTeacher_B_index" ON "public"."_LocationToTeacher"("B");

-- AddForeignKey
ALTER TABLE "public"."Lecture" ADD CONSTRAINT "Lecture_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Lecture" ADD CONSTRAINT "Lecture_updatedById_fkey" FOREIGN KEY ("updatedById") REFERENCES "public"."User"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Lecture" ADD CONSTRAINT "Lecture_cancelledById_fkey" FOREIGN KEY ("cancelledById") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Lecture" ADD CONSTRAINT "Lecture_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "public"."Location"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Lecture" ADD CONSTRAINT "Lecture_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "public"."Teacher"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."LectureReservation" ADD CONSTRAINT "LectureReservation_lectureId_fkey" FOREIGN KEY ("lectureId") REFERENCES "public"."Lecture"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."LectureReservation" ADD CONSTRAINT "LectureReservation_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "public"."Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_LocationToManager" ADD CONSTRAINT "_LocationToManager_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."Location"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_LocationToManager" ADD CONSTRAINT "_LocationToManager_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."Manager"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_LocationToTeacher" ADD CONSTRAINT "_LocationToTeacher_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."Location"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_LocationToTeacher" ADD CONSTRAINT "_LocationToTeacher_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."Teacher"("id") ON DELETE CASCADE ON UPDATE CASCADE;
