import type { Handle } from '@sveltejs/kit';
import { createRemoteJWKSet, decodeProtectedHeader, errors, jwtVerify } from 'jose';

import { getCachedRuleset, getRulesForRole } from '$lib/permissions';
import type { AppUser } from '$lib/stores/user';

//TODO: Prod from env
const url = new URL(process.env.JWKS_PUBLIC_URL! || 'http://backend:3000/.well-known/jwks.json');
const JWKS = createRemoteJWKSet(url);
const ISS = 'http://localhost:3000';
const AUD = 'api://local';

export const handle: Handle = async ({ event, resolve }) => {
	event.locals.user = null;
	const at = event.cookies.get('at');
	event.locals.refreshPresent = !!event.cookies.get('rt_present');

	const ruleset = await getCachedRuleset(event.fetch);

	if (at) {
		try {
			const { payload } = await jwtVerify<AppUser>(at, JWKS, {
				issuer: ISS,
				audience: AUD,
				clockTolerance: 60
			});

			const rules = getRulesForRole(ruleset, payload.role);

			event.locals.user = {
				sub: payload.sub,
				sid: payload.sid,
				permissions: payload.permissions ?? [],
				permsVer: payload.permsVer ?? 1,
				role: payload.role,

				// TODO: Separate from main JWT
				firstname: payload.firstname,
				lastname: payload.lastname,
				email: payload.email,

				rules
			};
		} catch (err: unknown) {
			if (err instanceof errors.JWKSNoMatchingKey) {
				const { kid, alg } = decodeProtectedHeader(at);
				console.warn('AT kid not in JWKS', { kid, alg });

				// TODO: somehow from config :p
				event.cookies.delete('at', {
					path: '/'
				});
			} else {
				console.error(err);
			}
		}
	}

	return resolve(event);
};
