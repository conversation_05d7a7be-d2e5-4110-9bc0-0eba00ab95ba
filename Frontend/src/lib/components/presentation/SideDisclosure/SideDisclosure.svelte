<script lang="ts">
	import { ChevronLeft } from '@lucide/svelte';
	import { onMount } from 'svelte';
	import { fly } from 'svelte/transition';

	import { browser } from '$app/environment';
	import { clickOutside } from '$lib/appmaxx/svelte-actions';
	import { exists } from '$lib/appmaxx/util';
	import { Button, Flex, Heading, Spacer, Text } from '$lib/components';
	import { T, t } from '$lib/translations/config';

	import { sideDisclosureHide } from './use.side-disclosure-hide';

	let isOpen = $state(false);
	let isHidden = $state(false);
	let mounted = $state(false);

	const toggleDisclosure = () => {
		isOpen = !isOpen;
	};

	const handleDisclosurePanelClick = () => {
		if (!isOpen) isOpen = true;
	};

	const handleClickOutside = () => {
		if (isOpen) {
			isOpen = false;
		}
	};

	const handleHide = (event: CustomEvent<boolean>) => {
		isHidden = event.detail;
	};

	const handleButtonClick = (e: Event) => {
		e.stopPropagation();
		toggleDisclosure();

		const newsletterSubscribeElement = document.getElementById('newsletter-subscribe');
		if (!exists(newsletterSubscribeElement)) return;

		newsletterSubscribeElement.scrollIntoView({
			behavior: 'smooth',
			block: 'center'
		});
	};

	onMount(() => {
		if (browser) {
			setTimeout(() => (mounted = true), 500);
		}
	});
</script>

{#if mounted}
	<div
		class="side-disclosure"
		class:opened={isOpen}
		class:hidden={isHidden}
		use:clickOutside
		use:sideDisclosureHide
		onclickoutside={handleClickOutside}
		onhide={handleHide}
		role="application"
		aria-labelledby="side-disclosure-label"
		in:fly={{ x: 20, duration: 300 }}
		aria-expanded={isOpen}
	>
		<button
			onclick={toggleDisclosure}
			aria-controls="side-disclosure-content"
			aria-expanded={isOpen}
			aria-label={$t('header.sideDisclosure.label')}
		>
			<span class="icon-container" class:opened={isOpen}>
				<ChevronLeft size="1.25rem" strokeWidth={2} />
			</span>
		</button>

		<!-- svelte-ignore a11y_click_events_have_key_events -->
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<div
			class="disclosure-content-wrapper"
			onclick={handleDisclosurePanelClick}
			class:opened={isOpen}
		>
			<span id="side-disclosure-label" class="disclosure-label" class:hidden={isOpen}>
				<Heading as="h4" size="4">
					<T key="header.sideDisclosure.label" />
				</Heading>
			</span>

			<div
				id="side-disclosure-content"
				class="disclosure-content-container"
				class:hidden={!isOpen}
			>
				<Flex direction="col" align="start" paddingY="ml" paddingX="l">
					<Heading as="h4" size="4">
						<T key="header.sideDisclosure.title" />
					</Heading>
					<Spacer direction="vertical" size="m" />

					<Text>
						<T key="header.sideDisclosure.text" />
					</Text>

					<Spacer direction="vertical" size="l" />

					<Button as="a" onclick={handleButtonClick} size="medium">
						{$t('header.sideDisclosure.button')}
					</Button>
				</Flex>
			</div>
		</div>
	</div>
{/if}

<style lang="scss">
	.side-disclosure {
		position: fixed;
		z-index: var(--side-disclosure-z-index);
		top: 33%;
		right: 0;
		display: flex;
		align-items: start;
		transform: translateY(-50%);
		color: var(--color-light);
		translate: calc(min(90dvw, 300px) - 2.5rem) 0;
		transition: translate 0.3s ease;

		&.opened {
			translate: 0 0;
		}

		&:not(.opened).hidden {
			translate: min(90dvw, 300px) 0;
		}

		button {
			border: none;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 2.5rem;
			height: 2.5rem;
			transition: background-color 0.2s ease;
			background-color: var(--color-dark);
			color: var(--color-light);
			border-radius: var(--border-radius-s) 0 0 var(--border-radius-s);

			&:hover {
				background-color: var(--color-dark-hover);
			}

			.icon-container {
				display: flex;
				transition: transform 0.2s ease;
				transform: rotate(0deg);

				&.opened {
					transform: rotate(180deg);
				}
			}
		}

		.disclosure-content-wrapper {
			position: relative;
			background-color: var(--color-dark);
			width: 100%;
			overflow: hidden;
			max-width: min(90dvw, 300px);
			border-bottom-left-radius: var(--border-radius-s);

			&:not(.opened) {
				cursor: pointer;
			}

			.disclosure-label {
				position: absolute;
				transform: translate(-50%, -50%) rotate(90deg);
				top: 50%;
				left: 1.33rem;
				transition:
					opacity 0.3s ease,
					translate 0.3s ease;

				&.hidden {
					opacity: 0;
					translate: calc(-50% - 2.5rem) -50%;
				}
			}

			.disclosure-content-container {
				opacity: 1;
				translate: 0 0;
				transition:
					opacity 0.3s ease,
					translate 0.3s ease;

				&.hidden {
					opacity: 0;
					translate: 2.5rem 0;
				}
			}
		}
	}
</style>
