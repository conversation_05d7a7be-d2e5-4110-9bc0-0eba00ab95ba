import type { ActionReturn } from 'svelte/action';

import { breakpointDesktopMin } from '$lib/globals';

interface Attributes {
	onhide?: (e: CustomEvent<boolean>) => void;
}

export function sideDisclosureHide(node: HTMLElement): ActionReturn<unknown, Attributes> {
	const checkIntersection = () => {
		if (window.innerWidth >= breakpointDesktopMin) return;

		const nodeRect = node.getBoundingClientRect();

		const fullscreenContainers = Array.from(
			document.querySelectorAll<HTMLElement>('[class*="container-overflow-fullscreen"]')
		);

		const hideForContainers = [...fullscreenContainers];

		const intersects = hideForContainers.some((el) => {
			const elRect = el.getBoundingClientRect();
			return !(
				nodeRect.bottom < elRect.top ||
				nodeRect.top > elRect.bottom ||
				nodeRect.right < elRect.left ||
				nodeRect.left > elRect.right
			);
		});

		node.dispatchEvent(new CustomEvent('hide', { detail: intersects }));
	};

	window.addEventListener('scroll', checkIntersection, { passive: true });
	window.addEventListener('resize', checkIntersection);

	checkIntersection();

	return {
		destroy() {
			window.removeEventListener('scroll', checkIntersection);
			window.removeEventListener('resize', checkIntersection);
		}
	};
}
