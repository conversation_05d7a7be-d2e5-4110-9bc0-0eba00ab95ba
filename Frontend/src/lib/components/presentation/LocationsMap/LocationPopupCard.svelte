<script lang="ts">
	import { CalendarPlus, X } from '@lucide/svelte';

	import { Button, Card, Flex, Heading, Image, Spacer, Text } from '$lib/components';
	import type { Lecture, Location } from '$lib/components/presentation/LocationsMap/LocationsMap';
	import { T } from '$lib/translations/config';

	let { location, onClose } = $props<{
		location: Location;
		onClose?: () => void;
	}>();

	function groupLecturesByDayOfWeek(
		lectures: Lecture[]
	): Record<Lecture['dayOfWeek'], Lecture[]> {
		return lectures.reduce(
			(acc, lecture) => {
				if (!acc[lecture.dayOfWeek]) {
					acc[lecture.dayOfWeek] = [];
				}
				acc[lecture.dayOfWeek].push(lecture);
				return acc;
			},
			{} as Record<Lecture['dayOfWeek'], Lecture[]>
		);
	}
</script>

<Card variant="dark" paddingX="l" paddingY="ml" style="width: min(1000px, 100%);">
	<Flex direction="row" align="center">
		<Button variant="light" size="small" onclick={onClose}>
			<X size="1.25rem" strokeWidth={2} />
		</Button>

		<Spacer direction="horizontal" size="m" />

		<Heading as="h3" size="4">{location.name}</Heading>
	</Flex>

	<Spacer direction="vertical" size="ml" />

	<Flex direction="row" align="start" gap="l">
		<Image
			src={location.mainPhotoUrl}
			alt={location.name}
			aspectRatio="1/1"
			width="128px"
			height="128px"
			style="border-radius: 0.75rem;"
		/>

		<Flex direction="col" align="start">
			<Heading as="h4" size="4">
				<T key="locationsMap.popupCard.address" />
			</Heading>
			<Spacer direction="vertical" size="s" />

			<Text as="p">{location.address}</Text>
			<Spacer direction="vertical" size="m" />

			<Flex direction="col" gap="m">
				{#each Object.entries(groupLecturesByDayOfWeek(location.lectures)) as [dayOfWeek, lectures] (dayOfWeek)}
					<Flex direction="col">
						<Heading as="h4" size="4">
							<T key="locationsMap.popupCard.days.{dayOfWeek}" />
						</Heading>
						<Spacer direction="vertical" size="s" />

						{#each lectures as lecture (lecture.id)}
							<Text as="p"
								>{lecture.name} ({lecture.startsAt} - {lecture.endsAt})</Text
							>
						{/each}
					</Flex>
				{/each}
			</Flex>

			<Spacer direction="vertical" size="l" />
			<Button as="a" href="/prihlaseni" size="medium" Icon={CalendarPlus}>
				<T key="locationsMap.popupCard.buttonLabel" />
			</Button>
			<Spacer direction="vertical" size="s" />
		</Flex>
	</Flex>
</Card>
