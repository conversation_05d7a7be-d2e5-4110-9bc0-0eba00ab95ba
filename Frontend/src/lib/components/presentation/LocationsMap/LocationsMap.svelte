<script lang="ts">
	import 'mapbox-gl/dist/mapbox-gl.css';

	import mapboxgl from 'mapbox-gl';
	import { onMount } from 'svelte';
	import { fly } from 'svelte/transition';

	import { browser } from '$app/environment';
	import { clickOutside } from '$lib/appmaxx/svelte-actions';
	import { exists } from '$lib/appmaxx/util';
	import { Card, Flex, Loader, Spacer } from '$lib/components';
	import {
		initLocationsMap,
		loadLocations
	} from '$lib/components/presentation/LocationsMap/map-utils';

	import { animateOnScroll } from '../use.animate-on-scroll';
	import LocationPopupCard from './LocationPopupCard.svelte';
	import LocationSearchInput from './LocationSearchInput.svelte';
	import { type Location, type LocationsMapProps } from './LocationsMap';
	import mapMarker from './map-marker.svg?raw';

	let { withoutSearch = false, withoutPageOverflow = false }: LocationsMapProps = $props();

	let selectedLocation = $state<Location | null>(null);
	let initializing = $state(true);
	let map: mapboxgl.Map;

	let locations = $state<Location[]>([]);
	let mapContainer: HTMLDivElement;

	onMount(async () => {
		if (!browser) return;

		setTimeout(initializeMap, 1500);
	});

	async function initializeMap() {
		if (!exists(mapContainer)) return;

		locations = await loadLocations();

		map = initLocationsMap({
			container: mapContainer,
			locations,
			mapMarkerImgSrc: mapMarker,
			handleLocationSelect
		});

		initializing = false;
	}

	const handleLocationSelect = (location: Location) => {
		if (!exists(map)) return;
		selectedLocation = location;
		map.flyTo({ center: [location.longitude, location.latitude], zoom: 12 });
	};

	const handleClickOutsideLocationPopup = () => {
		selectedLocation = null;
	};
</script>

{#if !withoutSearch}
	<Flex direction="col" use={[animateOnScroll]}>
		<LocationSearchInput {locations} onLocationSelect={handleLocationSelect} />
	</Flex>

	<Spacer direction="vertical" size="xxl" />
{/if}

<Card
	use={[animateOnScroll]}
	class="locations-map-card"
	border="light"
	paddingX="none"
	paddingY="none"
	borderRadius={{ mobile: 'none', tablet: 'l', desktop: 'xl' }}
	pageContainerOverflow={withoutPageOverflow
		? 'none'
		: { mobile: 'fullscreen', tablet: 'fullscreen', desktop: 'subtle' }}
	style="isolate: isolate; position: relative;"
>
	{#if initializing}
		<Flex
			justify="center"
			align="center"
			style="height: 100%; width: 100%; top: 50%; left: 50%; translate: -50% -50%; position: absolute;"
		>
			<Loader />
		</Flex>
	{/if}

	{#if exists(selectedLocation)}
		<div
			in:fly={{ y: 5, duration: 300 }}
			out:fly={{ y: 5, duration: 300 }}
			class="location-popup-card-container"
			use:clickOutside
			onclickoutside={handleClickOutsideLocationPopup}
		>
			<LocationPopupCard
				location={selectedLocation}
				onClose={handleClickOutsideLocationPopup}
			/>
		</div>
	{/if}

	<div bind:this={mapContainer} class="map"></div>
</Card>

<style lang="scss">
	.location-popup-card-container {
		position: absolute;
		top: 50%;
		left: 50%;
		translate: -50% -50%;
		z-index: 10;
	}

	.map {
		height: min(60vh, 70vh);
		width: 100%;
		position: relative;

		:global {
			.mapboxgl-ctrl {
				box-shadow: none;
				background-color: transparent;
				display: flex;
				flex-direction: column;
				gap: var(--spacing-xs);
				z-index: 9;
			}

			.mapboxgl-ctrl-zoom-in,
			.mapboxgl-ctrl-zoom-out {
				position: relative;
				width: 2.5rem;
				height: 2.5rem;
				border-radius: 50%;
				color: var(--color-light);
				background-color: var(--color-dark);
				transition: opacity 0.2s ease;
				font-size: 1.5rem;
				line-height: 0;
				cursor: pointer;
				display: flex;

				&:hover {
					opacity: 0.8;
					background-color: var(--color-dark-hover);
				}

				&:after {
					position: absolute;
					top: 50%;
					left: 50%;
					translate: -50% -50%;
					font-size: 1.25rem;
					padding-bottom: 3px;
					line-height: 0;
				}

				span {
					display: none;
				}
			}

			.mapboxgl-ctrl-zoom-in:after {
				padding-bottom: 4px;
				content: '+';
			}

			.mapboxgl-ctrl-zoom-out:after {
				content: '-';
			}

			.map-location-marker {
				transition: opacity 0.2s ease;

				&:hover {
					opacity: 0.8;
				}
			}
		}
	}
</style>
