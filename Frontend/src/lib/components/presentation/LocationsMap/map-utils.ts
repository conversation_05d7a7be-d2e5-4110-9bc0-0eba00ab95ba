import mapboxgl from 'mapbox-gl';

import type {
	DayOfWeek,
	LectureJson,
	Location,
	LocationJson
} from '$lib/components/presentation/LocationsMap/LocationsMap';

interface LocationsMapConfig {
	container: HTMLDivElement;
	locations: Location[];
	mapMarkerImgSrc: string;
	handleLocationSelect: (location: Location) => void;
}

export function initLocationsMap({
	container,
	locations,
	mapMarkerImgSrc,
	handleLocationSelect
}: LocationsMapConfig): mapboxgl.Map {
	mapboxgl.accessToken =
		'pk.eyJ1Ijoic3Rhc3RueS1qYWt1YiIsImEiOiJjbWYwYm93b2gwZTFtMmxzZ2Q4MzM4MjAwIn0.Ri9PVSACYMJDZG-Ma-KI3Q';

	const map = new mapboxgl.Map({
		container: container,
		style: 'mapbox://styles/mapbox/standard',
		center: [50.260308, 14.51691],
		zoom: 3,
		scrollZoom: false,
		maxBounds: [
			[12.09, 48.55],
			[18.86, 51.06]
		],
		maxZoom: 12,
		minZoom: 6,
		pitch: 0,
		bearing: 0,
		config: {
			basemap: {
				show3dObjects: false,
				showPedestrianRoads: false,
				lightPreset: 'light'
			}
		}
	});

	map.setMaxPitch(0);
	map.setMinPitch(0);
	map.touchPitch?.disable();
	map.dragRotate.disable();
	map.touchZoomRotate.disableRotation();

	map.addControl(new mapboxgl.NavigationControl({ showCompass: false }), 'top-left');

	map.on('load', () => {
		locations.forEach((location) => {
			const el = document.createElement('div');
			el.className = 'map-location-marker';
			el.innerHTML = mapMarkerImgSrc;
			el.style.width = '32px';
			el.style.height = '32px';
			el.onclick = () => handleLocationSelect(location);

			new mapboxgl.Marker({ element: el, anchor: 'bottom' })
				.setLngLat([location.longitude, location.latitude])
				.addTo(map);
		});
	});

	return map;
}

const locationsApiUrl = 'https://api.dej-prijimacky.cz/api/locations/get-all';
const lecturesApiUrl = 'https://api.dej-prijimacky.cz/api/lectures/get-all';

export async function loadLocations(): Promise<Location[]> {
	let locationsJson: LocationJson[];
	let lecturesJson: LectureJson[];

	try {
		locationsJson = (await fetchJson<{ list: LocationJson[] }>(locationsApiUrl)).list;
		lecturesJson = (await fetchJson<{ list: LectureJson[] }>(lecturesApiUrl)).list;

		return normalizeMapLocations(locationsJson, lecturesJson);
	} catch (error) {
		console.error('Failed to load locations:', error);
		return [];
	}
}

async function fetchJson<T>(url: string): Promise<T> {
	const res = await fetch(url);
	return await res.json();
}

function normalizeMapLocations(locations: LocationJson[], lectures: LectureJson[]): Location[] {
	const findLecturesForLocation = (locationId: number) =>
		lectures
			.filter((lecture) => lecture.location_id === locationId)
			.map((lecture) => ({
				id: lecture.id,
				name: lecture.name,
				dayOfWeek: lecture.day_of_week as DayOfWeek,
				startsAt: lecture.starts_at,
				endsAt: lecture.ends_at,
				startDate: lecture.start_date,
				finalDate: lecture.final_date,
				managerId: lecture.manager_id
			}));

	return locations.map((location) => ({
		id: location.id,
		name: location.name,
		address: location.address,
		description: location.description,
		capacity: location.capacity,
		prices: {
			per1lecture: location.price_per_lecture,
			per5lectures: location.price_per_5_lectures,
			per10lectures: location.price_per_10_lectures,
			per20lectures: location.price_per_20_lectures
		},
		latitude: parseFloat(location.latitude),
		longitude: parseFloat(location.longitude),
		mainPhotoName: location.main_photo_name,
		mainPhotoUrl: location.main_photo_url ?? null,
		managerId: location.manager_id ?? null,
		lectures: findLecturesForLocation(location.id)
	}));
}
