<script lang="ts">
	import { flip, offset, shift } from '@floating-ui/dom';
	import { Search } from '@lucide/svelte';
	import { createFloatingActions } from 'svelte-floating-ui';

	import { Flex, Input } from '$lib/components';
	import type { Location } from '$lib/components/presentation/LocationsMap/LocationsMap';
	import PopoverContent from '$lib/components/shared/primitives/Popover/PopoverContent.svelte';

	let {
		locations,
		onLocationSelect
	}: {
		locations: Location[];
		onLocationSelect: (location: Location) => void;
	} = $props();

	const [floatingRef, floatingContent] = createFloatingActions({
		strategy: 'fixed',
		placement: 'bottom-start',
		middleware: [offset(12), flip(), shift()]
	});

	let value = $state('');

	let filteredLocations = $derived.by(() => {
		if (value.length === 0) return [];

		return locations.filter((location) =>
			location.name.toLowerCase().includes(value.toLowerCase())
		);
	});

	let isOpen = $derived.by(() => value.length > 0 && filteredLocations.length > 0);

	const handleClickOutside = () => {
		isOpen = false;
	};

	const handleItemClick = (location: Location) => {
		onLocationSelect(location);
		value = location.name;
		isOpen = false;
	};
</script>

<Flex direction="row" justify="center">
	<Flex flex="1" style="max-width: 1000px;">
		<Input
			Icon={Search}
			size="large"
			placeholder="Hledat Vaše město..."
			bind:value
			use={[floatingRef]}
			fullWidth="true"
			rounded="true"
		/>

		<PopoverContent {floatingContent} {isOpen} {handleClickOutside}>
			<Flex as="ul" direction="col" gap="m">
				{#each filteredLocations as location (location.id)}
					<li class="location-item">
						<button onclick={() => handleItemClick(location)}>
							{location.name}
						</button>
					</li>
				{/each}
			</Flex>
		</PopoverContent>
	</Flex>
</Flex>

<style lang="scss">
	@use '$lib/assets/styles/hoverable' as hoverable;

	.location-item {
		@include hoverable.hoverable();

		button {
			padding: var(--spacing-xs) var(--spacing-s);
			border: none;
			background: none;
			cursor: pointer;
			text-align: left;
			width: 100%;
			height: 100%;
			font-size: var(--font-size-text);
		}
	}
</style>
