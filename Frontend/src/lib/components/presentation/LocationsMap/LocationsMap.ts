export type DayOfWeek = 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun';

export interface LocationsMapProps {
	withoutSearch?: boolean;
	withoutPageOverflow?: boolean;
}

export interface LocationJson {
	id: number;
	name: string;
	address: string;
	description: string | null;
	capacity: number;
	price_per_lecture: number;
	price_per_5_lectures: number;
	price_per_10_lectures: number;
	price_per_20_lectures: number;
	main_photo_name: string | null;
	latitude: string;
	longitude: string;
	manager_id: number | null;
	main_photo_url?: string;
}

export interface LectureJson {
	id: number;
	name: string;
	day_of_week: string;
	starts_at: string;
	ends_at: string;
	start_date: string | null;
	final_date: string;
	location_id: number;
	manager_id: number | null;
}

export interface Lecture {
	id: number;
	name: string;
	dayOfWeek: DayOfWeek;
	startsAt: string;
	endsAt: string;
	startDate: string | null;
	finalDate: string;
	managerId: number | null;
}

export interface Location {
	id: number;
	name: string;
	address: string;
	description: string | null;
	capacity: number;
	prices: {
		per1lecture: number;
		per5lectures: number;
		per10lectures: number;
		per20lectures: number;
	};
	latitude: number;
	longitude: number;
	mainPhotoName: string | null;
	mainPhotoUrl: string | null;
	lectures: Lecture[];
}
