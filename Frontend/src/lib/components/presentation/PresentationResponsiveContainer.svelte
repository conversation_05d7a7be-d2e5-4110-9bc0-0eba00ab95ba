<script lang="ts">
	import type { Snippet } from 'svelte';

	let { children, grow } = $props<{
		children: Snippet;
		grow?: boolean;
	}>();
</script>

<div class="container" class:grow>
	{@render children?.()}
</div>

<style lang="scss">
	$breakpoint-mobile-max: 639px;
	$breakpoint-tablet-min: 640px;
	$breakpoint-tablet-max: 1023px;
	$breakpoint-desktop-min: 1024px;

	.container {
		width: 100%;
		margin: 0 auto;
		font-size: var(--font-size-text);
		max-width: var(--max-content-width);
		display: flex;
		flex-direction: column;

		@media screen and (max-width: $breakpoint-mobile-max) {
			padding: 0 var(--mobile-side-padding);
		}

		@media screen and (min-width: $breakpoint-tablet-min) and (max-width: $breakpoint-tablet-max) {
			padding: 0 var(--tablet-side-padding);
		}

		@media screen and (min-width: $breakpoint-desktop-min) {
			padding: 0 var(--desktop-side-padding);
		}

		&.grow {
			flex-grow: 1;
		}
	}
</style>
