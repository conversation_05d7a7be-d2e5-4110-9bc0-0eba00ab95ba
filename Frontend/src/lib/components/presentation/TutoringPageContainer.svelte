<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';
	import desktopMonitor from '$lib/assets/img/drawn-icons/desktop-monitor.svg';
	import penPaper from '$lib/assets/img/drawn-icons/pen-paper.svg';
	import peopleInherit from '$lib/assets/img/drawn-icons/people-inherit.svg';
	import personFramed from '$lib/assets/img/drawn-icons/person-framed.svg';
	import smileyThumbsUp from '$lib/assets/img/drawn-icons/smiley-thumbs-up.svg';
	import teamwork from '$lib/assets/img/drawn-icons/teamwork.svg';
	import {
		Button,
		Callout,
		Card,
		Flex,
		Grid,
		Heading,
		Image,
		LocationsMap,
		Spacer,
		Text
	} from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import Chip from '$lib/components/shared/primitives/Chip/Chip.svelte';
	import { T, t } from '$lib/translations/config';

	let {
		heading,
		chips,
		featureCardKeyStart,
		heroDescription,
		pageDescription,
		action,
		imageSrc,
		comingSoon,
		map = false,
		mapTitle,
		mapSubtitle
	}: {
		heading: string;
		chips: string[];
		featureCardKeyStart: string;
		heroDescription: string;
		pageDescription: string;
		action: string;
		imageSrc: string;
		comingSoon: string;
		map?: boolean;
		mapTitle?: string;
		mapSubtitle?: string;
	} = $props();
</script>

<Callout>
	<T providedValue={comingSoon} />
</Callout>
<Spacer direction="vertical" size="xxl" />

<Grid cols={{ desktop: '2', tablet: '1', mobile: '1' }} gap="xl">
	<Flex use={[animateOnScroll]} direction="col" align="start" justify="center" flex="1">
		<Heading as="h1" size="hero">
			<T providedValue={heading} />
		</Heading>
		<Spacer direction="vertical" size="l" />

		<Flex direction="row" gap="s" align="start" wrap="wrap">
			{#each chips as chip (chip)}
				<Chip variant="dark">{chip}</Chip>
			{/each}
		</Flex>

		<Spacer direction="vertical" size="l" />
		<Text>
			<T providedValue={heroDescription} />
		</Text>

		<Spacer direction="vertical" size="l" />

		<Button as="a" href="/prihlaseni">{action}</Button>
	</Flex>
	<div use:applyActions={[animateOnScroll]} class="hero-image-container">
		<Card
			borderRadius="l"
			paddingX="none"
			paddingY="none"
			fillWidth="true"
			fillHeight="true"
			border="light"
		>
			<Image
				src={imageSrc}
				alt="Hero image"
				height="100%"
				width="100%"
				style="object-fit: cover;"
			/>
		</Card>
	</div>
</Grid>
<Spacer direction="vertical" size="between-sections" />

<Text use={[animateOnScroll]}>
	<T providedValue={pageDescription} />
</Text>
<Spacer direction="vertical" size="between-sections" />

{#snippet featureCard(title: string, description: string, iconSrc: string)}
	<Card
		use={[animateOnScroll]}
		borderRadius="l"
		fillWidth="true"
		paddingX="l"
		paddingY="l"
		border="light"
	>
		<Flex direction="row" gap="l">
			<Flex direction="col" align="start">
				<Heading as="h3" size="4">
					<T providedValue={title} />
				</Heading>
				<Spacer direction="vertical" size="sm" />

				<Text>
					<T providedValue={description} />
				</Text>
			</Flex>

			<Image src={iconSrc} alt="Feature icon" height="72px" />
		</Flex>
	</Card>
{/snippet}

<Flex direction={{ mobile: 'col', tablet: 'col', desktop: 'row' }} gap="l">
	<Flex direction="col" align="start" gap="l" justify="start" flex="1">
		{@render featureCard(
			$t(featureCardKeyStart + '.1.title'),
			$t(featureCardKeyStart + '.1.content'),
			penPaper
		)}

		{@render featureCard(
			$t(featureCardKeyStart + '.3.title'),
			$t(featureCardKeyStart + '.3.content'),
			desktopMonitor
		)}

		{@render featureCard(
			$t(featureCardKeyStart + '.5.title'),
			$t(featureCardKeyStart + '.5.content'),
			peopleInherit
		)}
	</Flex>

	<Flex direction="col" align="center" gap="l" justify="center" flex="1">
		{@render featureCard(
			$t(featureCardKeyStart + '.2.title'),
			$t(featureCardKeyStart + '.2.content'),
			personFramed
		)}

		{@render featureCard(
			$t(featureCardKeyStart + '.4.title'),
			$t(featureCardKeyStart + '.4.content'),
			smileyThumbsUp
		)}

		{@render featureCard(
			$t(featureCardKeyStart + '.6.title'),
			$t(featureCardKeyStart + '.6.content'),
			teamwork
		)}
	</Flex>
</Flex>

{#if map}
	<Spacer direction="vertical" size="between-sections" />
	<Text use={[animateOnScroll]} align="center" weight="bold">
		<T providedValue={mapSubtitle} />
	</Text>
	<Spacer direction="vertical" size="s" />
	<Heading
		use={[animateOnScroll]}
		as="h2"
		align="center"
		size={{ mobile: '3', tablet: '2', desktop: '1' }}
	>
		<T providedValue={mapTitle} />
	</Heading>
	<Spacer direction="vertical" size="xxl" />

	<LocationsMap />
{/if}

<style lang="scss">
	@use '$lib/assets/styles/breakpoints' as mq;

	.hero-image-container {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		flex: 1;

		@include mq.mobile {
			flex-direction: column;
			order: -1;
			height: 300px;
		}

		@include mq.tablet {
			flex-direction: column;
			order: -1;
			height: 300px;
		}
	}
</style>
