<script lang="ts">
	import { Info } from '@lucide/svelte';

	import { Text } from '$lib/components';
	import type { Childable } from '$lib/components/cva-constants';

	let { children }: Childable = $props();
</script>

<div class="callout">
	<Info size="1.25em" strokeWidth={2} />
	<Text align="center">
		{@render children?.()}
	</Text>
</div>

<style lang="scss">
	.callout {
		width: 100dvw;
		margin-left: calc(-50dvw + 50%);
		background-color: #d7e5fd;
		padding: var(--spacing-sm);
		color: var(--color-primary);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: var(--spacing-xs);
	}
</style>
