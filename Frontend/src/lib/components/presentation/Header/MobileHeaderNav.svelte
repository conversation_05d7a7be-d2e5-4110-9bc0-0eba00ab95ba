<script lang="ts">
	import { ArrowRight, LogIn, LogOut, Menu, X } from '@lucide/svelte';
	import { fade, slide } from 'svelte/transition';

	import { invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import { withRefreshRetry } from '$lib/api/withRetry';
	import logoOutline from '$lib/assets/img/logos/logo-outline.svg';
	import { Button, Flex } from '$lib/components';
	import { isAuthed } from '$lib/stores/user';
	import { t } from '$lib/translations/config';

	import { navLinks } from './navLinks';

	let { panelButtonHidden } = $props<{ panelButtonHidden: boolean }>();

	let isOpen = $state(false);

	function toggleMenu() {
		isOpen = !isOpen;
		setDocumentOverflow(isOpen ? 'hidden' : '');
	}

	function closeMenu() {
		isOpen = false;
		setDocumentOverflow('');
	}

	function setDocumentOverflow(value: string) {
		if (!document) {
			return;
		}

		document.body.style.overflow = value;
	}

	const handleLogout = async () => {
		const refreshFetch = withRefreshRetry(fetch);
		await refreshFetch('/api/auth/logout', { method: 'POST' });
		await invalidate('app:user');
		closeMenu();
	};
</script>

<div class="mobile-nav-container">
	<button class="menu-button" aria-label="Toggle menu" onclick={toggleMenu}>
		<Menu size="1.4rem" />
	</button>

	{#if isOpen}
		<nav class="mobile-nav" transition:slide={{ duration: 300 }}>
			<button class="close-button" aria-label="Close menu" onclick={closeMenu}>
				<X size="1.5rem" />
			</button>

			<div class="mobile-nav-links">
				{#each navLinks as item (item.href)}
					<a
						href={item.href}
						class:active={page.url.pathname === item.href}
						onclick={closeMenu}
					>
						{$t(item.labelKey)}
					</a>
				{/each}
			</div>

			<div class="mobile-nav-footer">
				<!--TODO: polish 🇵🇱-->
				{#if $isAuthed}
					<Flex gap="s" direction="col">
						{#if !panelButtonHidden}
							<Button
								as="a"
								href="/panel"
								variant="primary"
								TrailingIcon={ArrowRight}
								onclick={closeMenu}
							>
								{$t('header.panel')}
							</Button>
						{/if}

						<Button variant="error" TrailingIcon={LogOut} onclick={handleLogout}>
							{$t('header.logout')}
						</Button>
					</Flex>
				{:else}
					<Button
						as="a"
						href="/prihlaseni"
						variant="primary"
						TrailingIcon={LogIn}
						onclick={closeMenu}
					>
						{$t('header.login')}
					</Button>
				{/if}
			</div>

			<img
				src={logoOutline}
				alt="Logo"
				class="logo-outline"
				transition:fade={{ duration: 200 }}
			/>
		</nav>
	{/if}
</div>

<noscript>
	<style>
		.mobile-nav-container {
			display: none;
		}
	</style>
</noscript>

<style lang="scss">
	.mobile-nav-container {
		position: relative;
		z-index: var(--z-index-mobile-nav);
	}

	.menu-button,
	.close-button {
		border: none;
		cursor: pointer;
		color: var(--color-light);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 3rem;
		height: 3rem;
		background-color: var(--color-dark);
		border-radius: 50%;
		transition: opacity 0.2s ease;

		&:hover {
			opacity: 0.8;
		}
	}

	.mobile-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		background-color: var(--color-light);
		display: flex;
		isolation: isolate;
		flex-direction: column;
		padding: calc(3rem + var(--spacing-l)) var(--spacing-l) var(--spacing-l);
		overflow: hidden;

		.close-button {
			position: absolute;
			top: var(--spacing-m);

			@media (max-width: 639px) {
				right: var(--mobile-side-padding);
			}

			@media (min-width: 640px) {
				right: var(--tablet-side-padding);
			}
		}

		&-links {
			display: flex;
			flex-direction: column;
			flex: 1;
			gap: var(--spacing-m);

			a {
				color: var(--color-dark);
				text-decoration: none;
				font-size: 1.5rem;
				font-weight: 500;
				padding: var(--spacing-m);
				border-radius: var(--border-radius);
				transition: color 0.2s ease;

				&:hover {
					color: var(--color-primary);
				}

				&.active {
					font-weight: 700;
				}
			}
		}

		&-footer {
			margin-top: var(--spacing-l);
			padding-top: var(--spacing-l);
			border-top: 1px solid var(--color-border);
		}

		.logo-outline {
			position: absolute;
			width: 100%;
			bottom: 50%;
			right: 0;
			max-width: 400px;
			transform: translate(25%, 50%);
			z-index: -1;
		}
	}
</style>
