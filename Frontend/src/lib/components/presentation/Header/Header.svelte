<script lang="ts">
	import { ArrowRight, LogIn } from '@lucide/svelte';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import { slide } from 'svelte/transition';

	import { browser } from '$app/environment';
	import { page } from '$app/state';
	import {
		Button,
		Flex,
		LanguageSelector,
		PresentationResponsiveContainer,
		Spacer
	} from '$lib/components';
	import { breakpointTabletMax } from '$lib/globals';
	import { isAuthed } from '$lib/stores/user';
	import { t } from '$lib/translations/config';

	import DesktopHeaderNav from './DesktopHeaderNav.svelte';
	import HeaderLogo from './HeaderLogo.svelte';
	import MobileHeaderNav from './MobileHeaderNav.svelte';

	let mounted = $state(false);
	let animationDone = $state(false);
	let innerWidth = $state(0);
	let scrollY = $state(0);

	let scrolled = $derived(scrollY > 0);
	let panelButtonHidden = $derived(page.url.pathname.includes('/panel'));

	onMount(async () => {
		if (!browser) {
			return;
		}

		await new Promise((resolve) => setTimeout(resolve, 300));
		mounted = true;

		await new Promise((resolve) => setTimeout(resolve, 500));
		animationDone = true;
	});
</script>

<svelte:window bind:innerWidth bind:scrollY />

<header
	class:scrolled
	class:before-mount={!mounted}
	class:play-animation={mounted && !animationDone}
>
	<PresentationResponsiveContainer>
		<Flex align="center" flex="1">
			<Flex flex="2" justify="start" align="center">
				<HeaderLogo />
			</Flex>

			<Spacer direction="horizontal" size="m" />

			<Flex flex="3" justify="center" align="center" class="desktop-only">
				<DesktopHeaderNav />
			</Flex>

			<Spacer direction="horizontal" size="m" />

			<Flex flex="2" justify="end" align="center">
				<LanguageSelector />

				<Spacer direction="horizontal" size="m" />

				{#if innerWidth > breakpointTabletMax}
					<!--TODO: polish 🇵🇱-->
					{#if $isAuthed}
						{#if !panelButtonHidden}
							<div
								transition:slide={{ axis: 'x', duration: 300 }}
								class="go-to-panel-btn"
							>
								<Button
									as="a"
									animateIconOnHover="true"
									href="/panel"
									variant="gradient"
									TrailingIcon={ArrowRight}
								>
									{$t('header.panel')}
								</Button>
							</div>
						{/if}
					{:else}
						<Button as="a" href="/prihlaseni" variant="primary" TrailingIcon={LogIn}>
							{$t('header.login')}
						</Button>
					{/if}
				{:else}
					<MobileHeaderNav {panelButtonHidden} />
				{/if}
			</Flex>
		</Flex>
	</PresentationResponsiveContainer>
</header>

{#if scrolled}
	<div class="scrolled-shadow" transition:fade={{ duration: 300 }}></div>
{/if}
<noscript>
	<style>
		header {
			opacity: 1 !important;
			transform: unset !important;
		}
	</style>
</noscript>

<style lang="scss">
	header {
		--height-dekstop: 7rem;
		--height-desktop-scrolled: 6rem;
		--height-mobile: 6rem;
		--height-mobile-scrolled: 5rem;

		position: sticky;
		top: 0;
		z-index: var(--z-index-header);
		background-color: var(--color-background);
		transition: height 0.5s ease;
		display: flex;
		height: 7.5rem;
		align-items: center;
		opacity: 1;

		.go-to-panel-btn {
			overflow: hidden;
			border-radius: 10em;
		}

		&.before-mount {
			opacity: 0;
			transform: translateY(-20px);
		}

		&.play-animation {
			animation: slide-in 0.5s ease forwards;
		}

		@media (max-width: 1023px) {
			height: var(--height-mobile);

			&.scrolled {
				height: var(--height-mobile-scrolled);
			}
		}

		&.scrolled {
			height: var(--height-desktop-scrolled);
		}
	}

	.scrolled-shadow {
		position: fixed;
		user-select: none;
		pointer-events: none;
		top: 0;
		left: 0;
		right: 0;
		height: 90px;
		z-index: var(--z-index-header-shadow);
		transition: background 0.5s ease;
		background: linear-gradient(
			to bottom,
			rgba(255, 255, 255, 0.3) 44%,
			rgba(255, 255, 255, 0) 100%
		);
	}

	@keyframes slide-in {
		0% {
			opacity: 0;
			transform: translateY(-20px);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
