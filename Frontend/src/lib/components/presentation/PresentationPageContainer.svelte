<script lang="ts">
	import type { Snippet } from 'svelte';

	import { page } from '$app/state';
	import { PresentationResponsiveContainer, Spacer } from '$lib/components';

	let { children } = $props<{ children: Snippet }>();

	let lastPathname = $state<string | undefined>(undefined);

	// Scroll to top instantly on page change
	$effect(() => {
		if (page.url.pathname !== lastPathname) {
			lastPathname = page.url.pathname;
			document.body.scrollIntoView({
				behavior: 'instant',
				block: 'start'
			});
		}
	});
</script>

<PresentationResponsiveContainer grow>
	<Spacer direction="vertical" size={{ mobile: 's', tablet: 'l', desktop: 'xl' }} />

	{@render children?.()}

	<Spacer direction="vertical" size="between-sections" />
</PresentationResponsiveContainer>
