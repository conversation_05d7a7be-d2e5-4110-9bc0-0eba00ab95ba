import type { ActionReturn } from 'svelte/action';

interface Options extends IntersectionObserverInit {
	hiddenTransform?: string;
	duration?: number;
	once?: boolean;
}

interface Attributes {
	onenter?: (e: CustomEvent<void>) => void;
	onleave?: (e: CustomEvent<void>) => void;
}

export function animateOnScroll(
	node: HTMLElement,
	{
		threshold = 0.1,
		root = null,
		rootMargin = '0px',
		hiddenTransform = 'translateY(40px) scale(0.95)',
		duration = 1000,
		once = true
	}: Options = {}
): ActionReturn<Options, Attributes> {
	node.style.opacity = '0';
	node.style.transform = hiddenTransform;

	setTimeout(() => {
		node.style.transition = `opacity ${duration}ms ease, transform ${duration}ms ease`;
	}, 100);

	const observer = new IntersectionObserver(
		([entry]) => {
			if (entry.isIntersecting) {
				node.style.opacity = '1';
				node.style.transform = 'none';
				node.dispatchEvent(new CustomEvent('enter'));

				if (once) {
					observer.unobserve(node);
				}
			} else if (!once) {
				node.style.opacity = '0';
				node.style.transform = hiddenTransform;
				node.dispatchEvent(new CustomEvent('leave'));
			}
		},
		{ threshold, root, rootMargin }
	);

	observer.observe(node);

	return {
		update(params: Options) {
			node.style.transition = `opacity ${params.duration ?? duration}ms ease, transform ${
				params.duration ?? duration
			}ms ease`;
		},
		destroy() {
			observer.disconnect();
		}
	};
}
