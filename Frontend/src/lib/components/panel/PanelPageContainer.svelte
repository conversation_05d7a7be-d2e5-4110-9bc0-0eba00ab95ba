<script lang="ts">
	import { PageTransition, PanelSidebar } from '$lib/components';

	let { children } = $props();
</script>

<div class="panel-container">
	<div class="sidebar-container">
		<PanelSidebar />
	</div>

	<div class="content-container">
		<PageTransition key="panel">
			{@render children?.()}
		</PageTransition>
	</div>
</div>

<style lang="scss">
	.panel-container {
		display: flex;
		flex-direction: row;
		flex-grow: 1;

		.sidebar-container {
			position: fixed;
			top: 120px;
			bottom: 0;
			width: 250px;
			display: flex;
			flex-shrink: 0;
			z-index: 100;
			flex-direction: column;

			@media (max-width: 1023px) {
				display: none;
			}
		}

		.content-container {
			padding: 0 var(--spacing-xl);
			padding-left: calc(var(--spacing-xl) + 250px);
			padding-bottom: var(--spacing-xl);
			flex-grow: 1;
			display: flex;
			flex-direction: column;
			overflow-y: visible;
			overflow-x: hidden;

			@media (max-width: 1023px) {
				padding: 0 var(--tablet-side-padding);
			}

			@media (max-width: 639px) {
				padding: 0 var(--mobile-side-padding);
			}
		}
	}
</style>
