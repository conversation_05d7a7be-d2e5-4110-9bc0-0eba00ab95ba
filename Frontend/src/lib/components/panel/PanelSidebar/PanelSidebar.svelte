<script lang="ts">
	import {
		Calendar,
		ChartColumnBig,
		LogOut,
		MapPin,
		MoveRight,
		NotebookPen,
		Users
	} from '@lucide/svelte';

	import { invalidate } from '$app/navigation';
	import { withRefreshRetry } from '$lib/api';
	import { exists } from '$lib/appmaxx/util';
	import { Button, Flex, PanelSidebarLink, Spacer, Text } from '$lib/components';
	import Chip from '$lib/components/shared/primitives/Chip/Chip.svelte';
	import { Can, getUserRoleColor, getUserRoleTranslationKey } from '$lib/permissions';
	import Role from '$lib/permissions/Role.svelte';
	import { user, userName } from '$lib/stores/user';
	import { T } from '$lib/translations/config';

	const handleLogout = async () => {
		const refreshFetch = withRefreshRetry(fetch);
		await refreshFetch('/api/auth/logout', { method: 'POST' });
		await invalidate('app:user');
	};
</script>

<div class="panel-sidebar">
	<Flex as="nav" flex="1" direction="col">
		<Flex as="ul" direction="col" align="center" gap="sm">
			<PanelSidebarLink
				href="/panel"
				labelKey="panel.navLinks.dashboard"
				Icon={ChartColumnBig}
			/>

			<Role role="student">
				<PanelSidebarLink
					href="/panel/vyhledavac-lekci"
					activationHrefRegex={new RegExp('/panel/vyhledavac-lekci(/.*)?')}
					labelKey="panel.navLinks.lectureSearcher"
					Icon={NotebookPen}
				/>
			</Role>

			<Can subject={['User', 'Student', 'Teacher']} action="read">
				<PanelSidebarLink
					href="/panel/uzivatele"
					activationHrefRegex={new RegExp('/panel/uzivatele(/.*)?')}
					labelKey="panel.navLinks.users"
					Icon={Users}
				/>
			</Can>

			<Can subject={['Location']} action="read">
				<PanelSidebarLink
					href="/panel/lokace"
					labelKey="panel.navLinks.locations"
					activationHrefRegex={new RegExp('/panel/lokace(/.*)?')}
					Icon={MapPin}
				/>
			</Can>

			<Can subject={['Lecture']} action="read">
				<PanelSidebarLink
					href="/panel/lekce"
					activationHrefRegex={new RegExp('/panel/lekce(/.*)?')}
					labelKey="panel.navLinks.lectures"
					Icon={Calendar}
				/>
			</Can>
		</Flex>
	</Flex>

	{#if exists($user)}
		<Flex direction="col">
			<a href="/panel/muj-ucet" class="user-profile-link">
				<Flex direction="row" align="center">
					<Flex direction="col">
						<Text weight="bold">{$userName}</Text>
						<Text as="span" size="small" color="secondary">{$user.email}</Text>
					</Flex>

					<Spacer grow="1" />
					<Spacer direction="horizontal" size="m" />

					<MoveRight size="1.25em" strokeWidth={2.5} />
				</Flex>
			</a>

			<Spacer direction="vertical" size="s" />

			<Chip hexColor={getUserRoleColor($user.role)} size="small">
				<Text as="span" weight="bold" size="small">
					<T key={getUserRoleTranslationKey($user.role)} />
				</Text>
			</Chip>
		</Flex>
	{/if}

	<Button variant="error" onclick={handleLogout}>
		<T key="panel.logout" />
		<LogOut size="1em" strokeWidth={2.5} />
	</Button>
</div>

<style lang="scss">
	@use '$lib/assets/styles/hoverable' as hoverable;

	.panel-sidebar {
		display: flex;
		height: 100%;
		flex-direction: column;
		gap: var(--spacing-l);
		padding: var(--spacing-xl) var(--spacing-l);
		background-color: var(--color-dark);
		color: var(--color-light);
		border-right: 1px solid var(--color-border-dark);
		border-top-right-radius: var(--border-radius-l);
		opacity: 0;
		transform: translateX(-100%);
		border-top: 1px solid var(--color-border-dark);
		border-right: 1px solid var(--color-border-dark);
		animation: slide-in 0.3s ease forwards;
		overflow-y: auto;

		.user-profile-link {
			@include hoverable.hoverable(0.5rem, rgba(255, 255, 255, 0.05), 0.25rem);
		}
	}

	@keyframes slide-in {
		0% {
			opacity: 0;
			transform: translateX(-100%);
		}
		100% {
			opacity: 1;
			transform: translateX(0);
		}
	}
</style>
