<script lang="ts">
	import { page } from '$app/state';
	import { exists } from '$lib/appmaxx/util';
	import { Spacer, Text } from '$lib/components';
	import type { PanelSidebarLinkProps } from '$lib/components/panel/PanelSidebar/PanelSidebar';
	import { T } from '$lib/translations/config';

	let { Icon, labelKey, href, activationHrefRegex }: PanelSidebarLinkProps = $props();

	const isActive = $derived.by(() => {
		if (exists(activationHrefRegex)) {
			return activationHrefRegex.test(page.url.pathname);
		}

		return page.url.pathname === href;
	});
</script>

<li class="link" class:active={isActive}>
	<a {href}>
		<Icon size="1.25em" />
		<Spacer direction="horizontal" size="s" />
		<Text as="span">
			<T key={labelKey} />
		</Text>
	</a>
</li>

<style lang="scss">
	.link {
		width: 100%;

		a {
			display: flex;
			border-radius: 0.66rem;
			padding: var(--spacing-sm) var(--spacing-m);
			transition: background-color 0.2s ease;

			&:hover {
				background-color: rgba(255, 255, 255, 0.05);
			}
		}

		&.active {
			a {
				background-color: var(--color-primary);
			}
		}
	}
</style>
