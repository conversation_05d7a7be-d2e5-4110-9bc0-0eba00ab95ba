<script lang="ts">
	import { page } from '$app/state';
	import type { Childable } from '$lib/components/cva-constants';

	let { children }: Childable = $props();

	const activeLink = $derived(page.url.pathname);
</script>

<nav class="horizontal-tab-nav">
	{@render children?.()}
</nav>

<style lang="scss">
	.horizontal-tab-nav {
		display: flex;
		align-items: center;
		height: 50px;
		overflow: hidden;
		border-radius: var(--border-radius-l);
		border: 1px solid var(--color-border-light);

		:global {
			.nav-tab:not(:last-child) {
				border-right: 1px solid var(--color-border-light);
			}
		}
	}
</style>
