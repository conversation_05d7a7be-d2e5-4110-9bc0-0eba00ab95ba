<script lang="ts">
	import { page } from '$app/state';
	import type { NavTabProps } from '$lib/components/panel/HorizontalTabNav/HorizontalTabNav';

	let { href, label }: NavTabProps = $props();

	const isActive = $derived(page.url.pathname === href);
</script>

<a class="nav-tab" {href} class:active={isActive}>
	{label}
</a>

<style lang="scss">
	.nav-tab {
		text-decoration: none;
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--color-dark);
		padding: 0 var(--spacing-l);
		font-size: var(--font-size-text);
		background-color: var(--color-light);
		height: 100%;
		font-weight: 500;
		transition:
			color 0.2s,
			background-color 0.2s;

		&:hover {
			color: var(--color-primary);
			background-color: var(--color-section);
		}

		&.active {
			background-color: var(--color-primary);
			color: var(--color-light);
		}
	}
</style>
