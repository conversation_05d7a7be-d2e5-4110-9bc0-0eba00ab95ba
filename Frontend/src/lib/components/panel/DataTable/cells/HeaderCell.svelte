<script lang="ts" generics="T_DATA, T_VALUE">
	import { ArrowDown, ArrowUp, ArrowUpDown } from '@lucide/svelte';

	import { Text } from '$lib/components';

	import type { HeaderCellProps } from '../DataTable.ts';

	let { header }: HeaderCellProps<T_DATA, T_VALUE> = $props();

	const canSort = $derived(header.column.getCanSort());
	const isSorted = $derived(header.column.getIsSorted());

	function handleSort() {
		if (canSort) {
			header.column.toggleSorting();
		}
	}

	const headerContent = $derived.by(() => {
		const headerDef = header.column.columnDef.header;
		if (typeof headerDef === 'string') {
			return headerDef;
		}
		if (typeof headerDef === 'function') {
			return headerDef(header.getContext());
		}
		return header.id;
	});
</script>

{#if canSort}
	<button class="header-cell sortable" onclick={handleSort} type="button">
		<span class="header-content">
			<Text wrap="nowrap">
				{headerContent}
			</Text>
		</span>

		<span class="sort-indicator" class:sorted={isSorted}>
			{#if isSorted === 'asc'}
				<ArrowUp size={14} />
			{:else if isSorted === 'desc'}
				<ArrowDown size={14} />
			{:else}
				<ArrowUpDown size={14} />
			{/if}
		</span>
	</button>
{:else}
	<div class="header-cell">
		<span class="header-content">
			<Text wrap="nowrap">
				{headerContent()}
			</Text>
		</span>
	</div>
{/if}

<style lang="scss">
	.header-cell {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		background: none;
		border: none;
		padding: 0;
		font: inherit;
		color: inherit;
		text-align: left;

		&.sortable {
			cursor: pointer;
			user-select: none;

			&:hover {
				opacity: 0.8;
			}

			&:focus {
				outline: 2px solid var(--color-primary);
				outline-offset: 2px;
			}
		}
	}

	.header-content {
		flex: 1;
		white-space: nowrap;
	}

	.sort-indicator {
		margin-left: var(--spacing-xs);
		opacity: 0.5;
		font-size: 0.8em;
		transition: opacity 0.2s ease;
		flex-shrink: 0;

		&.sorted {
			opacity: 1;
			color: var(--color-primary);
		}
	}

	.sortable:hover .sort-indicator {
		opacity: 0.8;
	}
</style>
