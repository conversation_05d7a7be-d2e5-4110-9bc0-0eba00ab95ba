<script lang="ts">
	import { Mail } from '@lucide/svelte';

	interface EmailCellProps {
		email: string;
	}

	let { email }: EmailCellProps = $props();
</script>

<a class="email-cell" href={'mailto:' + email} title={email}>
	<Mail size={14} />
	<span>{email}</span>
</a>

<style lang="scss">
	.email-cell {
		display: inline-flex;
		align-items: center;
		gap: var(--spacing-xs);
		color: var(--color-primary);
		text-decoration: none;

		&:hover {
			text-decoration: underline;
		}
	}
</style>
