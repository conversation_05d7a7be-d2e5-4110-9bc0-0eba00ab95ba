<script lang="ts" generics="T_DATA extends RowData = RowData">
	import { Square<PERSON>en, Trash2 } from '@lucide/svelte';
	import type { RowData } from '@tanstack/table-core';

	import { Button, Flex } from '$lib/components';
	import { t } from '$lib/translations/config';

	interface ManagementCellProps {
		row: T_DATA;
		editHandler: (row: T_DATA) => void;
		deleteHandler: (row: T_DATA) => void;
	}

	let { row, editHandler, deleteHandler }: ManagementCellProps = $props();
</script>

<Flex direction="row" align="center" gap="s">
	<Button
		size="small"
		variant="primary"
		tooltipContent={$t('datatable.management.edit')}
		Icon={SquarePen}
		onclick={() => editHandler(row)}
	/>

	<Button
		size="small"
		variant="error"
		tooltipContent={$t('datatable.management.delete')}
		Icon={Trash2}
		onclick={() => deleteHand<PERSON>(row)}
	/>
</Flex>
