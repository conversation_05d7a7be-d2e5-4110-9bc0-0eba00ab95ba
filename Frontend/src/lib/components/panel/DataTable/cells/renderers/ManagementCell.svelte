<script lang="ts" generics="T_DATA extends RowData = RowData">
	import { SquarePen, Trash2 } from '@lucide/svelte';
	import type { RowData } from '@tanstack/table-core';

	import { Button, Flex } from '$lib/components';

	interface ManagementCellProps {
		row: T_DATA;
		clickHandler: (row: T_DATA) => void;
	}

	let { row, clickHandler }: ManagementCellProps = $props();
</script>

<Flex direction="row" align="center" gap="s">
	<Button size="small" Icon={SquarePen} onclick={() => clickHandler(row)} />
	<Button size="small" variant="error" Icon={Trash2} />
</Flex>
