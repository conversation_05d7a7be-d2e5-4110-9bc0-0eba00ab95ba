<script lang="ts">
	interface PriceCellProps {
		price: number;
		currency?: string;
	}

	let { price, currency = 'Kč' }: PriceCellProps = $props();

	const formattedPrice = $derived.by(() => {
		// Format number with Czech locale (space as thousands separator)
		const formatted = new Intl.NumberFormat('cs-CZ').format(price);
		return `${formatted} ${currency}`;
	});
</script>

<span class="price-cell" title={`${price} ${currency}`}>
	{formattedPrice}
</span>

<style lang="scss">
	.price-cell {
		white-space: nowrap;
		font-weight: 500;
	}
</style>
