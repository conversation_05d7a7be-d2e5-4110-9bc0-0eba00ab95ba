<script lang="ts">
	import { HandCoins } from '@lucide/svelte';

	interface PriceCellProps {
		price: number;
		currency?: string;
	}

	let { price, currency = 'Kč' }: PriceCellProps = $props();

	const formattedPrice = $derived.by(() => {
		// Format number with Czech locale (space as thousands separator)
		const formatted = new Intl.NumberFormat('cs-CZ').format(price);
		return `${formatted} ${currency}`;
	});
</script>

<span class="price-cell" title={`${price} ${currency}`}>
	<HandCoins size={14} />
	{formattedPrice}
</span>

<style lang="scss">
	.price-cell {
		display: inline-flex;
		align-items: center;
		gap: var(--spacing-xs);
	}
</style>
