<script lang="ts">
	import { Phone } from '@lucide/svelte';

	interface PhoneCellProps {
		tel: string;
	}

	let { tel }: PhoneCellProps = $props();
</script>

<a class="phone-cell" href={'tel:' + tel} title={tel}>
	<Phone size={14} />
	<span>{tel}</span>
</a>

<style lang="scss">
	.phone-cell {
		display: inline-flex;
		align-items: center;
		gap: var(--spacing-xs);
		color: var(--color-primary);
		text-decoration: none;

		&:hover {
			text-decoration: underline;
		}
	}
</style>
