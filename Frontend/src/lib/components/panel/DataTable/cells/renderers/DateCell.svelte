<script lang="ts">
	import { exists } from '$lib/appmaxx/util/index.js';
	import { locale } from '$lib/translations/config';

	interface DateCellProps {
		date: Date | null;
	}

	let { date }: DateCellProps = $props();

	const text = $derived.by(() => {
		if (!exists(date)) return '';

		return date.toLocaleDateString($locale, {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit'
		});
	});
</script>

<span class="date-cell" title={date ? date.toISOString() : '-'}>
	{text}
</span>

<style lang="scss">
	.date-cell {
		white-space: nowrap;
	}
</style>
