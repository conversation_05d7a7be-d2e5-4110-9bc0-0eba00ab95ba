import type { Cell, ColumnDef, Header, RowData } from '@tanstack/table-core';
import type { Component } from 'svelte';

import ManagementCell from '$lib/components/panel/DataTable/cells/renderers/ManagementCell.svelte';

export interface DataTableProps<T_DATA extends RowData = RowData> {
	columns: DataTableColumnDefinitions<T_DATA>[];
	data: T_DATA[];
}

export interface HeaderCellProps<T_DATA, T_VALUE> {
	header: Header<T_DATA, T_VALUE>;
}

export interface DataCellProps<T_DATA, T_VALUE> {
	cell: Cell<T_DATA, T_VALUE>;
}

export type DataTableColumnDefinitions<
	T_DATA extends RowData = RowData,
	T_VALUE = unknown
> = ColumnDef<T_DATA, T_VALUE>;

export interface CellRenderer {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	component: Component<any, any, any>;
	props: Record<string, unknown>;
}

export function createCellRenderer<COMP_PROPS = unknown>(
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	component: Component<COMP_PROPS extends Record<string, unknown> ? COMP_PROPS : never, any, any>,
	props: COMP_PROPS
): CellRenderer {
	return {
		component,
		props
	} as CellRenderer;
}

export function createManagementColumn<
	T_DATA extends RowData = RowData
>(): DataTableColumnDefinitions<T_DATA> {
	return {
		id: 'management',
		header: '',
		accessorFn: (row) => row,
		cell: ({ getValue }) =>
			createCellRenderer(ManagementCell, {
				row: getValue(),
				editHandler: (row) => console.log(row),
				deleteHandler: (row) => console.log(row)
			})
	};
}

export function getPaginationStructure(
	current: number,
	total: number,
	delta = 2
): (number | string)[] {
	const pages: Set<number> = new Set([1, total]);

	for (let i = current - delta; i <= current + delta; i++) {
		if (i >= 1 && i <= total) {
			pages.add(i);
		}
	}

	const sortedPages: number[] = Array.from(pages).sort((a, b) => a - b);

	const result: (number | string)[] = [];
	let lastPage: number | null = null;

	for (const page of sortedPages) {
		if (lastPage !== null) {
			if (page - lastPage === 2) {
				result.push(lastPage + 1);
			} else if (page - lastPage > 2) {
				result.push('...');
			}
		}
		result.push(page);
		lastPage = page;
	}

	return result;
}
