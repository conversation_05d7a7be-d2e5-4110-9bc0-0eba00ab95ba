<script lang="ts">
	import { RefreshCw } from '@lucide/svelte';
	import { useQueryClient } from '@tanstack/svelte-query';

	import { Button, Flex, Heading, Spacer } from '$lib/components';

	interface Props {
		title: string;
		queryKey: string[];
	}

	let { title, queryKey }: Props = $props();

	const queryClient = useQueryClient();

	const handleRefresh = () => {
		queryClient.invalidateQueries({ queryKey });
	};
</script>

<Flex direction="row" align="center">
	<Heading as="h2" size="3">{title}</Heading>
	<Spacer grow="1" />
	<Button variant="secondary" size="small" onclick={handleRefresh} LeadingIcon={RefreshCw}>
		Refresh
	</Button>
</Flex>
<Spacer direction="vertical" size="l" />
