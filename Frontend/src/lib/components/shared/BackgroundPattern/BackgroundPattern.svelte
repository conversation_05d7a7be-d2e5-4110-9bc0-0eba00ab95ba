<script lang="ts">
	let scrollY = 0;
	const parallaxFactor = 0.15;
</script>

<svelte:window bind:scrollY />

<div class="bg-pattern" style="transform: translateY({-scrollY * parallaxFactor}px);"></div>

<style lang="scss">
	.bg-pattern {
		position: fixed;
		inset: 0;
		height: 66dvh;
		user-select: none;
		pointer-events: none;
		background-image: url('$lib/assets/img/misc/bg-pattern.svg');
		background-repeat: repeat;
		background-size: auto;
		z-index: -1;
	}

	.bg-pattern::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			to bottom,
			rgba(255, 255, 255, 0) 0%,
			rgba(255, 255, 255, 1) 100%
		);
	}
</style>
