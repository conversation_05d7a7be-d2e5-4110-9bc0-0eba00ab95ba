<script lang="ts">
	import type { MetaSeoProps } from './MetaSeo';

	const {
		title,
		description,
		keywords,
		canonical,
		author,
		robots,
		og,
		twitter,
		article
	}: MetaSeoProps = $props();

	// Helper function to format keywords
	const formatKeywords = (keywords: string | string[] | undefined): string | undefined => {
		if (!keywords) return undefined;
		return Array.isArray(keywords) ? keywords.join(', ') : keywords;
	};

	// Helper function to format array properties
	const formatArray = (value: string | string[] | undefined): string[] => {
		if (!value) return [];
		return Array.isArray(value) ? value : [value];
	};
</script>

<svelte:head>
	{#if title}
		<title>{title}</title>
	{/if}

	{#if description}
		<meta name="description" content={description} />
	{/if}

	{#if formatKeywords(keywords)}
		<meta name="keywords" content={formatKeywords(keywords)} />
	{/if}

	{#if canonical}
		<link rel="canonical" href={canonical} />
	{/if}

	{#if author}
		<meta name="author" content={author} />
	{/if}

	{#if robots}
		<meta name="robots" content={robots} />
	{/if}

	<!-- Open Graph tags -->
	{#if og}
		<meta property="og:title" content={og.title} />
		<meta property="og:type" content={og.type} />
		<meta property="og:image" content={og.image} />
		<meta property="og:url" content={og.url} />
		{#if og.description}
			<meta property="og:description" content={og.description} />
		{/if}
		{#if og.siteName}
			<meta property="og:site_name" content={og.siteName} />
		{/if}
		{#if og.locale}
			<meta property="og:locale" content={og.locale} />
		{/if}
	{/if}

	<!-- Twitter Card tags -->
	{#if twitter}
		<meta name="twitter:card" content={twitter.card} />
		{#if twitter.site}
			<meta name="twitter:site" content={twitter.site} />
		{/if}
		{#if twitter.creator}
			<meta name="twitter:creator" content={twitter.creator} />
		{/if}
		{#if twitter.title}
			<meta name="twitter:title" content={twitter.title} />
		{/if}
		{#if twitter.description}
			<meta name="twitter:description" content={twitter.description} />
		{/if}
		{#if twitter.image}
			<meta name="twitter:image" content={twitter.image} />
		{/if}
		{#if twitter.imageAlt}
			<meta name="twitter:image:alt" content={twitter.imageAlt} />
		{/if}
	{/if}

	<!-- Article tags (for Pinterest Rich Pins and SEO) -->
	{#if article}
		{#if article.publishedTime}
			<meta property="article:published_time" content={article.publishedTime} />
		{/if}
		{#if article.modifiedTime}
			<meta property="article:modified_time" content={article.modifiedTime} />
		{/if}
		{#if article.expirationTime}
			<meta property="article:expiration_time" content={article.expirationTime} />
		{/if}
		{#if article.author}
			{#each formatArray(article.author) as author (author)}
				<meta property="article:author" content={author} />
			{/each}
		{/if}
		{#if article.section}
			<meta property="article:section" content={article.section} />
		{/if}
		{#if article.tag}
			{#each formatArray(article.tag) as tag (tag)}
				<meta property="article:tag" content={tag} />
			{/each}
		{/if}
	{/if}
</svelte:head>
