type IndexDirectives = 'index' | 'noindex';
type FollowDirectives = 'follow' | 'nofollow';
type ArchiveDirectives = 'archive' | 'noarchive';
type NoneDirective = 'none';

// Canonical ordered combos
export type RobotsDirective =
	| IndexDirectives
	| FollowDirectives
	| ArchiveDirectives
	| NoneDirective
	| `${IndexDirectives}, ${FollowDirectives}`
	| `${IndexDirectives}, ${ArchiveDirectives}`
	| `${FollowDirectives}, ${ArchiveDirectives}`
	| `${IndexDirectives}, ${FollowDirectives}, ${ArchiveDirectives}`;

export type MetaSeoProps = {
	title?: string;
	description?: string;
	keywords?: string | string[];
	canonical?: string;
	author?: string;
	robots?: RobotsDirective;
	// OpenGraph for Facebook, LinkedIn, WhatsApp, Telegram, Discord, Slack
	og?: {
		title: string; // Required by OpenGraph
		type:
			| 'website'
			| 'article'
			| 'book'
			| 'profile'
			| 'video.movie'
			| 'video.episode'
			| 'music.song'; // Required by OpenGraph
		image: string; // Required by OpenGraph - URL to image
		url: string; // Required by OpenGraph - canonical URL
		description?: string; // Optional but recommended
		siteName?: string; // Name of the overall site
		locale?: string; // e.g., 'en_US', 'cs_CZ'
	};
	// Twitter/X Cards
	twitter?: {
		card: 'summary' | 'summary_large_image' | 'app' | 'player'; // Required
		site?: string; // @username - Either site or creator should be provided
		creator?: string; // @creator username - Either site or creator should be provided
		title?: string; // Recommended for better display
		description?: string; // Recommended for better display
		image?: string;
		imageAlt?: string; // accessibility - recommended when image is provided
	};
	// Additional article-specific tags (used by Pinterest Rich Pins)
	article?: {
		publishedTime?: string; // ISO 8601 format
		modifiedTime?: string; // ISO 8601 format
		expirationTime?: string; // ISO 8601 format
		author?: string | string[]; // Author name(s)
		section?: string; // High-level section name
		tag?: string | string[]; // Tag words
	};
};
