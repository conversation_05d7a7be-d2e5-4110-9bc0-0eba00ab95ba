<script lang="ts">
	let { fillPage = false }: { fillPage?: boolean } = $props();
</script>

<span class="loader-container" class:fill-page={fillPage}>
	<span class="loader" aria-label="Loading..." role="status"> </span>
</span>

<style lang="scss">
	.loader-container {
		&.fill-page {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-grow: 1;
		}
	}

	.loader {
		border-radius: 75%;
		display: inline-block;
		box-sizing: border-box;
		border: 3px solid var(--color-primary);
		width: 28px;
		height: 28px;
		border-bottom-color: transparent;
		line-height: 0;
		animation: rotation 0.8s linear infinite;
	}

	@keyframes rotation {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
