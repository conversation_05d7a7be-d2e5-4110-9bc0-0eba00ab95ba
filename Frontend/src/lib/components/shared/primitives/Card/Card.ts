import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import { type Childable, paddingX, paddingY } from '$lib/components/cva-constants';

const variants = {
	borderRadius: {
		none: '',
		s: 'border-radius-s',
		m: 'border-radius-m',
		l: 'border-radius-l',
		xl: 'border-radius-xl'
	},
	variant: {
		section: 'card-variant-section',
		primary: 'card-variant-primary',
		dark: 'card-variant-dark'
	},
	paddingX,
	paddingY,
	overflow: {
		hidden: 'overflow-hidden',
		visible: 'overflow-visible',
		auto: 'overflow-auto'
	},
	fillWidth: {
		true: 'card-full-width',
		false: ''
	},
	fillHeight: {
		true: 'card-full-height',
		false: ''
	},
	shadow: {
		true: 'card-shadow',
		false: ''
	},
	isFlex: {
		true: 'card-flex',
		false: ''
	},
	flexDirection: {
		row: 'flex-row',
		'row-reverse': 'flex-row-reverse',
		col: 'flex-col',
		'col-reverse': 'flex-col-reverse'
	},
	border: {
		light: 'card-border-light',
		dark: 'card-border-dark',
		primary: 'card-border-primary',
		none: ''
	},
	pageContainerOverflow: {
		subtle: 'container-overflow-subtle',
		small: 'container-overflow-small',
		fullscreen: 'container-overflow-fullscreen',
		none: ''
	}
};

const defaultVariants = {
	variant: 'section',
	borderRadius: 'm',
	paddingX: 'm',
	paddingY: 'm',
	overflow: 'hidden',
	fillWidth: 'false',
	fillHeight: 'false',
	shadow: 'false',
	isFlex: 'true',
	flexDirection: 'col',
	border: 'none',
	pageContainerOverflow: 'none'
} as const;

type CardVariantProps = {
	variant?: keyof typeof variants.variant;
	borderRadius?: ResponsiveType<keyof typeof variants.borderRadius>;
	paddingX?: ResponsiveType<keyof typeof variants.paddingX>;
	paddingY?: ResponsiveType<keyof typeof variants.paddingY>;
	overflow?: keyof typeof variants.overflow;
	fillWidth?: ResponsiveType<keyof typeof variants.fillWidth>;
	fillHeight?: ResponsiveType<keyof typeof variants.fillHeight>;
	shadow?: keyof typeof variants.shadow;
	isFlex?: ResponsiveType<keyof typeof variants.isFlex>;
	flexDirection?: ResponsiveType<keyof typeof variants.flexDirection>;
	border?: keyof typeof variants.border;
	pageContainerOverflow?: ResponsiveType<keyof typeof variants.pageContainerOverflow>;
};

export const cardCva = cva<CardVariantProps>('card', variants, defaultVariants);

export type CardProps = HTMLAttributes<HTMLDivElement> & CardVariantProps & Actionable & Childable;
