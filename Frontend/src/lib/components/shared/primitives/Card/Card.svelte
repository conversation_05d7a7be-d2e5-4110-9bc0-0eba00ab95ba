<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { cardCva, type CardProps } from './Card';

	let {
		children,
		borderRadius,
		paddingX,
		paddingY,
		overflow,
		fillWidth,
		fillHeight,
		shadow,
		isFlex,
		border,
		flexDirection,
		pageContainerOverflow,
		variant,
		use,
		class: className,
		...props
	}: CardProps = $props();
</script>

<div
	class={cardCva(
		{
			borderRadius,
			paddingX,
			paddingY,
			overflow,
			fillWidth,
			fillHeight,
			shadow,
			border,
			flexDirection,
			isFlex,
			variant,
			pageContainerOverflow
		},
		className
	)}
	use:applyActions={use}
	{...props}
>
	{@render children?.()}
</div>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	// Variants
	.card-variant {
		&-section {
			background-color: var(--color-section);
		}

		&-primary {
			color: var(--color-light);
			background-image: url('$lib/assets/img/misc/noise-primary.webp');
			background-repeat: repeat;
			background-size: 400px 400px;
		}

		&-dark {
			background-color: var(--color-dark);
			color: var(--color-light);
		}
	}

	@include responsive.responsive-class('card-flex', display, flex);

	// Flex directions
	@include responsive.responsive-class('flex-row', flex-direction, row);
	@include responsive.responsive-class('flex-row-reverse', flex-direction, row-reverse);
	@include responsive.responsive-class('flex-col', flex-direction, column);
	@include responsive.responsive-class('flex-col-reverse', flex-direction, column-reverse);

	.card-border {
		&-light {
			border: 1px solid var(--color-border-light);
		}

		&-dark {
			border: 1px solid var(--color-dark);
		}

		&-primary {
			border: 2px solid var(--color-primary);
		}
	}

	// Border radii
	@include responsive.responsive-class('border-radius-s', border-radius, var(--border-radius-s));
	@include responsive.responsive-class('border-radius-m', border-radius, var(--border-radius-m));
	@include responsive.responsive-class('border-radius-l', border-radius, var(--border-radius-l));
	@include responsive.responsive-class(
		'border-radius-xl',
		border-radius,
		var(--border-radius-xl)
	);

	// Overflows
	.overflow {
		&-hidden {
			overflow: hidden;
		}

		&-auto {
			overflow: auto;
		}

		&-visible {
			overflow: visible;
		}
	}

	// Shadows
	.card-shadow {
		box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
	}

	// Paddings
	@include responsive.paddings();

	// Full width and height
	@include responsive.responsive-class('card-full-width', width, 100%);
	@include responsive.responsive-class('card-full-height', height, 100%);

	// Page container overflows
	@include responsive.responsive-class-block('container-overflow-small') {
		margin-left: -1rem;
		margin-right: -1rem;
		width: calc(100% + 2rem);
	}

	@include responsive.responsive-class-block('container-overflow-subtle') {
		margin-left: -2rem;
		margin-right: -2rem;
		width: calc(100% + 4rem);
	}

	@include responsive.responsive-class-block('container-overflow-fullscreen') {
		width: 100dvw;
		margin-left: calc(-50dvw + 50%);
	}
</style>
