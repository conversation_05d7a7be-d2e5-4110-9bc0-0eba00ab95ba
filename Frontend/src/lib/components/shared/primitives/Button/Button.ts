import type { Component } from 'svelte';
import type { HTMLAnchorAttributes, HTMLButtonAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import type { Childable } from '$lib/components/cva-constants';

const variants = {
	variant: {
		primary: 'btn-variant-primary',
		error: 'btn-variant-error',
		success: 'btn-variant-success',
		light: 'btn-variant-light',
		dark: 'btn-variant-dark',
		gradient: 'btn-variant-gradient',
		gray: 'btn-variant-gray'
	},
	size: {
		small: 'btn-size-small',
		medium: 'btn-size-medium',
		large: 'btn-size-large'
	},
	fullWidth: {
		true: 'btn-full-width',
		false: ''
	},
	wrap: {
		true: 'btn-wrap',
		false: ''
	},
	animateIconOnHover: {
		true: 'btn-animate-icon-on-hover',
		false: ''
	}
};

const defaultVariants = {
	variant: 'primary',
	size: 'large',
	fullWidth: 'false',
	wrap: 'false',
	animateIconOnHover: 'false'
} as const;

type ButtonVariantProps = {
	variant?: keyof typeof variants.variant;
	size?: ResponsiveType<keyof typeof variants.size>;
	fullWidth?: ResponsiveType<keyof typeof variants.fullWidth>;
	wrap?: keyof typeof variants.wrap;
	animateIconOnHover?: keyof typeof variants.animateIconOnHover;
};

export const buttonCva = cva<ButtonVariantProps>('btn', variants, defaultVariants);

type CustomButtonProps = {
	Icon?: Component;
	TrailingIcon?: Component;
	tooltipContent?: string;
};

type ButtonDefaultProps = { as?: 'button' } & HTMLButtonAttributes;

type ButtonAnchorProps = { as: 'a' } & HTMLAnchorAttributes;

export type ButtonProps = (ButtonDefaultProps | ButtonAnchorProps) &
	ButtonVariantProps &
	CustomButtonProps &
	Actionable &
	Childable;
