<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { buttonCva, type ButtonProps } from './Button';

	let {
		size,
		variant,
		fullWidth,
		wrap,
		Icon,
		TrailingIcon,
		children,
		animateIconOnHover,
		use,
		as = 'button',
		class: className,
		...props
	}: ButtonProps = $props();
</script>

<svelte:element
	this={as}
	class={buttonCva({ variant, size, fullWidth, wrap, animateIconOnHover }, className)}
	use:applyActions={use}
	{...props}
>
	{#if Icon}
		<span class="icon-container">
			<Icon size="1.25em" strokeWidth={2.5} />
		</span>
	{/if}

	{@render children?.()}

	{#if TrailingIcon}
		<span class="icon-container">
			<TrailingIcon size="1.25em" strokeWidth={2.5} />
		</span>
	{/if}
</svelte:element>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.btn {
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 10em;
		font-weight: bold;
		font-size: var(--font-size-text);
		transition: background-color 0.2s ease;
		gap: var(--spacing-s);
		color: var(--color);
		background-color: var(--background-color);
		text-decoration: none;
		white-space: nowrap;

		&:hover {
			background-color: var(--bg-hover);
		}

		&:disabled {
			opacity: 0.75;
			filter: grayscale(100%);
			-webkit-filter: grayscale(100%);
			pointer-events: none;
			cursor: not-allowed;

			&:hover {
				background-color: var(--background-color);
			}
		}

		.icon-container {
			display: flex;
		}

		&-animate-icon-on-hover {
			.icon-container {
				transition: transform 0.2s ease;
			}

			&:hover .icon-container {
				transform: translateX(3px);
			}
		}
	}

	.btn-wrap {
		white-space: normal;
	}

	@include responsive.responsive-class(
		'btn-size-small',
		padding,
		var(--spacing-s) var(--spacing-m)
	);
	@include responsive.responsive-class(
		'btn-size-medium',
		padding,
		var(--spacing-m) var(--spacing-l)
	);
	@include responsive.responsive-class(
		'btn-size-large',
		padding,
		calc(var(--spacing-m) + var(--spacing-xs)) var(--spacing-xl)
	);

	@include responsive.responsive-class('btn-full-width', width, 100%);

	.btn-variant {
		&-primary {
			--background-color: var(--color-primary);
			--color: var(--color-light);
			--bg-hover: var(--color-primary-hover);
		}

		&-error {
			--background-color: var(--color-error);
			--color: var(--color-light);
			--bg-hover: var(--color-error-hover);
		}

		&-success {
			--background-color: var(--color-success);
			--color: var(--color-light);
			--bg-hover: var(--color-success-hover);
		}

		&-light {
			--background-color: var(--color-light);
			--color: var(--color-dark);
			--bg-hover: var(--color-light-hover);
		}

		&-dark {
			--background-color: var(--color-dark);
			--color: var(--color-light);
			--bg-hover: var(--color-dark-hover);
		}

		&-gray {
			--background-color: var(--color-section);
			--color: var(--color-dark);
			--bg-hover: var(--color-light);
		}

		&-gradient {
			--background-color: transparent;
			--bg-hover: transparent;
			--color: var(--color-light);

			background: linear-gradient(var(--angle), var(--color1), var(--color2));
			transition:
				--angle 0.5s,
				--color1 0.5s,
				--color2 0.5s;

			&:hover {
				--angle: -45deg;
				--color1: #427ef4;
				--color2: #a442f4;
			}
		}
	}

	// Reactive CSS custom properties
	@property --angle {
		syntax: '<angle>';
		initial-value: 45deg;
		inherits: false;
	}

	@property --color1 {
		syntax: '<color>';
		initial-value: #427ef4;
		inherits: false;
	}

	@property --color2 {
		syntax: '<color>';
		initial-value: #7d42f4;
		inherits: false;
	}
</style>
