<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { textLinkCva, type TextLinkProps } from './TextLink';

	let {
		underlineVariant,
		underline = true,
		children,
		use,
		class: className,
		...props
	}: TextLinkProps = $props();
</script>

<a
	class="text-link {textLinkCva({ underlineVariant }, className)}"
	use:applyActions={use}
	{...props}
	class:underlined={underline}
>
	{@render children?.()}
</a>

<style lang="scss">
	.text-link {
		color: inherit;
		text-decoration: none;
		transition: color 0.2s ease;

		&-underline-light {
			&::after {
				background-color: var(--color-border-dark);
			}
		}

		&-underline-dark {
			&::after {
				background-color: var(--color-border-light);
			}
		}

		&.underlined {
			position: relative;
			padding-bottom: var(--spacing-xs);

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 100%;
				height: 1px;
				opacity: 0.3;
				transition:
					width 0.3s ease,
					opacity 0.3s ease;
			}
		}

		&:hover {
			color: var(--color-primary);

			&.underlined {
				&::after {
					width: 77%;
					opacity: 0.5;
				}
			}
		}

		&-light {
			color: var(--color-light);
		}
	}
</style>
