import type { HTMLAnchorAttributes } from 'svelte/elements';

import { cva } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import type { Childable } from '$lib/components/cva-constants';

const variants = {
	underlineVariant: {
		dark: 'text-link-underline-dark',
		light: 'text-link-underline-light'
	}
};

const defaultVariants = {
	underlineVariant: 'light'
} as const;

type TextLinkVariantProps = {
	underlineVariant?: keyof typeof variants.underlineVariant;
};

export const textLinkCva = cva<TextLinkVariantProps>('text-link', variants, defaultVariants);

interface CustomTextLinkProps {
	underline?: boolean;
}

export type TextLinkProps = TextLinkVariantProps &
	HTMLAnchorAttributes &
	CustomTextLinkProps &
	Actionable &
	Childable;
