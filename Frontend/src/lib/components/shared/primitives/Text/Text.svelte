<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { textCva, type TextProps } from './Text';

	let {
		children,
		as = 'p',
		align,
		wrap,
		size,
		italic,
		color,
		weight,
		lineHeight,
		use,
		class: className,
		...props
	}: TextProps = $props();
</script>

<svelte:element
	this={as}
	class={textCva({ align, wrap, size, italic, color, weight }, className)}
	style:--line-height={lineHeight}
	use:applyActions={use}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.text {
		margin: 0;
		font-family: inherit;
		line-height: var(--line-height, 1.5);
	}

	// Size
	@include responsive.responsive-class('text-size-h1', font-size, var(--font-size-1));
	@include responsive.responsive-class('text-size-h2', font-size, var(--font-size-2));
	@include responsive.responsive-class('text-size-h3', font-size, var(--font-size-3));
	@include responsive.responsive-class('text-size-h4', font-size, var(--font-size-4));
	@include responsive.responsive-class('text-size-regular', font-size, var(--font-size-text));
	@include responsive.responsive-class('text-size-small', font-size, var(--font-size-small));

	// Alignment
	@include responsive.responsive-class('text-align-left', text-align, left);
	@include responsive.responsive-class('text-align-center', text-align, center);
	@include responsive.responsive-class('text-align-right', text-align, right);

	// Wrapping
	@include responsive.responsive-class('text-wrap-wrap', white-space, normal);
	@include responsive.responsive-class-block('text-wrap-nowrap') {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	@include responsive.responsive-class-block('text-wrap-pretty') {
		white-space: normal;
		text-wrap: pretty;
	}
	@include responsive.responsive-class-block('text-wrap-balance') {
		white-space: normal;
		text-wrap: balance;
	}

	.text-color {
		&-default {
			color: var(--color-dark);
		}

		&-inherited {
			color: inherit;
		}

		&-secondary {
			color: var(--color-secondary-text);
		}
	}

	.text-weight {
		&-normal {
			font-weight: 400;
		}

		&-semibold {
			font-weight: 500;
		}

		&-bold {
			font-weight: 700;
		}
	}

	.text-italic {
		font-style: italic;
	}
</style>
