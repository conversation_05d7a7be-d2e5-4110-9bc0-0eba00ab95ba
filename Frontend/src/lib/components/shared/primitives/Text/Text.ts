import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import type { Childable } from '$lib/components/cva-constants';

const variants = {
	as: {
		p: '',
		span: '',
		div: '',
		label: ''
	},
	align: {
		left: 'text-align-left',
		center: 'text-align-center',
		right: 'text-align-right'
	},
	wrap: {
		wrap: 'text-wrap-wrap',
		nowrap: 'text-wrap-nowrap',
		pretty: 'text-wrap-pretty',
		balance: 'text-wrap-balance'
	},
	size: {
		h1: 'text-size-h1',
		h2: 'text-size-h2',
		h3: 'text-size-h3',
		h4: 'text-size-h4',
		regular: 'text-size-regular',
		small: 'text-size-small'
	},
	italic: {
		true: 'text-italic',
		false: ''
	},
	color: {
		default: 'text-color-default',
		inherited: 'text-color-inherited',
		secondary: 'text-color-secondary'
	},
	weight: {
		normal: 'text-weight-normal',
		semibold: 'text-weight-semibold',
		bold: 'text-weight-bold'
	}
};

const defaultVariants = {
	as: 'p',
	align: 'left',
	wrap: 'wrap',
	size: 'regular',
	italic: 'false',
	color: 'inherited',
	weight: 'normal'
} as const;

type TextVariantProps = {
	as?: keyof typeof variants.as;
	align?: ResponsiveType<keyof typeof variants.align>;
	wrap?: ResponsiveType<keyof typeof variants.wrap>;
	size?: ResponsiveType<keyof typeof variants.size>;
	italic?: keyof typeof variants.italic;
	color?: keyof typeof variants.color;
	weight?: keyof typeof variants.weight;
};

export const textCva = cva<TextVariantProps>('text', variants, defaultVariants);

type TextParagraphProps = { as?: 'p' } & HTMLAttributes<HTMLParagraphElement>;

type TextSpanProps = { as: 'span' } & HTMLAttributes<HTMLSpanElement>;

type TextCustomProps = {
	lineHeight?: string;
};

export type TextProps = (TextParagraphProps | TextSpanProps) &
	TextCustomProps &
	TextVariantProps &
	Actionable &
	Childable;
