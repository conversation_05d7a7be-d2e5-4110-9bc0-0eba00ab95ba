import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';

const variants = {
	direction: {
		horizontal: 'spacer-horizontal',
		vertical: 'spacer-vertical'
	},
	size: {
		xs: 'spacer-xs',
		s: 'spacer-s',
		sm: 'spacer-sm',
		m: 'spacer-m',
		ml: 'spacer-ml',
		l: 'spacer-l',
		xl: 'spacer-xl',
		xxl: 'spacer-xxl',
		'between-sections': 'spacer-between-sections'
	},
	grow: {
		'0': 'grow-0',
		'1': 'grow'
	}
};

const defaultVariants = {
	direction: 'vertical',
	size: 'm',
	grow: '0'
} as const;

type SpacerVariantProps = {
	direction?: ResponsiveType<keyof typeof variants.direction>;
	size?: ResponsiveType<keyof typeof variants.size>;
	grow?: keyof typeof variants.grow;
};

export const spacerCva = cva<SpacerVariantProps>('spacer', variants, defaultVariants);

export type SpacerDivProps = { as?: 'div' } & HTMLAttributes<HTMLDivElement> & SpacerVariantProps;

export type SpacerSpanProps = { as: 'span' } & HTMLAttributes<HTMLSpanElement> & SpacerVariantProps;

export type SpacerProps = SpacerDivProps | SpacerSpanProps;
