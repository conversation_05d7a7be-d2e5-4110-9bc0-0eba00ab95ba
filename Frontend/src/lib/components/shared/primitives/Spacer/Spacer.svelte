<script lang="ts">
	import { spacerCva, type SpacerProps } from './Spacer';

	let { as = 'div', direction, size, grow, class: className, ...props }: SpacerProps = $props();
</script>

<svelte:element this={as} class={spacerCva({ direction, size, grow }, className)} {...props} />

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.spacer {
		flex-shrink: 0;
	}

	@include responsive.responsive-class('spacer-xs', --size, var(--spacing-xs));
	@include responsive.responsive-class('spacer-s', --size, var(--spacing-s));
	@include responsive.responsive-class('spacer-sm', --size, var(--spacing-sm));
	@include responsive.responsive-class('spacer-m', --size, var(--spacing-m));
	@include responsive.responsive-class('spacer-ml', --size, var(--spacing-ml));
	@include responsive.responsive-class('spacer-l', --size, var(--spacing-l));
	@include responsive.responsive-class('spacer-xl', --size, var(--spacing-xl));
	@include responsive.responsive-class('spacer-xxl', --size, var(--spacing-xxl));
	@include responsive.responsive-class(
		'spacer-between-sections',
		--size,
		var(--spacing-between-sections)
	);

	// Horizontal
	@include responsive.responsive-class-block('spacer-horizontal') {
		display: inline-block;
		height: 1px;
		width: var(--size);
	}

	// Vertical
	@include responsive.responsive-class-block('spacer-vertical') {
		display: block;
		width: 1px;
		height: var(--size);
	}

	.grow {
		flex: 1;
	}

	.grow-0 {
		flex-grow: 0;
	}
</style>
