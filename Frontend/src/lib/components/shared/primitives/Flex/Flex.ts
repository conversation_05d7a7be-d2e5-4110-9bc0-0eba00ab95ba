import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import { type Childable, paddingX, paddingY } from '$lib/components/cva-constants';

const variants = {
	inline: {
		false: 'flex',
		true: 'inline-flex'
	},
	direction: {
		row: 'flex-row',
		'row-reverse': 'flex-row-reverse',
		col: 'flex-col',
		'col-reverse': 'flex-col-reverse'
	},
	align: {
		start: 'items-start',
		center: 'items-center',
		end: 'items-end',
		stretch: 'items-stretch',
		baseline: 'items-baseline'
	},
	justify: {
		start: 'justify-start',
		center: 'justify-center',
		end: 'justify-end',
		between: 'justify-between',
		around: 'justify-around',
		evenly: 'justify-evenly'
	},
	wrap: {
		nowrap: 'flex-nowrap',
		wrap: 'flex-wrap',
		'wrap-reverse': 'flex-wrap-reverse'
	},
	gap: {
		none: '',
		xs: 'gap-xs',
		s: 'gap-s',
		sm: 'gap-sm',
		m: 'gap-m',
		l: 'gap-l',
		xl: 'gap-xl',
		xxl: 'gap-xxl'
	},
	paddingX,
	paddingY,
	hoverable: {
		true: 'hoverable',
		false: ''
	},
	pageContainerOverflow: {
		subtle: 'container-overflow-subtle',
		small: 'container-overflow-small',
		fullscreen: 'container-overflow-fullscreen',
		none: ''
	}
};

const defaultVariants = {
	inline: 'false',
	direction: 'row',
	align: 'stretch',
	justify: 'start',
	wrap: 'nowrap',
	gap: 'none',
	paddingX: 'none',
	paddingY: 'none',
	hoverable: 'false',
	pageContainerOverflow: 'none'
} as const;

type FlexVariantProps = {
	inline?: keyof typeof variants.inline;
	direction?: ResponsiveType<keyof typeof variants.direction>;
	align?: ResponsiveType<keyof typeof variants.align>;
	justify?: ResponsiveType<keyof typeof variants.justify>;
	wrap?: ResponsiveType<keyof typeof variants.wrap>;
	gap?: ResponsiveType<keyof typeof variants.gap>;
	paddingX?: ResponsiveType<keyof typeof variants.paddingX>;
	paddingY?: ResponsiveType<keyof typeof variants.paddingY>;
	hoverable?: keyof typeof variants.hoverable;
	pageContainerOverflow?: ResponsiveType<keyof typeof variants.pageContainerOverflow>;
};

export const flexCva = cva<FlexVariantProps>('flex', variants, defaultVariants);

interface FlexCustomProps {
	as?: keyof HTMLElementTagNameMap;
	flex?: string;
}

export type FlexProps = FlexVariantProps &
	HTMLAttributes<HTMLElement> &
	FlexCustomProps &
	Actionable &
	Childable;
