<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';
	import { exists } from '$lib/appmaxx/util';

	import { flexCva, type FlexProps } from './Flex';

	let {
		children,
		as = 'div',
		inline,
		direction,
		align,
		justify,
		flex,
		wrap,
		paddingX,
		paddingY,
		hoverable,
		pageContainerOverflow,
		gap,
		class: className,
		use,
		...props
	}: FlexProps = $props();
</script>

<svelte:element
	this={as ?? 'div'}
	style:--flex={flex}
	class={flexCva(
		{
			inline,
			direction,
			align,
			justify,
			wrap,
			gap,
			paddingX,
			paddingY,
			hoverable,
			pageContainerOverflow
		},
		className
	)}
	class:flex-custom={exists(flex)}
	use:applyActions={use}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;
	@use '$lib/assets/styles/hoverable' as hoverable;

	.flex {
		display: flex;
	}

	.inline-flex {
		display: inline-flex;
	}

	.flex-custom {
		flex: var(--flex);
	}

	// Direction
	@include responsive.responsive-class('flex-row', flex-direction, row);
	@include responsive.responsive-class('flex-row-reverse', flex-direction, row-reverse);
	@include responsive.responsive-class('flex-col', flex-direction, column);
	@include responsive.responsive-class('flex-col-reverse', flex-direction, column-reverse);

	// Alignment
	@include responsive.responsive-class('items-start', align-items, flex-start);
	@include responsive.responsive-class('items-center', align-items, center);
	@include responsive.responsive-class('items-end', align-items, flex-end);
	@include responsive.responsive-class('items-stretch', align-items, stretch);
	@include responsive.responsive-class('items-baseline', align-items, baseline);

	// Justify
	@include responsive.responsive-class('justify-start', justify-content, flex-start);
	@include responsive.responsive-class('justify-center', justify-content, center);
	@include responsive.responsive-class('justify-end', justify-content, flex-end);
	@include responsive.responsive-class('justify-between', justify-content, space-between);
	@include responsive.responsive-class('justify-around', justify-content, space-around);
	@include responsive.responsive-class('justify-evenly', justify-content, space-evenly);

	// Gap
	@include responsive.responsive-class('gap-xs', gap, var(--spacing-xs));
	@include responsive.responsive-class('gap-s', gap, var(--spacing-s));
	@include responsive.responsive-class('gap-sm', gap, var(--spacing-sm));
	@include responsive.responsive-class('gap-m', gap, var(--spacing-m));
	@include responsive.responsive-class('gap-l', gap, var(--spacing-l));
	@include responsive.responsive-class('gap-xl', gap, var(--spacing-xl));
	@include responsive.responsive-class('gap-xxl', gap, var(--spacing-xxl));

	// Wrapping
	@include responsive.responsive-class('flex-nowrap', flex-wrap, nowrap);
	@include responsive.responsive-class('flex-wrap', flex-wrap, wrap);
	@include responsive.responsive-class('flex-wrap-reverse', flex-wrap, wrap-reverse);

	// Paddings
	@include responsive.paddings();

	.hoverable {
		@include hoverable.hoverable();
	}

	// Page container overflows
	@include responsive.responsive-class-block('container-overflow-small') {
		margin-left: -1rem;
		margin-right: -1rem;
		width: calc(100% + 2rem);
	}

	@include responsive.responsive-class-block('container-overflow-subtle') {
		margin-left: -2rem;
		margin-right: -2rem;
		width: calc(100% + 4rem);
	}

	@include responsive.responsive-class-block('container-overflow-fullscreen') {
		width: 100dvw;
		margin-left: calc(-50dvw + 50%);
	}
</style>
