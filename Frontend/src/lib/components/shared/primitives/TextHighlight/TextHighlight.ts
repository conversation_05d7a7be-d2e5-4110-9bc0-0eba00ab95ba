import type { HTMLAttributes } from 'svelte/elements';

import { cva } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import type { Childable } from '$lib/components/cva-constants';

const variants = {
	color: {
		primary: 'text-highlight-color-primary',
		success: 'text-highlight-color-success',
		error: 'text-highlight-color-error'
	}
};

const defaultVariants = {
	color: 'primary'
} as const;

type TextHighlightVariantProps = {
	color?: keyof typeof variants.color;
};

export const textHighlightCva = cva<TextHighlightVariantProps>(
	'text-highlight',
	variants,
	defaultVariants
);

export type TextHighlightProps = HTMLAttributes<HTMLSpanElement> &
	TextHighlightVariantProps &
	Actionable &
	Childable;
