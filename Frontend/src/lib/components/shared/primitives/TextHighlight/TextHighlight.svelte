<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { textHighlightCva, type TextHighlightProps } from './TextHighlight';

	let { children, color, use, class: className, ...props }: TextHighlightProps = $props();
</script>

<span class={textHighlightCva({ color }, className)} use:applyActions={use} {...props}>
	{@render children?.()}
</span>

<style lang="scss">
	.text-highlight {
		&-color-primary {
			color: var(--color-primary);
		}

		&-color-success {
			color: var(--color-success);
		}

		&-color-error {
			color: var(--color-error);
		}
	}
</style>
