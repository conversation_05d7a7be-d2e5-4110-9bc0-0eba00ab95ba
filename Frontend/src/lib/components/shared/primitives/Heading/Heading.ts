import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import type { Childable } from '$lib/components/cva-constants';

const variants = {
	as: {
		h1: '',
		h2: '',
		h3: '',
		h4: '',
		h5: '',
		h6: ''
	},
	size: {
		hero: 'heading-size-hero',
		'1': 'heading-size-1',
		'2': 'heading-size-2',
		'3': 'heading-size-3',
		'4': 'heading-size-4',
		text: 'heading-size-text',
		sub: 'heading-size-sub'
	},
	italic: {
		true: 'heading-italic',
		false: ''
	},
	align: {
		left: 'heading-align-left',
		center: 'heading-align-center',
		right: 'heading-align-right'
	},
	wrap: {
		wrap: 'heading-wrap-wrap',
		nowrap: 'heading-wrap-nowrap',
		pretty: 'heading-wrap-pretty',
		balance: 'heading-wrap-balance'
	},
	color: {
		default: 'heading-color-default',
		primary: 'heading-color-primary',
		secondary: 'heading-color-secondary',
		light: 'heading-color-light',
		inherited: 'heading-color-inherited'
	}
};

const defaultVariants = {
	color: 'inherited',
	as: 'h1',
	size: '1',
	align: 'left',
	wrap: 'wrap',
	italic: 'false'
} as const;

type HeadingVariantProps = {
	as?: keyof typeof variants.as;
	size?: ResponsiveType<keyof typeof variants.size>;
	italic?: keyof typeof variants.italic;
	align?: ResponsiveType<keyof typeof variants.align>;
	wrap?: ResponsiveType<keyof typeof variants.wrap>;
	color?: keyof typeof variants.color;
};

export const headingCva = cva<HeadingVariantProps>('heading', variants, defaultVariants);

export type HeadingProps = HTMLAttributes<HTMLHeadingElement> &
	HeadingVariantProps &
	Actionable &
	Childable;
