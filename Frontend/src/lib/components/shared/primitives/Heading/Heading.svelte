<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { headingCva, type HeadingProps } from './Heading';

	let {
		children,
		as,
		align,
		wrap,
		size,
		italic,
		color,
		use,
		class: className,
		...props
	}: HeadingProps = $props();
</script>

<svelte:element
	this={as ?? 'h1'}
	class={headingCva({ align, wrap, size, italic, color }, className)}
	use:applyActions={use}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	// Size
	@include responsive.responsive-class('heading-size-hero', font-size, var(--font-size-hero));
	@include responsive.responsive-class('heading-size-1', font-size, var(--font-size-1));
	@include responsive.responsive-class('heading-size-2', font-size, var(--font-size-2));
	@include responsive.responsive-class('heading-size-3', font-size, var(--font-size-3));
	@include responsive.responsive-class('heading-size-4', font-size, var(--font-size-4));
	@include responsive.responsive-class('heading-size-text', font-size, var(--font-size-text));
	@include responsive.responsive-class('heading-size-sub', font-size, var(--font-size-sub));

	// Alignment
	@include responsive.responsive-class('heading-align-left', text-align, left);
	@include responsive.responsive-class('heading-align-center', text-align, center);
	@include responsive.responsive-class('heading-align-right', text-align, right);

	// Wrapping
	@include responsive.responsive-class('heading-wrap-wrap', white-space, normal);
	@include responsive.responsive-class-block('heading-wrap-nowrap') {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	@include responsive.responsive-class-block('heading-wrap-pretty') {
		white-space: normal;
		text-wrap: pretty;
	}

	@include responsive.responsive-class-block('heading-wrap-balance') {
		white-space: normal;
		text-wrap: balance;
	}

	.heading-color {
		&-default {
			color: var(--color-dark);
		}

		&-light {
			color: var(--color-light);
		}

		&-primary {
			color: var(--color-primary);
		}

		&-secondary {
			color: var(--color-secondary-text);
		}

		&-inherited {
			color: inherit;
		}
	}

	.heading-italic {
		font-style: italic;
	}
</style>
