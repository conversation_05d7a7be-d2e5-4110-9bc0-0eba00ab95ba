<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';
	import { exists } from '$lib/appmaxx/util/index.js';

	import { chipCva, type ChipProps } from './Chip';

	let {
		size,
		variant,
		Icon,
		children,
		use,
		hexColor,
		class: className,
		...props
	}: ChipProps = $props();

	const HEX_COLOR_RE = /^#[0-9A-Fa-f]{6}$/;

	if (exists(hexColor) && !HEX_COLOR_RE.test(hexColor)) {
		throw new Error('Invalid hex color, use #RRGGBB format (# and 6 HEX chars)');
	}
</script>

<div
	class={chipCva({ variant, size }, className)}
	class:chip-custom-color={exists(hexColor)}
	style:--chip-bg="{hexColor}30"
	style:--chip-fg={hexColor}
	use:applyActions={use}
	{...props}
>
	{#if Icon}
		<Icon size="1em" strokeWidth={2.5} />
	{/if}

	{@render children?.()}
</div>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.chip {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		border-radius: 10em;
		transition: opacity 0.2s ease;
		gap: var(--spacing-s);
		white-space: nowrap;
	}

	@include responsive.responsive-class(
		'chip-size-small',
		padding,
		var(--spacing-xs) var(--spacing-s)
	);
	@include responsive.responsive-class(
		'chip-size-medium',
		padding,
		var(--spacing-s) var(--spacing-m)
	);
	@include responsive.responsive-class(
		'chip-size-large',
		padding,
		var(--spacing-sm) var(--spacing-ml)
	);

	.chip-variant {
		&-light {
			background-color: var(--color-light);
			color: var(--color-dark);
			border: 1px solid var(--color-border-light);
		}

		&-dark {
			background-color: var(--color-dark);
			color: var(--color-light);
		}

		&-primary {
			background-color: var(--color-primary);
			color: var(--color-light);
		}

		&-error {
			background-color: var(--color-error);
			color: var(--color-light);
		}
	}

	.chip-custom-color {
		background-color: var(--chip-bg);
		color: var(--chip-fg);
		border: 1px solid var(--chip-fg);
	}
</style>
