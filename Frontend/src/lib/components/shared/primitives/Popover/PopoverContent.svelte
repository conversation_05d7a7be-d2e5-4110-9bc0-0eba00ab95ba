<script lang="ts">
	import type { Snippet } from 'svelte';
	import type { Action } from 'svelte/action';
	import { fly } from 'svelte/transition';

	import { clickOutside } from '$lib/appmaxx/svelte-actions';
	import { Card } from '$lib/components';

	let { children, floatingContent, handleClickOutside, isOpen } = $props<{
		children: Snippet;
		floatingContent: Action;
		handleClickOutside: () => void;
		isOpen: boolean;
	}>();
</script>

{#if isOpen}
	<div
		class="popover-content"
		use:clickOutside={{ ignoredElementsSelectors: ['.popover-target'] }}
		onclickoutside={handleClickOutside}
		in:fly={{ y: 5, duration: 300 }}
		out:fly={{ y: 5, duration: 300 }}
		use:floatingContent
	>
		<Card shadow="true" paddingX="none" paddingY="none">
			<div class="popover-content-inner">
				{@render children()}
			</div>
		</Card>
	</div>
{/if}

<style lang="scss">
	.popover-content {
		z-index: var(--z-index-popover);
		display: flex;
		flex-direction: column;
		max-height: min(30rem, 50dvh);
		overflow-y: auto;

		.popover-content-inner {
			display: flex;
			flex-direction: column;
			max-height: min(30rem, 50dvh);
			overflow-y: auto;
			padding: var(--spacing-m);
		}
	}
</style>
