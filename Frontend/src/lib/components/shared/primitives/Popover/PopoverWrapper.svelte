<script lang="ts">
	import { flip, offset, shift } from '@floating-ui/dom';
	import { ChevronDown } from '@lucide/svelte';
	import type { Snippet } from 'svelte';
	import { createFloatingActions } from 'svelte-floating-ui';

	import PopoverContent from '$lib/components/shared/primitives/Popover/PopoverContent.svelte';

	let { target, content } = $props<{ target: Snippet; content: Snippet }>();

	const [floatingRef, floatingContent] = createFloatingActions({
		strategy: 'fixed',
		placement: 'bottom',
		middleware: [offset(16), flip(), shift()]
	});

	let isOpen = $state(false);

	const handleTargetClick = () => {
		isOpen = !isOpen;
	};

	const handleClickOutside = () => {
		isOpen = false;
	};
</script>

<button class="popover-target" onclick={handleTargetClick} use:floatingRef>
	{@render target?.()}

	<span class="icon-container" class:opened={isOpen}>
		<ChevronDown size="1.1em" strokeWidth={2} />
	</span>
</button>

<PopoverContent {isOpen} {floatingContent} {handleClickOutside}>
	{@render content?.()}
</PopoverContent>

<style lang="scss">
	@use '$lib/assets/styles/hoverable' as hoverable;

	.popover-target {
		background: none;
		border: none;
		cursor: pointer;
		font-size: 1rem;
		display: inline-flex;
		align-items: center;
		gap: var(--spacing-xs);

		@include hoverable.hoverable();

		.icon-container {
			display: inline-flex;
			transition: transform 0.2s ease;
			transform: rotate(0deg);

			&.opened {
				transform: rotate(-180deg);
			}
		}
	}
</style>
