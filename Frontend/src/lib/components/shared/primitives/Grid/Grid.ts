import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import { type Childable, paddingX, paddingY } from '$lib/components/cva-constants';

const variants = {
	inline: {
		false: 'grid',
		true: 'inline-grid'
	},
	rows: {
		'1': 'grid-rows-1',
		'2': 'grid-rows-2',
		'3': 'grid-rows-3',
		'4': 'grid-rows-4',
		auto: 'grid-rows-auto',
		custom: '' // For custom row templates
	},
	cols: {
		'1': 'grid-cols-1',
		'2': 'grid-cols-2',
		'3': 'grid-cols-3',
		'4': 'grid-cols-4',
		auto: 'grid-cols-auto',
		custom: '' // For custom column templates
	},
	gap: {
		none: '',
		xs: 'gap-xs',
		s: 'gap-s',
		sm: 'gap-sm',
		m: 'gap-m',
		l: 'gap-l',
		xl: 'gap-xl',
		xxl: 'gap-xxl'
	},
	gapX: {
		none: '',
		xs: 'gap-x-xs',
		s: 'gap-x-s',
		sm: 'gap-x-sm',
		m: 'gap-x-m',
		l: 'gap-x-l',
		xl: 'gap-x-xl',
		xxl: 'gap-x-xxl'
	},
	gapY: {
		none: '',
		xs: 'gap-y-xs',
		s: 'gap-y-s',
		sm: 'gap-y-sm',
		m: 'gap-y-m',
		l: 'gap-y-l',
		xl: 'gap-y-xl',
		xxl: 'gap-y-xxl'
	},
	alignItems: {
		start: 'items-start',
		center: 'items-center',
		end: 'items-end',
		stretch: 'items-stretch'
	},
	justifyItems: {
		start: 'justify-items-start',
		center: 'justify-items-center',
		end: 'justify-items-end',
		stretch: 'justify-items-stretch'
	},
	alignContent: {
		start: 'content-start',
		center: 'content-center',
		end: 'content-end',
		between: 'content-between',
		around: 'content-around',
		evenly: 'content-evenly',
		stretch: 'content-stretch'
	},
	justifyContent: {
		start: 'justify-start',
		center: 'justify-center',
		end: 'justify-end',
		between: 'justify-between',
		around: 'justify-around',
		evenly: 'justify-evenly',
		stretch: 'justify-stretch'
	},
	paddingX,
	paddingY
};

const defaultVariants = {
	inline: 'false',
	rows: 'auto',
	cols: 'auto',
	gap: 'none',
	gapX: 'none',
	gapY: 'none',
	alignItems: 'stretch',
	justifyItems: 'stretch',
	alignContent: 'start',
	justifyContent: 'start',
	paddingX: 'none',
	paddingY: 'none'
} as const;

type GridVariantProps = {
	inline?: keyof typeof variants.inline;
	rows?: ResponsiveType<keyof typeof variants.rows>;
	cols?: ResponsiveType<keyof typeof variants.cols>;
	gap?: ResponsiveType<keyof typeof variants.gap>;
	gapX?: ResponsiveType<keyof typeof variants.gapX>;
	gapY?: ResponsiveType<keyof typeof variants.gapY>;
	alignItems?: keyof typeof variants.alignItems;
	justifyItems?: keyof typeof variants.justifyItems;
	alignContent?: keyof typeof variants.alignContent;
	justifyContent?: keyof typeof variants.justifyContent;
	paddingX?: ResponsiveType<keyof typeof variants.paddingX>;
	paddingY?: ResponsiveType<keyof typeof variants.paddingY>;
};

export const gridCva = cva<GridVariantProps>('grid', variants, defaultVariants);

type CustomTemplateProps = {
	rowTemplate?: string;
	colTemplate?: string;
	centerOrphan?: boolean;
};

export type GridDivProps = { as?: 'div' } & HTMLAttributes<HTMLDivElement>;

export type GridSectionProps = { as: 'section' } & HTMLAttributes<HTMLElement>;

export type GridProps = (GridDivProps | GridSectionProps) &
	GridVariantProps &
	CustomTemplateProps &
	Actionable &
	Childable;
