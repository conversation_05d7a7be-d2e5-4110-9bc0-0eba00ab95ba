<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { gridCva, type GridProps } from './Grid';

	let {
		children,
		as = 'div',
		inline,
		rows,
		cols,
		gap,
		gapX,
		gapY,
		paddingX,
		paddingY,
		alignItems,
		justifyItems,
		alignContent,
		justifyContent,
		rowTemplate,
		colTemplate,
		class: className,
		use,
		style,
		// centerOrphan,
		...props
	}: GridProps = $props();

	// let wrapperElement: HTMLElement;
	// let orphanWrapper: HTMLDivElement | null = $state(null);
	// let orphanLimiter: HTMLDivElement | null = $state(null);
	// let windowWidth = $state(0);
	// let numberOfCols = $derived.by(() => {
	// 	windowWidth;
	// 	if (!wrapperElement) return 0;
	// 	const styles = getComputedStyle(wrapperElement);
	// 	const gridTemplateColumns = styles.getPropertyValue('grid-template-columns');
	// 	return gridTemplateColumns.split(' ').length;
	// });
	// let hasOrphan = $derived.by(() => {
	// 	return wrapperElement.children.length % numberOfCols !== 0;
	// });

	// Create a custom style for templates
	let customStyle = '';
	if (rowTemplate && rows === 'custom') {
		customStyle += `grid-template-rows: ${rowTemplate}; `;
	}
	if (colTemplate && cols === 'custom') {
		customStyle += `grid-template-columns: ${colTemplate}; `;
	}

	// Combine with user style
	const finalStyle = customStyle + (style || '');

	// onMount(() => {
	// 	if (!centerOrphan) return;
	// 	const lastChild = wrapperElement.children[wrapperElement.children.length - 1];
	// 	const wrapper = document.createElement('div');
	// 	const limiter = document.createElement('div');
	// 	limiter.style.display = 'flex';
	// 	wrapper.style.gridColumn = 'span/1';
	// 	wrapper.style.display = 'flex';
	// 	wrapper.style.justifyContent = 'center';
	// 	limiter.appendChild(lastChild);
	// 	wrapper.appendChild(limiter);
	// 	wrapperElement.appendChild(wrapper);
	// 	orphanWrapper = wrapper;
	// 	orphanLimiter = limiter;
	// });

	// $effect(() => {
	// 	if (!centerOrphan) return;
	// 	if (!orphanWrapper) return;
	// 	if (hasOrphan) {
	// 		orphanWrapper.style.gridColumn = '1/-1';
	// 		orphanWrapper.style.display = 'flex';
	// 		return;
	// 	}

	// 	orphanWrapper.style.display = 'contents';
	// });

	// $effect(() => {
	// 	windowWidth;
	// 	if (!centerOrphan) return;
	// 	if (!orphanLimiter) return;
	// 	if (hasOrphan && wrapperElement.firstChild) {
	// 		orphanLimiter.style.width = getComputedStyle(wrapperElement.children[0]).width;
	// 	} else {
	// 		orphanLimiter.style.width = '';
	// 	}
	// });
</script>

<!-- <svelte:window bind:innerWidth={windowWidth} /> -->

<svelte:element
	this={as}
	class={gridCva(
		{
			inline,
			rows,
			cols,
			gap,
			gapX,
			gapY,
			alignItems,
			justifyItems,
			alignContent,
			justifyContent,
			paddingX,
			paddingY
		},
		className
	)}
	style={finalStyle}
	use:applyActions={use}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.grid {
		display: grid;
	}

	.inline-grid {
		display: inline-grid;
	}

	// Grid template rows
	@include responsive.responsive-class(
		'grid-rows-1',
		grid-template-rows,
		repeat(1, minmax(0, 1fr))
	);
	@include responsive.responsive-class(
		'grid-rows-2',
		grid-template-rows,
		repeat(2, minmax(0, 1fr))
	);
	@include responsive.responsive-class(
		'grid-rows-3',
		grid-template-rows,
		repeat(3, minmax(0, 1fr))
	);
	@include responsive.responsive-class(
		'grid-rows-4',
		grid-template-rows,
		repeat(4, minmax(0, 1fr))
	);
	@include responsive.responsive-class('grid-rows-auto', grid-template-rows, auto);

	// Grid template columns
	@include responsive.responsive-class(
		'grid-cols-1',
		grid-template-columns,
		repeat(1, minmax(0, 1fr))
	);
	@include responsive.responsive-class(
		'grid-cols-2',
		grid-template-columns,
		repeat(2, minmax(0, 1fr))
	);
	@include responsive.responsive-class(
		'grid-cols-3',
		grid-template-columns,
		repeat(3, minmax(0, 1fr))
	);
	@include responsive.responsive-class(
		'grid-cols-4',
		grid-template-columns,
		repeat(4, minmax(0, 1fr))
	);
	@include responsive.responsive-class('grid-cols-auto', grid-template-columns, auto);

	// Alignment
	.items-start {
		align-items: start;
	}

	.items-center {
		align-items: center;
	}

	.items-end {
		align-items: end;
	}

	.items-stretch {
		align-items: stretch;
	}

	.justify-items-start {
		justify-items: start;
	}

	.justify-items-center {
		justify-items: center;
	}

	.justify-items-end {
		justify-items: end;
	}

	.justify-items-stretch {
		justify-items: stretch;
	}

	.content-start {
		align-content: start;
	}

	.content-center {
		align-content: center;
	}

	.content-end {
		align-content: end;
	}

	.content-between {
		align-content: space-between;
	}

	.content-around {
		align-content: space-around;
	}

	.content-evenly {
		align-content: space-evenly;
	}

	.content-stretch {
		align-content: stretch;
	}

	.justify-start {
		justify-content: start;
	}

	.justify-center {
		justify-content: center;
	}

	.justify-end {
		justify-content: end;
	}

	.justify-between {
		justify-content: space-between;
	}

	.justify-around {
		justify-content: space-around;
	}

	.justify-evenly {
		justify-content: space-evenly;
	}

	.justify-stretch {
		justify-content: stretch;
	}

	// Gap utilities (reusing from Flex component)
	@include responsive.responsive-class('gap-xs', gap, var(--spacing-xs));
	@include responsive.responsive-class('gap-s', gap, var(--spacing-s));
	@include responsive.responsive-class('gap-sm', gap, var(--spacing-sm));
	@include responsive.responsive-class('gap-m', gap, var(--spacing-m));
	@include responsive.responsive-class('gap-l', gap, var(--spacing-l));
	@include responsive.responsive-class('gap-xl', gap, var(--spacing-xl));
	@include responsive.responsive-class('gap-xxl', gap, var(--spacing-xxl));

	// Column gap utilities
	@include responsive.responsive-class('gap-x-xs', column-gap, var(--spacing-xs));
	@include responsive.responsive-class('gap-x-s', column-gap, var(--spacing-s));
	@include responsive.responsive-class('gap-x-sm', column-gap, var(--spacing-sm));
	@include responsive.responsive-class('gap-x-m', column-gap, var(--spacing-m));
	@include responsive.responsive-class('gap-x-l', column-gap, var(--spacing-l));
	@include responsive.responsive-class('gap-x-xl', column-gap, var(--spacing-xl));
	@include responsive.responsive-class('gap-x-xxl', column-gap, var(--spacing-xxl));

	// Row gap utilities
	@include responsive.responsive-class('gap-y-xs', row-gap, var(--spacing-xs));
	@include responsive.responsive-class('gap-y-s', row-gap, var(--spacing-s));
	@include responsive.responsive-class('gap-y-sm', row-gap, var(--spacing-sm));
	@include responsive.responsive-class('gap-y-m', row-gap, var(--spacing-m));
	@include responsive.responsive-class('gap-y-l', row-gap, var(--spacing-l));
	@include responsive.responsive-class('gap-y-xl', row-gap, var(--spacing-xl));
	@include responsive.responsive-class('gap-y-xxl', row-gap, var(--spacing-xxl));

	// Paddings
	@include responsive.paddings();
</style>
