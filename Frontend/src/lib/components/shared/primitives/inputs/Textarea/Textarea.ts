import type { Component } from 'svelte';
import type { HTMLTextareaAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';

const variants = {
	size: {
		normal: 'textarea-normal',
		large: 'textarea-large'
	},
	fullWidth: {
		true: 'textarea-full-width',
		false: ''
	},
	rounded: {
		true: 'textarea-rounded',
		false: ''
	}
};

const defaultVariants = {
	size: 'normal',
	variant: 'default',
	fullWidth: 'false',
	rounded: 'false'
} as const;

export type TextareaVariants = {
	size?: ResponsiveType<keyof typeof variants.size>;
	fullWidth?: ResponsiveType<keyof typeof variants.fullWidth>;
	rounded?: keyof typeof variants.rounded;
};

export const textareaCva = cva<TextareaVariants>('textarea', variants, defaultVariants);

interface CustomTextareaProps {
	errors?: string[];
	Icon?: Component;
	rows?: number;
}

export type TextareaProps = Omit<HTMLTextareaAttributes, 'size'> &
	TextareaVariants &
	CustomTextareaProps &
	Actionable;
