<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';
	import { exists } from '$lib/appmaxx/util';
	import { InputContainer } from '$lib/components';

	import { textareaCva, type TextareaProps } from './Textarea';

	let {
		value = $bindable<string | undefined>(undefined),
		placeholder,
		disabled = false,
		required = false,
		errors = [],
		name,
		id,
		size,
		fullWidth,
		rounded,
		Icon,
		rows = 5,
		style,
		use,
		...props
	}: TextareaProps = $props();
</script>

<InputContainer alignErrors="top" {errors} {fullWidth}>
	{#snippet input(toggleErrorsTooltip, hasErrors)}
		<div
			class="textarea-wrapper {textareaCva({ size, fullWidth, rounded })}"
			{style}
			class:textarea-error={hasErrors}
		>
			{#if exists(Icon)}
				<span class="icon-container" class:error={hasErrors}>
					<Icon size="1.25em" strokeWidth={2} />
				</span>
			{/if}

			<textarea
				onfocus={() => toggleErrorsTooltip(false)}
				onblur={() => toggleErrorsTooltip(true)}
				aria-invalid={hasErrors}
				aria-errormessage="${name}-error"
				bind:value
				class:has-icon={exists(Icon)}
				{name}
				{id}
				{placeholder}
				{disabled}
				{required}
				{rows}
				use:applyActions={use}
				{...props}
			></textarea>
		</div>
	{/snippet}
</InputContainer>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.textarea-wrapper {
		position: relative;
		font-size: var(--font-size-text);
		background-color: var(--color-light);
		border: 1px solid var(--color-border-light);
		border-radius: var(--border-radius-s);
		outline: none;
		transition:
			border-color 0.2s ease,
			box-shadow 0.2s ease;

		&:focus-within,
		&:focus-visible {
			border-color: var(--color-primary);
			box-shadow: 0 0 0 2px rgba(66, 126, 244, 0.2);
			outline: unset !important;
		}

		&:has(textarea:disabled) {
			opacity: 0.6;
			cursor: not-allowed;
			background-color: var(--color-section);
		}

		.icon-container {
			position: absolute;
			top: 1rem;
			height: 1.25em;
			color: var(--color-icon-dark);
		}

		textarea {
			width: 100%;
			border: none;
			font-size: var(--font-size-text);
			background: none;
			resize: vertical;

			&:focus,
			&:focus-within {
				outline: none;
			}
		}
	}

	// Size variants
	@include responsive.responsive-class-block('textarea-normal') {
		border-radius: var(--border-radius-s);

		textarea {
			padding: var(--spacing-sm) var(--spacing-ml);

			&.has-icon {
				padding-left: 3rem;
			}
		}

		.icon-container {
			left: calc(3rem / 2);
			translate: -33% 0;
		}
	}

	@include responsive.responsive-class-block('textarea-large') {
		border-radius: 0.75rem;

		textarea {
			padding: var(--spacing-sm) var(--spacing-l);

			&.has-icon {
				padding-left: 3.5rem;
			}
		}

		.icon-container {
			left: calc(3.5rem / 2);
			translate: -33% 0;
		}
	}

	// Variant styles
	.textarea-error {
		border: 1px solid var(--color-error);

		&:has(textarea:focus-within) {
			box-shadow: 0 0 0 2px rgba(244, 66, 66, 0.2);
			border-color: var(--color-error);
		}
	}

	.textarea-rounded {
		border-radius: 10em;
	}

	@include responsive.responsive-class('textarea-full-width', width, 100%);
</style>
