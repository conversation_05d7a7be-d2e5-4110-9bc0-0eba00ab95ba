import type { Component } from 'svelte';
import type { HTMLInputAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';

const variants = {
	size: {
		normal: 'input-normal',
		large: 'input-large'
	},
	fullWidth: {
		true: 'input-full-width',
		false: ''
	},
	rounded: {
		true: 'input-rounded',
		false: ''
	}
};

const defaultVariants = {
	size: 'normal',
	variant: 'default',
	fullWidth: 'false',
	rounded: 'false'
} as const;

type InputVariants = {
	size?: ResponsiveType<keyof typeof variants.size>;
	fullWidth?: ResponsiveType<keyof typeof variants.fullWidth>;
	rounded?: keyof typeof variants.rounded;
};

type InputType = 'text' | 'email' | 'password' | 'number' | 'date';

export const inputCva = cva<InputVariants>('input', variants, defaultVariants);

interface CustomInputProps {
	type?: InputType;
	errors?: string[];
	success?: boolean;
	Icon?: Component;
}

export type InputProps = Omit<HTMLInputAttributes, 'size' | 'type'> &
	InputVariants &
	CustomInputProps &
	Actionable;
