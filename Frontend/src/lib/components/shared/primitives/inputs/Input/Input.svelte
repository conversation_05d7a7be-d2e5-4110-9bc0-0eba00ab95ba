<script lang="ts">
	import { Eye, EyeOff } from '@lucide/svelte';

	import { applyActions } from '$lib/appmaxx/svelte-actions';
	import { exists } from '$lib/appmaxx/util';
	import obeliskIcon from '$lib/assets/img/misc/obelisk.svg?url';
	import { InputContainer } from '$lib/components';

	import { inputCva, type InputProps } from './Input';

	let {
		type = 'text',
		value = $bindable(undefined),
		placeholder,
		disabled = false,
		required = false,
		errors = [],
		name,
		id,
		size,
		fullWidth,
		rounded,
		Icon,
		style,
		use,
		...props
	}: InputProps = $props();

	let isPassword = $derived(type === 'password');
	let isPasswordVisible = $state(false);

	const handleTogglePasswordVisibility = () => {
		isPasswordVisible = !isPasswordVisible;
	};
</script>

<InputContainer {errors} {fullWidth}>
	{#snippet input(toggleErrorsTooltip: (visible: boolean) => void, hasErrors: boolean)}
		<div
			class="input {inputCva({ size, fullWidth, rounded })}"
			{style}
			class:input-error={hasErrors}
		>
			{#if required}
				<span class="required-indicator" class:error={hasErrors}>
					<img src={obeliskIcon} alt="*" height="8" width="8" />
				</span>
			{/if}
			{#if exists(Icon)}
				<span class="icon-container" class:error={hasErrors}>
					<Icon size="1.25em" strokeWidth={2} />
				</span>
			{/if}
			<input
				onfocus={() => toggleErrorsTooltip(false)}
				onblur={() => toggleErrorsTooltip(true)}
				type={isPassword && isPasswordVisible ? 'text' : type}
				aria-invalid={hasErrors}
				aria-errormessage="${name}-error"
				bind:value
				class:has-icon={exists(Icon)}
				class:is-password={isPassword}
				{name}
				{id}
				{placeholder}
				{disabled}
				{required}
				use:applyActions={use}
				{...props}
			/>

			{#if isPassword}
				<button
					type="button"
					class="password-toggle"
					onclick={handleTogglePasswordVisibility}
				>
					{#if !isPasswordVisible}
						<Eye size="1.25em" strokeWidth={2} />
					{:else}
						<EyeOff size="1.25em" strokeWidth={2} />
					{/if}
				</button>
			{/if}
		</div>
	{/snippet}
</InputContainer>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.input {
		position: relative;
		font-size: var(--font-size-text);
		background-color: var(--color-light);
		border: 1px solid var(--color-border-light);
		border-radius: var(--border-radius-s);
		outline: none;
		transition:
			border-color 0.2s ease,
			box-shadow 0.2s ease;

		&:focus-within {
			border-color: var(--color-primary);
			box-shadow: 0 0 0 2px rgba(66, 126, 244, 0.2);

			.required-indicator {
				border-color: var(--color-primary);
				box-shadow: 0 0 0 2px rgba(66, 126, 244, 0.2);

				&.error {
					box-shadow: 0 0 0 2px rgba(244, 66, 66, 0.2);
				}
			}
		}

		&:has(input:disabled) {
			opacity: 0.6;
			cursor: not-allowed;
			background-color: var(--color-section);
		}

		.icon-container {
			position: absolute;
			top: 50%;
			translate: 0 -50%;
			height: 1.25em;
			color: var(--color-icon-dark);
		}

		.required-indicator {
			position: absolute;
			top: -6px;
			left: -6px;
			color: var(--color-error);
			font-size: 1.25rem;
			line-height: 1;
			pointer-events: none;
			padding: 5px;
			background-color: white;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
			border-radius: 6px;
			border: 1px solid var(--color-border-light);
			transition:
				border-color 0.2s ease,
				box-shadow 0.2s ease;

			&.error {
				border-color: var(--color-error);
			}
		}

		input {
			width: 100%;
			height: 100%;
			border: none;
			font-size: var(--font-size-text);
			background: none;

			&.without-icon {
				padding-left: 0;
			}

			&[type='date'] {
				appearance: none;

				&::-webkit-calendar-picker-indicator {
					cursor: pointer;
				}
			}

			&:focus,
			&:focus-within {
				outline: none;
			}
		}

		.password-toggle {
			position: absolute;
			top: 50%;
			translate: 0 -50%;
			display: flex;
			align-items: center;
			background: none;
			border: none;
			cursor: pointer;
			color: var(--color-icon-dark);
		}
	}

	// Size variants
	@include responsive.responsive-class-block('input-normal') {
		height: 3rem;
		border-radius: var(--border-radius-s);

		input {
			padding: 0 var(--spacing-ml);
			border-radius: var(--border-radius-s);

			&.has-icon {
				padding-left: 3rem;
			}

			&.is-password {
				padding-right: 3rem;
			}
		}

		.icon-container {
			left: calc(3rem / 2);
			translate: -33% -50%;
		}

		.password-toggle {
			right: calc(3rem / 2);
			translate: 33% -50%;
		}
	}

	@include responsive.responsive-class-block('input-large') {
		height: 3.5rem;
		border-radius: 0.75rem;

		input {
			padding: 0 var(--spacing-l);
			border-radius: 0.75rem;

			&.has-icon {
				padding-left: 3.5rem;
			}

			&.is-password {
				padding-right: 3.5rem;
			}
		}

		.icon-container {
			left: calc(3.5rem / 2);
			translate: -33% -50%;
		}

		.password-toggle {
			right: calc(3.5rem / 2);
			translate: 33% -50%;
		}
	}

	// Variant styles
	.input-error {
		border: 1px solid var(--color-error);

		&:has(input:focus-within) {
			box-shadow: 0 0 0 2px rgba(244, 66, 66, 0.2);
			border-color: var(--color-error);
		}
	}

	.input-rounded {
		border-radius: 10em;
	}

	@include responsive.responsive-class('input-full-width', width, 100%);
</style>
