import type { Component } from 'svelte';
import type { HTMLSelectAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';

const variants = {
	size: {
		normal: 'select-normal',
		large: 'select-large'
	},
	fullWidth: {
		true: 'select-full-width',
		false: ''
	},
	rounded: {
		true: 'select-rounded',
		false: ''
	}
};

const defaultVariants = {
	size: 'normal',
	variant: 'default',
	fullWidth: 'false',
	rounded: 'false'
} as const;

type SelectVariants = {
	size?: ResponsiveType<keyof typeof variants.size>;
	fullWidth?: ResponsiveType<keyof typeof variants.fullWidth>;
	rounded?: keyof typeof variants.rounded;
};
export const selectCva = cva<SelectVariants>('input', variants, defaultVariants);

interface CustomSelectProps {
	errors?: string[];
	success?: boolean;
	Icon?: Component;
    options: { value: string; label: string }[];
}

export type SelectProps = HTMLSelectAttributes & SelectVariants & Actionable & CustomSelectProps;
