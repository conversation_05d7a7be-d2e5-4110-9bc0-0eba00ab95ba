<script lang="ts">

    import {ChevronDown} from '@lucide/svelte'

    import {applyActions} from '$lib/appmaxx/svelte-actions';
    import {exists} from '$lib/appmaxx/util';
    import obeliskIcon from '$lib/assets/img/misc/obelisk.svg?url';
    import {InputContainer} from '$lib/components';

    import {selectCva, type SelectProps} from './Select';

    let {
        value = $bindable(undefined),
        Icon,
        placeholder,
        disabled = false,
        required = false,
        errors = [],
        name,
        id,
        size,
        options,
        fullWidth,
        rounded,
        style,
        use,
        ...props
    }: SelectProps = $props();
</script>

<InputContainer {errors} {fullWidth}>
    {#snippet input(toggleErrorsTooltip: (visible: boolean) => void, hasErrors: boolean)}
        <div
                class="input {selectCva({ size, fullWidth, rounded })}"
                {style}
                class:input-error={hasErrors}
        >
            {#if required}
				<span class="required-indicator" class:error={hasErrors}>
					<img src={obeliskIcon} alt="*" height="8" width="8"/>
				</span>
            {/if}
            {#if exists(Icon)}
				<span class="icon-container" class:error={hasErrors}>
					<Icon size="1.25em" strokeWidth={2}/>
				</span>
            {/if}
            <select
                    onfocus={() => toggleErrorsTooltip(false)}
                    onblur={() => toggleErrorsTooltip(true)}
                    aria-invalid={hasErrors}
                    aria-errormessage="${name}-error"
                    class:has-icon={exists(Icon)}
                    bind:value
                    data-option-value={value}
                    {name}
                    {id}
                    {placeholder}
                    {disabled}
                    {required}
                    use:applyActions={use}
                    {...props}
            >
                {#each options as option (option.value)}
                    <option value={option.value}>{option.label}</option>
                {/each}
            </select>

            <span class="select-icon-container" class:error={hasErrors}>
					<ChevronDown size="1.25em" strokeWidth={2}/>
            </span>
        </div>
    {/snippet}
</InputContainer>

<style lang="scss">
  @use '$lib/assets/styles/responsive' as responsive;

  .input {
    position: relative;
    font-size: var(--font-size-text);
    background-color: var(--color-light);
    border: 1px solid var(--color-border-light);
    border-radius: var(--border-radius-s);
    outline: none;
    transition: border-color 0.2s ease,
    box-shadow 0.2s ease;

    &:focus-within {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(66, 126, 244, 0.2);

      .required-indicator {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 2px rgba(66, 126, 244, 0.2);

        &.error {
          box-shadow: 0 0 0 2px rgba(244, 66, 66, 0.2);
        }
      }
    }

    &:has(select:disabled) {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: var(--color-section);
    }

    .icon-container {
      position: absolute;
      top: 50%;
      translate: 0 -50%;
      height: 1.25em;
      color: var(--color-icon-dark);
      pointer-events: none;
    }

    .select-icon-container{
      position: absolute;
      top: 50%;
      translate: 0 50%;
      height: 1.25em;
      color: var(--color-icon-dark);
      pointer-events: none;
    }

    .required-indicator {
      position: absolute;
      top: -6px;
      left: -6px;
      color: var(--color-error);
      font-size: 1.25rem;
      line-height: 1;
      pointer-events: none;
      padding: 5px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-radius: 6px;
      border: 1px solid var(--color-border-light);
      transition: border-color 0.2s ease,
      box-shadow 0.2s ease;

      &.error {
        border-color: var(--color-error);
      }
    }

    select {
      width: 100%;
      height: 100%;
      border: none;
      font-size: var(--font-size-text);
      background: none;

      appearance: none; /* Standard */
      -webkit-appearance: none; /* Safari / Chrome */
      -moz-appearance: none; /* Firefox */

      option {
        outline: none;
      }

      &.without-icon {
        padding-left: 0;
      }

      &.has-icon {
        padding-left: 3rem;
      }

      &[data-option-value=""]{
        color: var(--color-secondary-text) !important;
      }

      &:focus,
      &:focus-within {
        outline: none;
      }

    }
  }

  // Size variants
  @include responsive.responsive-class-block('select-normal') {
    height: 3rem;
    border-radius: var(--border-radius-s);

    select {
      padding: 0 var(--spacing-ml);
      border-radius: var(--border-radius-s);
    }

    .icon-container {
      left: calc(3rem / 2);
      translate: -33% -50%;
    }

    .select-icon-container {
      right: calc(3rem / 2);
      translate: 33% -50%;
    }
  }

  @include responsive.responsive-class-block('select-large') {
    height: 3.5rem;
    border-radius: 0.75rem;

    select {
      padding: 0 var(--spacing-l);
      border-radius: 0.75rem;
    }

    .icon-container {
      left: calc(3rem / 2);
      translate: -33% -50%;
    }

    .select-icon-container {
      right: calc(3rem / 2);
      translate: 33% -50%;
    }
  }

  // Variant styles
  .input-error {
    border: 1px solid var(--color-error);

    &:has(select:focus-within) {
      box-shadow: 0 0 0 2px rgba(244, 66, 66, 0.2);
      border-color: var(--color-error);
    }
  }

  .input-rounded {
    border-radius: 10em;
  }

  @include responsive.responsive-class('select-full-width', width, 100%);
</style>
