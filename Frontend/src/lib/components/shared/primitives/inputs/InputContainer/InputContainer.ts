import type { Snippet } from 'svelte';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';

const variants = {
	fullWidth: {
		true: 'input-container-full-width',
		false: ''
	}
};

const defaultVariants = {
	fullWidth: 'false'
} as const;

type InputContainerVariants = {
	fullWidth?: ResponsiveType<keyof typeof variants.fullWidth>;
};

export type InputContainerProps = InputContainerVariants & {
	input: Snippet<[toggleErrorsTooltip: (visible: boolean) => void, hasErrors: boolean]>;
	errors?: string[];
	alignErrors?: 'top' | 'middle' | 'bottom';
};

export const inputContainerCva = cva<InputContainerVariants>(
	'input-container',
	variants,
	defaultVariants
);
