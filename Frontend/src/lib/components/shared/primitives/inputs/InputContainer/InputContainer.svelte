<script lang="ts">
	import { TriangleAlert } from '@lucide/svelte';
	import { fly } from 'svelte/transition';

	import { Flex, Spacer } from '$lib/components';
	import { t } from '$lib/translations/config';

	import { inputContainerCva, type InputContainerProps } from './InputContainer';

	let { input, fullWidth, errors = [], alignErrors = 'middle' }: InputContainerProps = $props();
	let errorsTooltipVisible = $derived(errors.length > 0);
	let hasErrors = $derived(errors.length > 0);

	function toggleErrorsTooltip(visible: boolean) {
		errorsTooltipVisible = errors.length > 0 ? visible : false;
	}
</script>

<label class={inputContainerCva({ fullWidth })}>
	{@render input(toggleErrorsTooltip, hasErrors)}

	{#if errorsTooltipVisible}
		<span
			class={`errors-container errors-container--${alignErrors}`}
			transition:fly={{ y: 3, duration: 200 }}
		>
			<TriangleAlert size="1.25em" strokeWidth={2} />
			<Spacer direction="horizontal" size="sm" />

			<Flex direction="col" align="end">
				{#each errors as error (error)}
					<span class="input-error-message">{$t(error)}</span>
				{/each}
			</Flex>
		</span>
	{/if}
</label>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	.input-container {
		position: relative;
		display: flex;
		flex-direction: column;
		gap: var(--spacing-s);

		:global {
			&:has(input:disabled) {
				opacity: 0.75;
				cursor: not-allowed;
			}
		}
	}

	@include responsive.responsive-class('input-container-full-width', width, 100%);

	.errors-container {
		position: absolute;
		display: flex;
		background-color: var(--color-error);
		color: var(--color-light);
		align-items: center;
		padding: var(--spacing-xs) var(--spacing-sm);
		border-radius: var(--border-radius-s);
		right: var(--spacing-s);
		top: 50%;
		translate: 0 -50%;

		.input-error-message {
			font-size: var(--font-size-small);
		}
		&--top {
			top: 0.65rem;
			right: 0.55rem;
			translate: unset;
		}
	}
</style>
