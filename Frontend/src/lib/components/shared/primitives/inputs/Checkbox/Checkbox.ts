import type { Snippet } from 'svelte';
import type { HTMLInputAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';

const variants = {
	size: {
		normal: 'checkbox-size-normal',
		large: 'checkbox-size-large'
	}
};

const defaultVariants = {
	size: 'normal'
} as const;

type CheckboxVariants = {
	size?: ResponsiveType<keyof typeof variants.size>;
};

export const checkboxCva = cva<CheckboxVariants>('checkbox', variants, defaultVariants);

interface CustomCheckboxProps {
	errors?: string[];
	children?: Snippet;
}

export type CheckboxProps = Omit<HTMLInputAttributes, 'type' | 'size'> &
	CheckboxVariants &
	CustomCheckboxProps &
	Actionable;
