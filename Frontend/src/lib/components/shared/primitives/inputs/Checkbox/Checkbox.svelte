<script lang="ts">
	import { Check } from '@lucide/svelte';

	import { applyActions } from '$lib/appmaxx/svelte-actions';
	import { InputContainer } from '$lib/components';

	import { checkboxCva, type CheckboxProps } from './Checkbox';

	let {
		size,
		errors = [],
		checked = $bindable(true),
		children,
		use,
		...props
	}: CheckboxProps = $props();
</script>

<InputContainer {errors}>
	{#snippet input(clearErrors: () => void, errorsVisible: boolean)}
		<label use:applyActions={use}>
			<span class="checkbox-container">
				<input type="checkbox" onchange={() => clearErrors()} bind:checked {...props} />
				<span class={checkboxCva({ size })} class:checkbox-error={errorsVisible}>
					{#if checked}
						<Check size="1em" strokeWidth={2.5} />
					{/if}
				</span>
			</span>

			{#if children}
				<span class="checkbox-label">
					{@render children?.()}
				</span>
			{/if}
		</label>
	{/snippet}
</InputContainer>

<style lang="scss">
	@use '$lib/assets/styles/responsive' as responsive;

	label {
		display: flex;
		align-items: center;
		gap: var(--spacing-m);
		cursor: pointer;
	}

	.checkbox-container {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	input[type='checkbox'] {
		position: absolute;
		opacity: 0;
		width: 0;
		height: 0;
	}

	.checkbox {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: var(--color-light);
		border: 1px solid var(--color-border-light);
		border-radius: var(--border-radius-s);
		transition: all 0.2s ease;
		color: white;

		&:hover {
			border-color: var(--color-primary);
		}

		input:checked + & {
			background-color: var(--color-primary);
			border-color: var(--color-primary);

			&-error {
				background-color: var(--color-error);
				border-color: var(--color-error);
			}
		}

		input:focus + & {
			box-shadow: 0 0 0 2px rgba(66, 126, 244, 0.2);
		}

		input:disabled + & {
			opacity: 0.6;
			cursor: not-allowed;
			background-color: var(--color-section);
		}

		&-error {
			border-color: var(--color-error);
		}
	}

	@include responsive.responsive-class-block('checkbox-size-normal') {
		font-size: 1.125rem;
		width: 1.75rem;
		height: 1.75rem;
	}

	@include responsive.responsive-class-block('checkbox-size-large') {
		font-size: 1.25rem;
		width: 2.25rem;
		height: 2.25rem;
	}

	.checkbox-label {
		user-select: none;
	}
</style>
