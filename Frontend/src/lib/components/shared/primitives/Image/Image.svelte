<script lang="ts">
	import { applyActions } from '$lib/appmaxx/svelte-actions';

	import { type ImageProps } from './Image';

	let {
		src,
		alt,
		aspectRatio,
		srHidden,
		style,
		use,
		class: className,
		...props
	}: ImageProps = $props();

	let finalStyle = $derived.by(() => {
		if (aspectRatio) {
			return `aspect-ratio: ${aspectRatio};object-fit: cover;` + (style || '');
		}
		return style;
	});
</script>

<img
	{alt}
	{src}
	class={className}
	aria-hidden={srHidden}
	style={finalStyle}
	use:applyActions={use}
	{...props}
/>
