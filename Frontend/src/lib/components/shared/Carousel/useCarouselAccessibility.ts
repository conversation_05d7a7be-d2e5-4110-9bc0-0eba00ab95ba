import { exists } from '$lib/appmaxx/util';

export interface AccessibilityOptions {
	translations: {
		carouselPaused: string;
		carouselPlaying: string;
		carouselStarted: string;
		carouselStopped: string;
		itemOfTotal: (current: number, total: number) => string;
	};
}

export function useCarouselAccessibility() {
	let announcementMessage = '';
	let prefersReducedMotion = false;

	function initialize(mediaQuery?: MediaQueryList): void {
		if (exists(mediaQuery)) {
			prefersReducedMotion = mediaQuery.matches;
		}
	}

	function announce(message: string): string {
		announcementMessage = message;
		return message;
	}

	function announceItemChange(
		current: number,
		total: number,
		options: AccessibilityOptions
	): string {
		return announce(options.translations.itemOfTotal(current, total));
	}

	function shouldReduceMotion(): boolean {
		return prefersReducedMotion;
	}

	return {
		initialize,
		announce,
		announceItemChange,
		shouldReduceMotion,
		get message() {
			return announcementMessage;
		}
	};
}
