import { browser } from '$app/environment';
import { exists } from '$lib/appmaxx/util';

export interface AnimationOptions {
	speed: number;
	shouldAnimate: () => boolean;
	onTransitionComplete: () => void;
}

export function useCarouselAnimation() {
	let animationId: number | null = null;
	let cachedItemWidth = 0;
	let cachedGapWidth = 0;
	let needsRemeasure = true;
	let translateX = 0;

	function startAnimation(container: HTMLElement, options: AnimationOptions): void {
		if (!browser) return;

		if (animationId) {
			cancelAnimationFrame(animationId);
		}

		function animate() {
			if (!exists(container) || !options.shouldAnimate()) {
				animationId = requestAnimationFrame(animate);
				return;
			}

			if (needsRemeasure) {
				measureDimensions(container);
			}

			if (cachedItemWidth > 0) {
				const totalItemWidth = cachedItemWidth + cachedGapWidth;

				translateX += options.speed;
				container.style.setProperty('--translate-x', `-${translateX}px`);

				if (translateX >= totalItemWidth) {
					translateX -= totalItemWidth;
					container.style.setProperty('--translate-x', `-${translateX}px`);
					options.onTransitionComplete();
				}
			}

			animationId = requestAnimationFrame(animate);
		}

		animationId = requestAnimationFrame(animate);
	}

	function stopAnimation(): void {
		if (exists(animationId)) {
			cancelAnimationFrame(animationId);
			animationId = null;
		}
	}

	function resetPosition(container: HTMLElement): void {
		translateX = 0;
		container.style.setProperty('--translate-x', '0px');
	}

	function measureDimensions(container: HTMLElement): void {
		const firstItemEl = container.querySelector('.carousel-item');
		if (exists(firstItemEl)) {
			cachedItemWidth = firstItemEl.getBoundingClientRect().width;
			cachedGapWidth = parseFloat(getComputedStyle(container).columnGap || '0');
			needsRemeasure = false;
		}
	}

	function invalidateMeasurements(): void {
		needsRemeasure = true;
	}

	return {
		startAnimation,
		stopAnimation,
		resetPosition,
		measureDimensions,
		invalidateMeasurements
	};
}
