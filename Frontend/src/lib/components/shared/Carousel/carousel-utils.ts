import type { CarouselItem } from './Carousel';

// Safely clones an object, falling back to shallow copy if structuredClone fails
export function safeClone<T>(obj: T): T {
	try {
		return structuredClone(obj);
	} catch (e) {
		if (Array.isArray(obj)) {
			return [...obj] as unknown as T;
		} else if (obj && typeof obj === 'object') {
			return { ...obj };
		}
		return obj;
	}
}

// Duplicates items to ensure minimum count for smooth scrolling
export function duplicateItemsIfNeeded<T extends CarouselItem>(
	sourceItems: T[],
	minimumItemsToShow: number
): T[] {
	if (sourceItems.length === 0) return [];

	let result = [...sourceItems];
	let cloneIndex = 0;

	while (result.length < minimumItemsToShow) {
		const duplicates = sourceItems.map((item) => ({
			...safeClone(item),
			_cloneIndex: cloneIndex
		}));
		result = [...result, ...duplicates];
		cloneIndex++;
	}

	return result;
}

// Generates a unique key for carousel items
export function getItemKey<T extends CarouselItem>(item: T): string {
	return item.id + (item._cloneIndex !== undefined ? `-clone-${item._cloneIndex}` : '');
}
