<script lang="ts">
	import { ChevronLeft, ChevronRight, Pause, Play } from '@lucide/svelte';

	import { t } from '$lib/translations/config';

	let { previous, next, togglePause, isPaused, carouselId } = $props<{
		carouselId: string;
		isPaused: boolean;
		previous: () => void;
		next: () => void;
		togglePause: () => void;
	}>();
</script>

<div class="carousel-controls" aria-controls={carouselId}>
	<button
		type="button"
		class="carousel-control carousel-prev"
		aria-label={$t('carousel.controls.previousItem')}
		onclick={previous}
		onkeydown={(e) => e.key === 'Enter' && previous()}
	>
		<ChevronLeft size={18} aria-hidden="true" />
	</button>

	<button
		type="button"
		class="carousel-control carousel-toggle-play"
		aria-label={isPaused
			? $t('carousel.controls.playCarousel')
			: $t('carousel.controls.pauseCarousel')}
		aria-pressed={!isPaused}
		onclick={togglePause}
		onkeydown={(e) => e.key === 'Enter' && togglePause()}
	>
		{#if isPaused}
			<Play size={18} aria-hidden="true" />
		{:else}
			<Pause size={18} aria-hidden="true" />
		{/if}
	</button>

	<button
		type="button"
		class="carousel-control carousel-next"
		aria-label={$t('carousel.controls.nextItem')}
		onclick={next}
		onkeydown={(e) => e.key === 'Enter' && next()}
	>
		<ChevronRight size={18} aria-hidden="true" />
	</button>
</div>

<style lang="scss">
	.carousel-controls {
		display: flex;
		justify-content: center;
		gap: 0.5rem;
		margin-bottom: 0.5rem;
		position: absolute;
		bottom: 1rem;
		left: 0;
		right: 0;
		z-index: 5;
	}

	.carousel-control {
		background-color: rgba(255, 255, 255, 0.7);
		border: none;
		border-radius: 50%;
		width: 2.5rem;
		height: 2.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition:
			background-color 0.2s,
			transform 0.2s;
		color: #333;
		padding: 0;

		&:hover {
			background-color: rgba(255, 255, 255, 0.9);
			transform: scale(1.05);
		}

		&:focus {
			background-color: rgba(255, 255, 255, 0.9);
		}

		&:focus-visible {
			outline: 2px solid #4d90fe;
			outline-offset: 2px;
		}

		&:active {
			transform: scale(0.95);
		}
	}
</style>
