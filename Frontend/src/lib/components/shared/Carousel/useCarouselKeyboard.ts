export interface KeyboardHandlers {
	onNext: () => void;
	onPrevious: () => void;
	onTogglePause: () => void;
}

export function useCarouselKeyboard() {
	function handleKeyDown(event: KeyboardEvent, handlers: KeyboardHandlers): void {
		switch (event.key) {
			case 'ArrowLeft':
				event.preventDefault();
				handlers.onPrevious();
				break;
			case 'ArrowRight':
				event.preventDefault();
				handlers.onNext();
				break;
			case ' ':
			case 'Spacebar':
				event.preventDefault();
				handlers.onTogglePause();
				break;
			case 'Home':
				event.preventDefault();
				break;
			case 'End':
				event.preventDefault();
				break;
		}
	}

	return {
		handleKeyDown
	};
}
