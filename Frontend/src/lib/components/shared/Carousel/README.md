# Carousel Component

A fully accessible, performant carousel component for Svelte applications with support for keyboard navigation, screen readers, and reduced motion preferences.

## Features

- **Continuous Animation**: Smooth auto-scrolling animation for content display
- **Accessibility Compliant**: ARIA attributes and keyboard navigation support
- **Responsive Design**: Adapts to different screen sizes
- **Performance Optimised**: Uses animation frame management for smooth animations
- **Reduced Motion Support**: Respects user's motion preferences
- **Internationalization Ready**: All text content can be translated
- **Touch & Mouse Controls**: Interactive navigation controls

## Usage

```svelte
<script>
	import { Carousel, type CarouselItem } from '$lib/components/shared/Carousel';

	interface Item extends CarouselItem {
		content: string;
	}

	const items: Item[] = [
		{ id: '1', content: 'Item 1' },
		{ id: '2', content: 'Item 2' },
		{ id: '3', content: 'Item 3' }
	];
</script>

<Carousel {items} let:item speed={0.5} gap="1rem" pauseOnHover={true} autoStart={true}>
    {#snippet item(item: Item)}
	    <div class="my-carousel-item">
		    {item.content}
	    </div>
	{/snippet}
</Carousel>
```

## Props

| Prop                 | Type      | Default              | Description                                                                         |
| -------------------- | --------- | -------------------- | ----------------------------------------------------------------------------------- |
| `items`              | `T[]`     | Required             | Array of items to display in the carousel. Each item must have a unique identifier. |
| `speed`              | `number`  | `0.5`                | Animation speed in pixels per frame. Higher values make the carousel move faster.   |
| `gap`                | `string`  | `"1rem"`             | CSS value for spacing between carousel items.                                       |
| `containerClass`     | `string`  | `""`                 | Additional CSS class for the carousel container.                                    |
| `itemClass`          | `string`  | `""`                 | Additional CSS class for each carousel item.                                        |
| `pauseOnHover`       | `boolean` | `false`              | Whether to pause the carousel animation when hovered.                               |
| `minimumItemsToShow` | `number`  | `10`                 | Minimum number of items to display (will duplicate items if needed).                |
| `ariaLabel`          | `string`  | `"Content carousel"` | Accessibility label for the carousel.                                               |
| `showControls`       | `boolean` | `true`               | Whether to display navigation controls.                                             |
| `autoStart`          | `boolean` | `true`               | Whether to automatically start the carousel animation.                              |

## Keyboard Navigation

The carousel supports the following keyboard shortcuts:

- **Left Arrow**: Navigate to previous slide
- **Right Arrow**: Navigate to next slide
- **Space**: Toggle pause/play
- **Tab**: Focus on carousel controls

## Accessibility Features

- Proper ARIA roles and attributes
- Screen reader announcements for slide changes
- Reduced motion respect for users who prefer reduced motion
- Focus management for keyboard navigation
- Live region for dynamic content announcements

## Translations

The carousel uses the app's translation system for all text content. The following translation keys are used:

```json
{
	"controls": {
		"previousItem": "Previous item",
		"nextItem": "Next item",
		"playCarousel": "Play carousel",
		"pauseCarousel": "Pause carousel"
	},
	"announcements": {
		"carouselWith": "Carousel with {0} items",
		"carouselPaused": "Carousel paused",
		"carouselPlaying": "Carousel playing",
		"carouselStarted": "Carousel started",
		"carouselStopped": "Carousel stopped",
		"itemOfTotal": "Item {0} of {1}"
	}
}
```

## Implementation Details

The carousel is implemented using several modular utilities:

- **Animation**: Handles smooth scrolling with requestAnimationFrame
- **Accessibility**: Manages screen reader announcements and reduced motion
- **Keyboard**: Provides keyboard navigation support

## Best Practices

1. **Provide Meaningful Content**: Each carousel item should have descriptive content
2. **Keep Items Simple**: Avoid complex interactive elements within carousel items
3. **Use Appropriate Speed**: Adjust speed based on content complexity
4. **Consider Mobile Users**: Test touch interactions on mobile devices
5. **Test with Screen Readers**: Ensure accessibility works with various screen readers

## Example: Image Gallery

```svelte
<script>
	import { Carousel, type CarouselItem } from '$lib/components/shared/Carousel';

	interface ImageItem extends CarouselItem {
		src: string;
		alt: string;
	}

	const images: ImageItem[] = [
		{ id: '1', src: '/images/photo1.jpg', alt: 'Mountain landscape' },
		{ id: '2', src: '/images/photo2.jpg', alt: 'Ocean sunset' },
		{ id: '3', src: '/images/photo3.jpg', alt: 'Forest path' }
	];
</script>

<Carousel items={images} let:item speed={0.3} pauseOnHover={true} ariaLabel="Photo gallery">
	{#snippet item(item: ImageItem)}
        <div class="image-container">
            <img src={item.src} alt={item.alt} />
            <div class="caption">{item.alt}</div>
        </div>
    {/snippet}
</Carousel>
```
