<script lang="ts" generics="T extends CarouselItem">
	import { onDestroy, onMount, type Snippet } from 'svelte';

	import { browser } from '$app/environment';
	import { exists } from '$lib/appmaxx/util';
	import { T, t } from '$lib/translations/config';

	import type { CarouselItem } from './Carousel';
	import { duplicateItemsIfNeeded, getItemKey, safeClone } from './carousel-utils';
	import CarouselControls from './CarouselControls.svelte';
	import { useCarouselAccessibility } from './useCarouselAccessibility';
	import { useCarouselAnimation } from './useCarouselAnimation';
	import { useCarouselKeyboard } from './useCarouselKeyboard';

	let {
		items,
		speed = 0.5,
		gap = '1rem',
		containerClass = '',
		itemClass = '',
		pauseOnHover = false,
		minimumItemsToShow = 10,
		item,
		ariaLabel = 'Content carousel',
		showControls = true,
		autoStart = true
	}: {
		items: T[];
		speed?: number;
		gap?: string;
		containerClass?: string;
		itemClass?: string;
		pauseOnHover?: boolean;
		minimumItemsToShow?: number;
		item: Snippet<[item: T]>;
		ariaLabel?: string;
		showControls?: boolean;
		autoStart?: boolean;
	} = $props();

	let items$ = $state<T[]>([]);
	let carouselContainer: HTMLElement;
	let isPaused = $state(!autoStart);
	let isTransitioning = $state(false);
	let currentIndex = $state(0);
	let carouselId = `carousel-${Math.random().toString(36).substring(2, 9)}`;
	let originalItems = $state<T[]>([]);

	// Initialize utilities
	const accessibility = useCarouselAccessibility();
	const animation = useCarouselAnimation();
	const keyboard = useCarouselKeyboard();

	// Derived state for aria-live
	const ariaLive = $derived(isPaused ? 'polite' : 'off');
	// State for announcements
	let announcementMessage = $state('');

	$effect(() => {
		if (exists(items) && items.length > 0) {
			originalItems = [...items];
			items$ = duplicateItemsIfNeeded(items, minimumItemsToShow);
			animation.invalidateMeasurements();
			if (browser) {
				queueMicrotask(() => {
					if (exists(carouselContainer)) {
						animation.measureDimensions(carouselContainer);
					}
				});
			}
		}
	});

	function handleMouseEnter() {
		if (pauseOnHover) {
			isPaused = true;
			announcementMessage = $t('carousel.announcements.carouselPaused');
		}
	}

	function handleMouseLeave() {
		if (pauseOnHover && !isPaused) {
			isPaused = false;
			announcementMessage = $t('carousel.announcements.carouselPlaying');
		}
	}

	// Handle item transition
	function moveToNextItem() {
		if (items$.length <= 1) return;

		isTransitioning = true;
		const firstItem = safeClone(items$[0]);
		items$ = [...items$.slice(1), firstItem];
		currentIndex = (currentIndex + 1) % originalItems.length;

		// Find the original item index for accessibility announcements
		const originalIndex = originalItems.findIndex(
			(item) => 'id' in item && item.id === (firstItem as CarouselItem)?.id
		);
		if (originalIndex >= 0) {
			announcementMessage = $t(
				'carousel.announcements.itemOfTotal',
				(originalIndex + 1).toString(10),
				originalItems.length.toString(10)
			);
		}

		// Reset transition flag after a short delay
		setTimeout(() => {
			isTransitioning = false;
		}, 300);
	}

	// Handle manual movement to previous item
	function moveToPreviousItem() {
		if (items$.length <= 1) return;

		isTransitioning = true;
		// Take the last item and move it to the front
		const lastItem = safeClone(items$[items$.length - 1]);
		items$ = [lastItem, ...items$.slice(0, items$.length - 1)];
		currentIndex = (currentIndex - 1 + originalItems.length) % originalItems.length;

		// Find the original item index for accessibility announcements
		const originalIndex = originalItems.findIndex(
			(item) => 'id' in item && item.id === (lastItem as CarouselItem)?.id
		);
		if (originalIndex >= 0) {
			announcementMessage = $t(
				'carousel.announcements.itemOfTotal',
				(originalIndex + 1).toString(10),
				originalItems.length.toString(10)
			);
		}

		// Reset transition flag after a short delay
		setTimeout(() => {
			isTransitioning = false;
		}, 300);
	}

	function togglePause() {
		isPaused = !isPaused;
		if (isPaused) {
			animation.stopAnimation();
			announcementMessage = $t('carousel.announcements.carouselPaused');
		} else {
			startCarouselAnimation();
			announcementMessage = $t('carousel.announcements.carouselPlaying');
		}
	}

	function startCarouselAnimation() {
		if (!exists(carouselContainer) || !browser) return;

		animation.startAnimation(carouselContainer, {
			speed,
			shouldAnimate: () =>
				!isPaused && items$.length > 1 && !accessibility.shouldReduceMotion(),
			onTransitionComplete: moveToNextItem
		});

		announcementMessage = $t('carousel.announcements.carouselStarted');
	}

	function stopCarouselAnimation() {
		isPaused = true;
		animation.stopAnimation();
		announcementMessage = $t('carousel.announcements.carouselStopped');
	}

	function handleKeyDown(event: KeyboardEvent) {
		keyboard.handleKeyDown(event, {
			onNext: next,
			onPrevious: previous,
			onTogglePause: togglePause
		});
	}

	function next() {
		if (items$.length <= 1) return;

		isPaused = true;
		moveToNextItem();

		if (exists(carouselContainer)) {
			animation.resetPosition(carouselContainer);
		}

		announcementMessage = $t('carousel.announcements.carouselPaused');
	}

	function previous() {
		if (items$.length <= 1) return;

		isPaused = true;
		moveToPreviousItem();

		if (exists(carouselContainer)) {
			animation.resetPosition(carouselContainer);
		}

		announcementMessage = $t('carousel.announcements.carouselPaused');
	}

	// Reduced motion preferences
	let prefersReducedMotion = $state(false);

	onMount(() => {
		if (!browser) return;

		if (exists(carouselContainer)) {
			animation.resetPosition(carouselContainer);
		}

		// Check for reduced motion preference
		const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
		prefersReducedMotion = mediaQuery.matches;

		// Initialise accessibility with reduced motion preference
		accessibility.initialize(mediaQuery);

		// Resize observer to remeasure items when the container size changes
		const resizeObserver = new ResizeObserver(() => {
			animation.invalidateMeasurements();
			if (exists(carouselContainer)) {
				animation.measureDimensions(carouselContainer);
			}
		});

		if (exists(carouselContainer) && exists(carouselContainer.parentElement)) {
			resizeObserver.observe(carouselContainer.parentElement);
		}

		// Start animation if auto-start is enabled
		if (autoStart && !prefersReducedMotion) {
			startCarouselAnimation();
		}

		onDestroy(() => resizeObserver.disconnect());
	});

	onDestroy(() => {
		if (browser) animation.stopAnimation();
	});
</script>

<div
	role="button"
	aria-roledescription="carousel"
	aria-label={ariaLabel}
	class="carousel-outer {containerClass}"
	id={carouselId}
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
	onkeydown={handleKeyDown}
	tabindex="0"
	aria-busy={isTransitioning}
>
	<div aria-live={ariaLive} aria-atomic="true" class="sr-only">
		{#if announcementMessage}
			{announcementMessage}
		{:else}
			<T
				key="carousel.announcements.carouselWith"
				params={[originalItems.length.toString(10)]}
			/>
		{/if}
	</div>

	{#if showControls}
		<CarouselControls {carouselId} {isPaused} {previous} {next} {togglePause} />
	{/if}

	<div class="carousel-container" bind:this={carouselContainer} style="gap: {gap};">
		{#each items$ as i, index (getItemKey(i))}
			<div
				class="carousel-item {itemClass}"
				role="tabpanel"
				aria-roledescription="slide"
				aria-label={$t(
					'carousel.announcements.itemOfTotal',
					((index % originalItems.length) + 1).toString(10),
					originalItems.length.toString(10)
				)}
				aria-current={index === 0 ? 'true' : 'false'}
			>
				{@render item(i)}
			</div>
		{/each}
	</div>
</div>

<style lang="scss">
	.carousel-outer {
		width: 100%;
		overflow: hidden;
		position: relative;
	}

	.carousel-container {
		display: flex;
		flex-direction: row;
		will-change: transform;
		transform: translate3d(var(--translate-x, 0), 0, 0);
	}

	.carousel-item {
		flex-shrink: 0;
	}

	.carousel-outer:focus-visible {
		outline: 2px solid #4d90fe;
		outline-offset: 2px;
	}

	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border-width: 0;
	}
</style>
