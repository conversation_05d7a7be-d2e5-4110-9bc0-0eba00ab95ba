<script lang="ts">
	import { onDestroy, onMount, tick } from 'svelte';
	import { fly } from 'svelte/transition';

	import { page } from '$app/state';
	import {
		type PageTransitionKey,
		type PageTransitionProps,
		pageTransitionsStore
	} from '$lib/components/shared/PageTransition/PageTransition';

	let { children, key }: PageTransitionProps = $props();

	let initialized = $state(false);
	let transitionKey = $state(0);
	let lastUrl = $state(page.url.pathname);

	onMount(async () => {
		await tick();
		initialized = true;

		pageTransitionsStore.update((keys) => {
			keys.push(key);
			return keys;
		});
	});

	onDestroy(() => {
		pageTransitionsStore.update((keys) => {
			return keys.filter((k) => k !== key);
		});
	});

	// Some logic to determine when to transition
	$effect(() => {
		if (lastUrl === page.url.pathname) return;

		lastUrl = page.url.pathname;

		if (isHighestKey($pageTransitionsStore)) {
			transitionKey = Math.random();
		}
	});

	function isHighestKey(keys: PageTransitionKey[]): boolean {
		const anyHigherKey = keys.some((k) => k.startsWith(`${key}.`));
		return !anyHigherKey;
	}
</script>

{#key transitionKey}
	<div class="page-transition" class:initialized in:fly={{ y: -10, duration: 300, opacity: 0.3 }}>
		{@render children?.()}
	</div>
{/key}

<noscript>
	<style>
		.page-transition {
			opacity: 1 !important;
		}
	</style>
</noscript>

<style lang="scss">
	.page-transition {
		width: 100%;
		height: 100%;
		flex-grow: 1;
		display: flex;
		flex-direction: column;
		transition:
			opacity 0.3s ease,
			translate 0.3s ease;
		opacity: 0;
		translate: 0 -10px;

		&.initialized {
			opacity: 1;
			translate: none;
		}
	}
</style>
