<script lang="ts">
	let { children } = $props();
</script>

<div class="markdown-styles">
	{@render children?.()}
</div>

<style lang="scss">
	.markdown-styles {
		display: flex;
		flex-direction: column;
		gap: var(--spacing-xxl);
		line-height: 1.5;

		:global {
			h1 {
				font-size: var(--font-size-1);
			}

			h2 {
				font-size: var(--font-size-2);
			}

			h3 {
				font-size: var(--font-size-3);
			}

			h4 {
				font-size: var(--font-size-4);
			}

			h5 {
				font-size: var(--font-size-text);
			}

			h6 {
				font-size: var(--font-size-sub);
			}

			p {
				font-size: var(--font-size-text);
			}

			a {
				color: var(--color-primary);
				text-decoration: none;

				&:hover {
					text-decoration: underline;
				}
			}

			hr {
				border: none;
				border-top: 1px solid var(--color-border-light);
				margin: var(--spacing-l) 0;
			}

			ol {
				display: flex;
				flex-direction: column;
				gap: var(--spacing-m);
				list-style: none;
				counter-reset: custom-counter;
				padding-left: var(--spacing-xl);

				@media (max-width: 639px) {
					padding-left: var(--spacing-m);
				}

				li {
					counter-increment: custom-counter;
					position: relative;
					padding-left: 2rem;

					&::before {
						content: counter(custom-counter) '.';
						position: absolute;
						left: 0;
						font-weight: bold;
						color: var(--color-primary);
						padding-right: 0.5rem;
					}
				}
			}

			ul {
				display: flex;
				flex-direction: column;
				gap: var(--spacing-xl);
				list-style: none;
				padding-left: var(--spacing-xl);

				@media (max-width: 639px) {
					padding-left: var(--spacing-m);
				}

				li {
					display: flex;
					flex-direction: column;
					gap: var(--spacing-m);
				}
			}

			.spacing-xs {
				margin-bottom: var(--spacing-xs);
			}

			.spacing-s {
				margin-bottom: var(--spacing-s);
			}

			.spacing-sm {
				margin-bottom: var(--spacing-sm);
			}

			.spacing-m {
				margin-bottom: var(--spacing-m);
			}

			.spacing-ml {
				margin-bottom: var(--spacing-ml);
			}

			.spacing-l {
				margin-bottom: var(--spacing-l);
			}

			.spacing-xl {
				margin-bottom: var(--spacing-xl);
			}

			.spacing-xxl {
				margin-bottom: var(--spacing-xxl);
			}
		}
	}
</style>
