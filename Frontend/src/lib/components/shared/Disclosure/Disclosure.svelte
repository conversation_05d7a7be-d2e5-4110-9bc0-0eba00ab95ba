<script lang="ts">
	import { ChevronDown } from '@lucide/svelte';
	import { slide } from 'svelte/transition';

	import { exists } from '$lib/appmaxx/util';
	import { Spacer } from '$lib/components';
	import type { DisclosureProps } from '$lib/components/shared/Disclosure/Disclosure';
	import Card from '$lib/components/shared/primitives/Card/Card.svelte';

	let {
		title,
		children,
		initiallyOpen = false,
		id = `disclosure-${Math.random().toString(36).substring(2, 9)}`,
		transitionParams = { duration: 250 },
		class: className = '',
		...props
	}: DisclosureProps = $props();

	let isOpen = $state(initiallyOpen);
	const contentId = `${id}-content`;
	const headerId = `${id}-header`;

	function toggleDisclosure() {
		isOpen = !isOpen;
	}

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleDisclosure();
		}
	}
</script>

<div class="disclosure {className}" {...props}>
	<Card
		variant="section"
		borderRadius="l"
		paddingX="ml"
		paddingY="m"
		shadow="true"
		class="disclosure-card"
	>
		<button
			type="button"
			class="disclosure-header"
			aria-expanded={isOpen}
			aria-controls={contentId}
			id={headerId}
			onclick={toggleDisclosure}
			onkeydown={handleKeyDown}
		>
			<span class="disclosure-icon" class:opened={isOpen} aria-hidden="true">
				<ChevronDown size={26} strokeWidth={2} color="var(--color-primary)" />
			</span>

			<Spacer direction="horizontal" size="m" />

			<span class="disclosure-title">
				{#if exists(title)}
					{@render title()}
				{/if}
			</span>
		</button>

		{#if isOpen}
			<div
				id={contentId}
				role="region"
				aria-labelledby={headerId}
				class="disclosure-content"
				transition:slide={transitionParams}
			>
				<Spacer direction="vertical" size="m" />

				{#if exists(children)}
					{@render children()}
				{/if}
			</div>
		{/if}
	</Card>
</div>

<style lang="scss">
	@use '$lib/assets/styles/hoverable' as hoverable;

	.disclosure {
		width: 100%;

		&-header {
			display: flex;
			align-items: center;
			width: 100%;
			padding: var(--spacing-s) var(--spacing-m);
			border: none;
			background: none;
			cursor: pointer;
			text-align: left;
			border-radius: var(--border-radius-m);
			@include hoverable.hoverable();

			.disclosure-icon {
				display: flex;
				align-items: center;
				justify-content: center;
				transition: transform 0.2s ease;

				&.opened {
					transform: rotate(-180deg);
				}
			}
		}
	}
</style>
