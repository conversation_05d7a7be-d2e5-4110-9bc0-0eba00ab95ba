export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
export type FetchLike = (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>;
export type Message = { messageKey: string; params?: Record<string, unknown> };

export type BackendError = {
	ok: false;
	message: string;
	status: number;
	errors: {
		global?: Message[];
		fields?: { field: string; issues: Message[] }[];
	};
};

export type BackendSuccess<TData = undefined> = {
	ok: true;
	status: number;
	data: TData;
	messages?: Message[];
};

export type ApiPayload<TData> = BackendError | BackendSuccess<TData>;

type Phase = 'request' | 'response' | 'error';

type EventBase = {
	routeKey: string;
	url: string;
	method: HttpMethod;
	startedAt: number;
};
export type RequestEvent<P> = EventBase & { payload: P; init: RequestInit };
export type ResponseEvent<P, R> = EventBase & { payload: P; response: Response; data?: R };
export type ErrorEvent<P> = EventBase & { payload: P; error: unknown };

type Handler<T> = (evt: T) => void;

export interface ApiRoute<
	P = unknown,
	R extends Record<string, unknown> = Record<string, unknown>
> {
	(payload: P, init?: RequestInit): Promise<ApiPayload<R>>;
	raw(payload?: BodyInit, init?: RequestInit): Promise<Response>;
	on<T extends Phase>(
		phase: T,
		cb: T extends 'request'
			? Handler<RequestEvent<P>>
			: T extends 'response'
				? Handler<ResponseEvent<P, ApiPayload<R>>>
				: Handler<ErrorEvent<P>>
	): () => void;
}

export type RouteBuilder = <
	P = unknown,
	R extends Record<string, unknown> = Record<string, unknown>
>(
	method: HttpMethod,
	path: string
) => ApiRoute<P, R>;

export class ApiService {
	private baseUrl = '';
	private fetchImpl: FetchLike | undefined;
	private headers: HeadersInit | undefined;
	private listeners = new Map<string, Set<Handler<unknown>>>();

	constructor(opts: { baseUrl?: string; fetch?: FetchLike; headers?: HeadersInit } = {}) {
		if (opts.baseUrl) this.baseUrl = opts.baseUrl;
		this.fetchImpl = opts.fetch;
		this.headers = opts.headers;
	}

	setBaseUrl(url: string) {
		this.baseUrl = url;
	}
	setFetch(fetchFn: FetchLike | undefined) {
		this.fetchImpl = fetchFn;
	}
	setHeaders(headers: HeadersInit | undefined) {
		this.headers = headers;
	}

	on<T extends Phase>(
		phase: T,
		cb: T extends 'request'
			? Handler<RequestEvent<unknown>>
			: T extends 'response'
				? Handler<ResponseEvent<unknown, unknown>>
				: Handler<ErrorEvent<unknown>>
	): () => void {
		if (!this.listeners.has(phase)) this.listeners.set(phase, new Set());
		const set = this.listeners.get(phase)!;
		set.add(cb as Handler<unknown>);
		return () => set.delete(cb as Handler<unknown>);
	}

	onRoute(phase: Phase, routeKey: string, cb: Handler<unknown>): () => void {
		const k = `${phase}:${routeKey}`;
		if (!this.listeners.has(k)) this.listeners.set(k, new Set());
		const set = this.listeners.get(k)!;
		set.add(cb);
		return () => set.delete(cb);
	}

	define<T>(namespace: string, buildNamespace: (route: RouteBuilder) => T): T {
		const routeBuilder: RouteBuilder = (method, path) => {
			const routeKey = `${namespace}.${pathToKey(path)}`;
			return this.makeRoute(method, path, routeKey);
		};
		return buildNamespace(routeBuilder);
	}

	route<P = unknown, R extends Record<string, unknown> = Record<string, unknown>>(
		routeKey: string,
		method: HttpMethod,
		path: string
	): ApiRoute<P, R> {
		return this.makeRoute(method, path, routeKey);
	}

	private makeRoute<P, R extends Record<string, unknown>>(
		method: HttpMethod,
		path: string,
		routeKey: string
	): ApiRoute<P, R> {
		const call = async (payload: P, init?: RequestInit): Promise<ApiPayload<R>> => {
			const startedAt = Date.now();
			let url = this.urlFor(path, payload);

			if (
				(method === 'GET' || method === 'DELETE') &&
				payload &&
				typeof payload === 'object'
			) {
				const used = new Set(extractPathKeys(path));
				const rest: Record<string, unknown> = {};
				for (const [k, v] of Object.entries(payload as Record<string, unknown>)) {
					if (!used.has(k)) rest[k] = v;
				}
				url = attachQuery(url, rest);
			}
			const f = this.fetchImpl ?? globalThis.fetch;

			const headers: HeadersInit = {
				'content-type': 'application/json',
				...(this.headers || {}),
				...(init?.headers || {})
			};

			const reqInit: RequestInit = {
				method,
				headers,
				body: method === 'GET' || method === 'DELETE' ? undefined : JSON.stringify(payload),
				...init
			};

			this.emit('request', routeKey, {
				routeKey,
				url,
				method,
				startedAt,
				payload,
				init: reqInit
			} as RequestEvent<P>);

			try {
				const res = await f(url, reqInit);
				const ctype = res.headers.get('content-type') || '';
				let data: unknown = undefined;
				if (ctype.includes('application/json')) data = await res.json();

				const apiPayload = data as ApiPayload<R>;

				this.emit('response', routeKey, {
					routeKey,
					url,
					method,
					startedAt,
					payload,
					response: res,
					data: apiPayload
				} as ResponseEvent<P, ApiPayload<R>>);

				return apiPayload;
			} catch (error) {
				this.emit('error', routeKey, {
					routeKey,
					url,
					method,
					startedAt,
					payload,
					error
				} as ErrorEvent<P>);
				throw error;
			}
		};

		const raw = async (payload?: BodyInit, init?: RequestInit): Promise<Response> => {
			const startedAt = Date.now();
			const url = this.urlFor(path, payload as unknown);
			const f = this.fetchImpl ?? globalThis.fetch;

			const reqInit: RequestInit = {
				method,
				headers: { ...(this.headers || {}), ...(init?.headers || {}) },
				body: method === 'GET' || method === 'DELETE' ? undefined : payload,
				...init
			};

			this.emit('request', routeKey, {
				routeKey,
				url,
				method,
				startedAt,
				payload,
				init: reqInit
			} as RequestEvent<unknown>);

			try {
				const res = await f(url, reqInit);

				this.emit('response', routeKey, {
					routeKey,
					url,
					method,
					startedAt,
					payload,
					response: res
				} as ResponseEvent<unknown, Response>);

				// žádný throw na !res.ok – caller si rozhodne
				return res;
			} catch (error) {
				this.emit('error', routeKey, {
					routeKey,
					url,
					method,
					startedAt,
					payload,
					error
				} as ErrorEvent<unknown>);
				throw error;
			}
		};

		const on = (<T extends Phase>(
			phase: T,
			cb: T extends 'request'
				? Handler<RequestEvent<P>>
				: T extends 'response'
					? Handler<ResponseEvent<P, ApiPayload<R>>>
					: Handler<ErrorEvent<P>>
		) => this.onRoute(phase, routeKey, cb as Handler<unknown>)) as ApiRoute<P, R>['on'];

		const fn = call as ApiRoute<P, R>;
		fn.raw = raw;
		fn.on = on;
		return fn;
	}

	private urlFor(path: string, payload: unknown): string {
		if (payload && typeof payload === 'object') {
			let out = path;
			for (const [k, v] of Object.entries(payload as Record<string, unknown>)) {
				out = out.replace(
					new RegExp(`:${k}(\\b|\\/|$)`, 'g'),
					encodeURIComponent(String(v)) + '$1'
				);
			}
			return this.baseUrl + out;
		}
		return this.baseUrl + path;
	}

	private emit(phase: Phase, routeKey: string, evt: unknown) {
		this.listeners.get(phase)?.forEach((cb) => (cb as Handler<unknown>)(evt));
		this.listeners.get(`${phase}:${routeKey}`)?.forEach((cb) => (cb as Handler<unknown>)(evt));
	}
}

function pathToKey(path: string) {
	return (
		path.replace(/^/, '').replace(/[:{}]/g, '').replace(/\W+/g, '.').replace(/\.+/g, '.') ||
		'root'
	);
}

function extractPathKeys(path: string): string[] {
	const out: string[] = [];
	const re = /:([A-Za-z0-9_]+)/g;
	let m;
	while ((m = re.exec(path))) out.push(m[1]);
	return out;
}

function attachQuery(url: string, obj: Record<string, unknown>): string {
	const usp = new URLSearchParams();
	for (const [k, v] of Object.entries(obj)) {
		if (v === undefined || v === null) continue;
		usp.append(k, String(v));
	}
	const qs = usp.toString();
	if (!qs) return url;
	return url + (url.includes('?') ? '&' : '?') + qs;
}

export const api = new ApiService();
