import { api, type ApiPayload, type RouteBuilder } from '$lib/api/apiServiceWrapper';
import type { <PERSON><PERSON>, Manager, Student, Teacher } from '$lib/services';

export type PagingParamsQuery = { page?: number; limit?: number };

export const users = api.define('users', (r: RouteBuilder) => ({
	listStudents: r<PagingParamsQuery, ApiPayload<Student[]>>('GET', '/students'),
	listTeachers: r<PagingParamsQuery, ApiPayload<Teacher[]>>('GET', '/teachers'),
	listManagers: r<PagingParamsQuery, ApiPayload<Manager[]>>('GET', '/managers'),
	listAdmins: r<PagingParamsQuery, ApiPayload<Admin[]>>('GET', '/admins')
}));
