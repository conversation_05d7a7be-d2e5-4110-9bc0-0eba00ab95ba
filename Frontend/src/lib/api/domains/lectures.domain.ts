import { api, type ApiPayload, type RouteBuilder } from '$lib/api/apiServiceWrapper';
import type { Lecture, Reservation } from '$lib/services';

export type CreateLectureInput = {
	type: string;
	name: string;
	lang: string;
	price: number;
	dateStart: string;
	dateEnd: string;
	notes?: string;
	capacity?: number;
	locationId?: string;
	repeatDays?: Array<
		'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday'
	>;
	repeatUntil?: string;
};

export type PatchLectureInput = {
	scope?: string;
	name?: string;
	timeStart?: string;
	timeEnd?: string;
	notes?: string;
};

export type CancelLectureInput = {
	scope?: string;
};

export type ListLecturesQuery = {
	skip?: number;
	take?: number;
	sort?: Record<string, 'asc' | 'desc'>;
	filters?: Record<string, unknown>;
};

export type ListReservationsQuery = {
	filters?: Record<string, unknown>;
	studentId?: string;
	lectureId?: string;
};

export const lectures = api.define('lectures', (r: RouteBuilder) => ({
	// Lecture management
	createLecture: r<CreateLectureInput, ApiPayload<Lecture>>('POST', '/lectures'),
	patchLecture: r<PatchLectureInput, ApiPayload<Lecture>>('PATCH', '/lectures/{lectureId}'),
	cancelLecture: r<CancelLectureInput, ApiPayload<Lecture>>(
		'PATCH',
		'/lectures/{lectureId}/cancel'
	),
	getLecture: r<undefined, ApiPayload<Lecture>>('GET', '/lectures/{lectureId}'),
	listLectures: r<ListLecturesQuery, ApiPayload<Lecture[]>>('GET', '/lectures'),

	// Reservation management
	createReservation: r<undefined, ApiPayload<undefined>>(
		'POST',
		'/lectures/{lectureId}/reservation'
	),
	cancelReservation: r<undefined, ApiPayload<undefined>>(
		'PATCH',
		'/lectures/{lectureId}/reservation/cancel'
	),
	listReservations: r<ListReservationsQuery, ApiPayload<Reservation[]>>(
		'GET',
		'/lectures/reservation'
	),
	getReservation: r<undefined, ApiPayload<Reservation>>(
		'GET',
		'/lectures/reservation/{reservationId}'
	)
}));
