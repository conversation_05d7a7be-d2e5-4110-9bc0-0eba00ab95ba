import { api, type ApiPayload, type RouteBuilder } from '$lib/api/apiServiceWrapper';
import type { Location } from '$lib/services';

export type CreateLocationInput = {
	name: string;
	address: string;
	description: string;
	capacity?: number;
	purpose: 'prijimacky_nanecisto' | 'prezencni_doucovani';
	managerIds?: string[];
	teacherIds?: string[];
};

export type PatchLocationInput = {
	name?: string;
	address?: string;
	description?: string;
	capacity?: number;
	purpose?: 'prijimacky_nanecisto' | 'prezencni_doucovani';
	managerIds?: string[];
	teacherIds?: string[];
};

export type ListLocationsQuery = {
	skip?: number;
	take?: number;
	sort?: Record<string, 'asc' | 'desc'>;
	filters?: Record<string, unknown>;
};

export const locations = api.define('locations', (r: RouteBuilder) => ({
	// Location management
	listLocations: r<ListLocationsQuery, ApiPayload<Location[]>>('GET', '/locations'),
	getLocation: r<undefined, ApiPayload<Location>>('GET', '/locations/{locationId}'),
	createLocation: r<CreateLocationInput, ApiPayload<Location>>('POST', '/locations'),
	patchLocation: r<PatchLocationInput, ApiPayload<Location>>('PATCH', '/locations/{locationId}'),
	deleteLocation: r<undefined, ApiPayload<undefined>>('DELETE', '/locations/{locationId}'),

	// Image management
	setLocationImage: r<FormData, ApiPayload<Location>>('POST', '/locations/{locationId}/image'),
	getLocationImage: r<undefined, ApiPayload<Blob>>('GET', '/locations/{locationId}/image'),
	deleteLocationImage: r<undefined, ApiPayload<Location>>(
		'DELETE',
		'/locations/{locationId}/image'
	)
}));
