import { api, type ApiPayload, type RouteBuilder } from '$lib/api/apiServiceWrapper';
import type {
	ForgotPasswordInput,
	LoginInput,
	RegisterInput,
	ResetPasswordValues
} from '$lib/schemas';

type MeResponse = { sub: string };

export const auth = api.define('auth', (r: RouteBuilder) => ({
	register: r<RegisterInput, ApiPayload<undefined>>('POST', '/auth/register'),
	login: r<LoginInput, ApiPayload<undefined>>('POST', '/auth/login'),
	forgotPassword: r<ForgotPasswordInput, ApiPayload<undefined>>('POST', '/auth/forgot-password'),
	reset: r<ResetPasswordValues, ApiPayload<undefined>>('POST', '/auth/reset-password'),
	me: r<undefined, ApiPayload<MeResponse>>('GET', '/auth/me')
}));
