import { api, type ApiPayload, type RouteBuilder } from '$lib/api/apiServiceWrapper';
import type { NewsletterSubscribeInput } from '$lib/schemas';

export const newsletter = api.define('newsletter', (r: RouteBuilder) => ({
	subscribe: r<NewsletterSubscribeInput, ApiPayload<undefined>>('POST', '/newsletter/subscribe'),
	unsubscribe: r<{ i: string; m: string }, ApiPayload<undefined>>(
		'GET',
		'/newsletter/unsubscribe'
	)
}));
