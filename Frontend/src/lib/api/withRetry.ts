type F = (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>;

export function withRefreshRetry(baseFetch: F): F {
	return async (input, init) => {
		const url = typeof input === 'string' ? input : input.toString();
		if (url.includes('/auth/refresh')) return baseFetch(input, init);

		const res = await baseFetch(input, init);
		if (res.status !== 401) return res;

		try {
			const r = await baseFetch('/api/auth/refresh', {
				method: 'POST',
				credentials: 'include'
				// + CSRF header ?
			});
			if (!r.ok) throw new Error('refresh failed');
		} catch {
			return res;
		}
		return baseFetch(input, init); // retry
	};
}
