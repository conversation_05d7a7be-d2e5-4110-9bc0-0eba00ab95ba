import type { SuperValidated, ValidationErrors } from 'sveltekit-superforms';

import type { ApiPayload, BackendSuccess, Message } from '$lib/api/apiServiceWrapper';

export type I18n = (key: string, params?: Record<string, unknown>) => string;

type FieldKey<T> = Extract<keyof T, string>;

const uniq = <T>(arr: T[]) => Array.from(new Set(arr));

export function toValidationErrors<T extends Record<string, unknown>>(
	payload: ApiPayload<T>
): ValidationErrors<T> {
	const out = {} as ValidationErrors<T>;
	if (!payload.ok) {
		for (const fg of payload.errors.fields ?? []) {
			const key = fg.field as FieldKey<T>;
			const msgs = fg.issues.map((i) => i.messageKey);
			if (msgs.length) {
				(out as Record<string, string[]>)[key] = uniq(msgs);
			}
		}
	}
	return out;
}

export function applyBackendToForm<TData extends Record<string, unknown>, TMsg = string>(
	form: SuperValidated<TData, TMsg, TData>,
	payload: ApiPayload<TData>,
	t?: I18n
): payload is BackendSuccess<TData> {
	if (payload.ok) {
		form.valid = true as const;
		form.data = payload.data;

		const m: Message | undefined = payload.messages?.[0];
		form.message = (m
			? (t?.(m.messageKey, m.params) ?? m.messageKey)
			: undefined) as unknown as TMsg;

		form.errors = {} as ValidationErrors<TData>;
		return true;
	}

	// error
	form.valid = false;
	form.errors = toValidationErrors(payload);

	const g: Message | undefined = payload.errors.global?.[0];
	form.message = (g
		? (t?.(g.messageKey, g.params) ?? g.messageKey)
		: undefined) as unknown as TMsg;

	return false;
}
