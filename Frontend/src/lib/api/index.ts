import { api } from '$lib/api/apiServiceWrapper';
import { auth } from '$lib/api/domains/auth.domain';
import { becomeTeacher } from '$lib/api/domains/becomeTeacher.domain';
import { lectures } from '$lib/api/domains/lectures.domain';
import { locations } from '$lib/api/domains/locations.domain';
import { newsletter } from '$lib/api/domains/newsletter.domain';
import { users } from '$lib/api/domains/users.domain';
import { withRefreshRetry } from '$lib/api/withRetry';

export * from './adapters/superformAdapter';
export * from './withRetry';

api.setBaseUrl('/api');
api.setFetch(withRefreshRetry(fetch));

export const apiService = {
	core: api,
	domains: { auth, newsletter, becomeTeacher, users, lectures, locations }
};
