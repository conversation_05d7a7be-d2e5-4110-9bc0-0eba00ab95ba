import type { RawRuleOf } from '@casl/ability';
import { derived, type Readable, writable } from 'svelte/store';

import { exists } from '$lib/appmaxx/util';
import {
	type Action,
	type AppAbility,
	buildAbilitiesFromRules,
	type Subject,
	type UserRole
} from '$lib/permissions';

export interface AppUser {
	sub: string;
	sid: string;
	permissions: string[];
	permsVer: number;
	firstname: string;
	lastname: string;
	email: string;
	role: UserRole;
	rules: RawRuleOf<AppAbility>[];
}

export const user = writable<AppUser | null>(null);
export const isAuthed = derived(user, (u) => !!u);

export const abilities = derived(user, (u) => {
	if (!u) return null;
	return buildAbilitiesFromRules(u.rules);
});

export const userName = derived(user, (u) => {
	if (!u) return 'N/A';
	return `${u.firstname} ${u.lastname}`;
});

export const canUser = (subject: Subject, action: Action): Readable<boolean> => {
	return derived([abilities], ([$abilities]) => (): boolean => {
		if (!exists($abilities)) return false;
		return $abilities.can(action, subject);
	});
};
