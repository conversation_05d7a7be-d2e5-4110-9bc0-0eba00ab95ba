import dotenv from 'dotenv';
import { z } from 'zod/v4';

dotenv.config();

const envSchema = z.object({
	JWKS_PUBLIC_URL: z.url() // Validace URL
});

type EnvVariables = z.infer<typeof envSchema>;

export function getEnvVariables(): EnvVariables {
	const parsedEnv = envSchema.safeParse(process.env);

	if (!parsedEnv.success) {
		console.error('Invalid environment variables:', parsedEnv.error);
		process.exit(1);
	}

	return parsedEnv.data;
}

//export const env = getEnvVariables();
