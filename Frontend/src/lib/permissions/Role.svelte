<script lang="ts">
	import type { Childable } from '$lib/components/cva-constants';
	import type { UserRole } from '$lib/permissions/types';
	import { hasRole } from '$lib/permissions/util';
	import { user } from '$lib/stores/user';

	interface Props extends Childable {
		role: UserRole | UserRole[];
	}

	let { role, children }: Props = $props();

	const canAccess = $derived(hasRole($user, role));
</script>

{#if canAccess}
	{@render children?.()}
{/if}
