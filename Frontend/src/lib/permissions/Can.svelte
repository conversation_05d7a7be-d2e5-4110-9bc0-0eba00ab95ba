<script lang="ts">
	import type { Childable } from '$lib/components/cva-constants';
	import { type Action, can, type Subject } from '$lib/permissions';
	import { abilities } from '$lib/stores/user';

	interface Props extends Childable {
		action: Action | Action[];
		subject: Subject | Subject[];
		field?: string;
	}

	let { action, subject, field, children }: Props = $props();

	const canAccess = $derived(can($abilities, subject, action));
</script>

{#if canAccess}
	{@render children?.()}
{/if}
