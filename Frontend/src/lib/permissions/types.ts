import type { MongoAbility } from '@casl/ability';

export type UserRole = 'student' | 'teacher' | 'manager' | 'admin';

export type AppTuple = [Action, Subject];
export type AppAbility = MongoAbility<AppTuple>;

export type RolePermissions = Partial<Record<Subject, Partial<Record<Action, boolean>>>>;
export type Ruleset = Record<UserRole, RolePermissions>;

// Mirrored from the backend

export enum ActionEnum {
	Manage = 'manage', // CRUD
	Create = 'create', // C
	Read = 'read', // R
	Update = 'update', // U
	Delete = 'delete' // D
}

export type Action = `${ActionEnum}`;

export enum SubjectEnum {
	All = 'all',
	User = 'User', // All user entities -> student, teacher, manager, admin
	Location = 'Location', // Location entity
	Lecture = 'Lecture', // Lecture entity
	Student = 'Student', // Student user entity
	Teacher = 'Teacher', // Teacher user entity
	Manager = 'Manager', // Manager user entity
	Admin = 'Admin' // Admin user entity
}

export type Subject = `${SubjectEnum}`;
