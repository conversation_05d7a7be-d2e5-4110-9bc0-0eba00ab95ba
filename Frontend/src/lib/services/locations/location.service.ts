import type { CreateQueryResult } from '@tanstack/svelte-query';

import { apiService } from '$lib/api';
import type { ListLecturesQuery } from '$lib/api/domains/lectures.domain';
import { get } from '$lib/services/listing';
import type { Location } from '$lib/services/locations/types';

class LocationService {
	listLocations(): CreateQueryResult<Location[], Error> {
		return get<Location[], ListLecturesQuery>(
			'lectures',
			'list',
			apiService.domains.locations.listLocations
		);
	}
}

export const locationService = new LocationService();
