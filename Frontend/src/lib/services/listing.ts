import { createQuery, type CreateQueryResult } from '@tanstack/svelte-query';

import type { ApiPayload, ApiRoute } from '$lib/api/apiServiceWrapper';

export function get<T, QUERY>(
	namespace: string,
	id: string,
	queryFn: ApiRoute<QUERY, ApiPayload<T>>,
	query: QUERY = {} as QUERY
): CreateQueryResult<T, Error> {
	return createQuery({
		queryKey: [namespace, id],
		queryFn: async () => {
			const result = await queryFn(query);
			if (!result.ok) {
				throw new Error(result.message);
			}
			return result.data;
		},
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000 // 10 minutes
	});
}
