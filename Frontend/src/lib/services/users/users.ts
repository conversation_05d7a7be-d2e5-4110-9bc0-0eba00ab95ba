import type { UserRole } from '$lib/permissions';

export interface User {
	firstName: string;
	lastName: string;
	email: string;
	emailVerifiedAt: Date | null;
	role: UserRole;
	createdAt: string;
	lang: string;
}

interface UserExtension extends User {
	id: string;
	userId: string;
	archivedAt: Date | null;
}

export interface Student extends UserExtension {
	emailStudent: string;
	billingFirstName: string | null;
	billingLastName: string | null;
	billingAddress: string | null;
	billingCity: string | null;
	billingZip: string | null;
	billingPhone: string | null;
	billingEmail: string | null;
}

export interface Teacher extends UserExtension {
	phone: string | null;
	description: string | null;
	wageTax: number | null;
}

export type Manager = UserExtension;

export type Admin = UserExtension;
