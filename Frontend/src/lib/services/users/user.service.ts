import { type CreateQ<PERSON>yResult } from '@tanstack/svelte-query';

import { apiService } from '$lib/api';
import type { PagingParamsQuery } from '$lib/api/domains/users.domain';
import type { <PERSON><PERSON>, Manager, Student, Teacher } from '$lib/services';
import { get } from '$lib/services/listing';

class UserService {
	listStudents(): CreateQueryResult<Student[], Error> {
		return get<Student[], PagingParamsQuery>(
			'users',
			'listStudents',
			apiService.domains.users.listStudents
		);
	}

	listTeachers(): CreateQueryResult<Teacher[], Error> {
		return get<Teacher[], PagingParamsQuery>(
			'users',
			'listTeachers',
			apiService.domains.users.listTeachers
		);
	}

	listManagers(): CreateQueryResult<Manager[], Error> {
		return get<Manager[], PagingParamsQuery>(
			'users',
			'listManagers',
			apiService.domains.users.listManagers
		);
	}

	listAdmins(): CreateQueryResult<Admin[], Error> {
		return get<Admin[], PagingParamsQuery>(
			'users',
			'listAdmins',
			apiService.domains.users.listAdmins
		);
	}
}

export const userService = new UserService();
