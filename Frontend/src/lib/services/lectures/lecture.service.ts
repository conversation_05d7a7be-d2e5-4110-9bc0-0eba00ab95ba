import type { <PERSON>reate<PERSON><PERSON>y<PERSON><PERSON>ult } from '@tanstack/svelte-query';

import { apiService } from '$lib/api';
import type { ListLecturesQuery } from '$lib/api/domains/lectures.domain';
import type { Lecture } from '$lib/services/lectures/types';
import { get } from '$lib/services/listing';

class LectureService {
	listLectures(): CreateQueryResult<Lecture[], Error> {
		return get<Lecture[], ListLecturesQuery>(
			'lectures',
			'list',
			apiService.domains.lectures.listLectures
		);
	}
}

export const lectureService = new LectureService();
