import type { TranslationsConfig } from '$lib/appmaxx/translations';
import { Translations } from '$lib/appmaxx/translations';

const config: TranslationsConfig = {
	defaultLocale: 'cs',
	loaders: [
		// Globals
		{
			locale: 'cs',
			key: 'header',
			loader: async () => (await import('./cs/presentation/header.json')).default
		},
		{
			locale: 'cs',
			key: 'footer',
			loader: async () => (await import('./cs/global/footer.json')).default
		},
		{
			locale: 'cs',
			key: 'httpErrors',
			loader: async () => (await import('./cs/global/http-errors.json')).default
		},
		{
			locale: 'cs',
			key: 'meta',
			loader: async () => (await import('./cs/global/meta.json')).default
		},
		{
			locale: 'cs',
			key: 'carousel',
			loader: async () => (await import('./cs/presentation/carousel.json')).default
		},
		{
			locale: 'cs',
			key: 'locationsMap',
			loader: async () => (await import('./cs/presentation/locations-map.json')).default
		},
		{
			locale: 'cs',
			key: 'roles',
			loader: async () => (await import('./cs/global/roles.json')).default
		},
		// Presentation
		{
			locale: 'cs',
			key: 'home',
			loader: async () => (await import('./cs/presentation/home.json')).default
		},
		{
			locale: 'cs',
			key: 'register',
			loader: async () => (await import('./cs/presentation/auth/register.json')).default
		},
		{
			locale: 'cs',
			key: 'login',
			loader: async () => (await import('./cs/presentation/auth/login.json')).default
		},
		{
			locale: 'cs',
			key: 'forgotPassword',
			loader: async () =>
				(await import('./cs/presentation/auth/forgot-password.json')).default
		},
		{
			locale: 'cs',
			key: 'reset',
			loader: async () => (await import('./cs/presentation/auth/reset.json')).default
		},
		{
			locale: 'cs',
			key: 'verify',
			loader: async () => (await import('./cs/presentation/auth/verify.json')).default
		},
		{
			locale: 'cs',
			key: 'unsubscribe',
			loader: async () => (await import('./cs/presentation/auth/unsubscribe.json')).default
		},
		{
			locale: 'cs',
			key: 'global',
			loader: async () => (await import('./cs/global/errors.json')).default
		},
		{
			locale: 'cs',
			key: 'aboutUs',
			loader: async () => (await import('./cs/presentation/about-us.json')).default
		},
		{
			locale: 'cs',
			key: 'pricing',
			loader: async () => (await import('./cs/presentation/pricing.json')).default
		},
		{
			locale: 'cs',
			key: 'contact',
			loader: async () => (await import('./cs/presentation/contact.json')).default
		},
		{
			locale: 'cs',
			key: 'becomeTeacher',
			loader: async () => (await import('./cs/presentation/become-teacher.json')).default
		},
		{
			locale: 'cs',
			key: 'tos',
			loader: async () => (await import('./cs/presentation/tos.json')).default
		},
		{
			locale: 'cs',
			key: 'tosMd',
			loader: async () => (await import('./cs/presentation/tos.md?raw')).default
		},
		{
			locale: 'cs',
			key: 'privacy',
			loader: async () => (await import('./cs/presentation/privacy.json')).default
		},
		{
			locale: 'cs',
			key: 'privacyMd',
			loader: async () => (await import('./cs/presentation/privacy.md?raw')).default
		},
		{
			locale: 'cs',
			key: 'tutoringIndividual',
			loader: async () => (await import('./cs/presentation/tutoring-individual.json')).default
		},
		{
			locale: 'cs',
			key: 'tutoringTests',
			loader: async () => (await import('./cs/presentation/tutoring-tests.json')).default
		},
		{
			locale: 'cs',
			key: 'tutoringOnline',
			loader: async () => (await import('./cs/presentation/tutoring-online.json')).default
		},
		{
			locale: 'cs',
			key: 'tutoringPresence',
			loader: async () => (await import('./cs/presentation/tutoring-presence.json')).default
		},
		{
			locale: 'cs',
			key: 'preparations8g',
			loader: async () => (await import('./cs/presentation/preparation-8g.json')).default
		},
		{
			locale: 'cs',
			key: 'preparationsHs',
			loader: async () => (await import('./cs/presentation/preparation-hs.json')).default
		},
		// Panel
		{
			locale: 'cs',
			key: 'panel',
			loader: async () => (await import('./cs/panel/panel.json')).default
		},
		{
			locale: 'cs',
			key: 'users',
			loader: async () => (await import('./cs/panel/users.json')).default
		},
		{
			locale: 'cs',
			key: 'lectures',
			loader: async () => (await import('./cs/panel/lectures.json')).default
		},
		{
			locale: 'cs',
			key: 'locations',
			loader: async () => (await import('./cs/panel/locations.json')).default
		},
		{
			locale: 'cs',
			key: 'lectureSearcher',
			loader: async () => (await import('./cs/panel/lecture-searcher.json')).default
		},
		{
			locale: 'cs',
			key: 'dashboard',
			loader: async () => (await import('./cs/panel/dashboard.json')).default
		},
		{
			locale: 'cs',
			key: 'datatable',
			loader: async () => (await import('./cs/panel/datatable.json')).default
		}
	],
	renderTransformer: (value: string) => {
		return (
			value
				// line breaks
				.replace(/\n/g, '<br>')

				// bold: {[bold]...}
				.replace(/\{\[bold](.+?)}/gs, '<strong>$1</strong>')

				// semibold: {[semibold]...}
				.replace(
					/\{\[semibold](.+?)}/gs,
					'<strong class="translations-semibold">$1</strong>'
				)

				// semiboldDark: {[semiboldDark]...}
				.replace(
					/\{\[semiboldDark](.+?)}/gs,
					'<strong class="translations-semibold-dark">$1</strong>'
				)

				// italics: {[italics]...}
				.replace(/\{\[italics](.+?)}/gs, '<em>$1</em>')

				// primary: {[primary]...}
				.replace(/\{\[primary](.+?)}/gs, '<span class="translations-primary">$1</span>')

				// secondary: {[secondary]...}
				.replace(/\{\[secondary](.+?)}/gs, '<span class="translations-secondary">$1</span>')

				// link: {[link=URL]...}
				.replace(/\{\[link=(.+?)](.+?)}/gs, '<a class="translations-link" href="$1">$2</a>')
		);
	}
};

export const translations = new Translations(config);
export const { T, t, TMd, md, locales, locale, loadTranslations } = translations;
