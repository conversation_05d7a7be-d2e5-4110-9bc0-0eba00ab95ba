import { marked } from 'marked';

export function convertMarkdownToHtml(markdown: string): string {
	if (!markdown) return '';

	marked.use({
		extensions: [
			{
				name: 'group',
				level: 'block',
				start(src) {
					return src.match(/:::group/)?.index;
				},
				tokenizer(src) {
					const match = /^:::group\s*([\s\S]+?)\s*:::/m.exec(src);
					if (match) {
						return {
							type: 'group',
							raw: match[0],
							text: match[1].trim()
						};
					}
				},
				renderer(token) {
					const contentWithSpacings = token.text.replace(
						/---spacing-([a-z]+)/g,
						(_: string, size: string) => `<div class="spacing-${size}"></div>`
					);

					return `<div class="group">\n${marked.parse(contentWithSpacings)}\n</div>`;
				}
			}
		]
	});

	try {
		// We cast it to string because async is set to false, so it should never be a promise
		return marked.parse(markdown, { async: false }) as string;
	} catch (error) {
		console.error('Error parsing markdown:', error);
		return markdown;
	}
}
