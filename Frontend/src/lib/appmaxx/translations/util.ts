import { setContext } from 'svelte';

import { TRANSLATIONS_CTX } from '$lib/appmaxx/translations/constants';
import type { TranslationsAPI } from '$lib/appmaxx/translations/types';

export function getLocale(url: URL, request: Request): string {
	return (
		url.searchParams.get('lang') ||
		getLocaleCookie(request) ||
		request.headers.get('accept-language')?.split(',')[0].split('-')[0] ||
		'en'
	);
}

export function setTranslationsContext(t: TranslationsAPI): TranslationsAPI {
	return setContext(TRANSLATIONS_CTX, t);
}

function getLocaleCookie(request: Request): string | null {
	const cookie = request.headers.get('cookie');
	if (!cookie) return null;

	const match = cookie.match(/(^|;)\s*locale=([^;]+)/);
	return match ? match[2] : null;
}
