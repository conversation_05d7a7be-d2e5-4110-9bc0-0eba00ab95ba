<script lang="ts">
	import { getContext, hasContext } from 'svelte';

	import { TRANSLATIONS_CTX } from '$lib/appmaxx/translations/constants';
	import type { TMdCompProps, TranslationsAPI } from '$lib/appmaxx/translations/types';
	import { exists } from '$lib/appmaxx/util';

	let { key }: TMdCompProps = $props();
	if (!hasContext(TRANSLATIONS_CTX))
		throw new Error(
			'No translations context provided. Make sure to call setTranslationsContext(Translations instance) in your root layout.'
		);

	const translationsAPI = getContext<TranslationsAPI>(TRANSLATIONS_CTX);

	if (!exists(key)) throw new Error('Key must be provided');

	const mdReadable = translationsAPI.md;
</script>

<!-- We are sure that the translation Markdown HTML is safe, so we disable the eslint rule -->
<!-- eslint-disable-next-line svelte/no-at-html-tags -->
{@html $mdReadable(key)}
