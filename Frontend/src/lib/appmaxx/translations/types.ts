import type { Readable, Writable } from 'svelte/store';

export type TranslationValue = string | TranslationRecord;

export type TranslationPlaceholderResolver = (key: string, args: string[]) => string;
export type TranslationRenderTransformer = (translatedValue: string) => string;

export const defaultPlaceholderResolver: TranslationPlaceholderResolver = (
	key: string,
	args: string[]
) => {
	const placeholders = args.map((arg, index) => ({ key: `{${index}}`, value: arg }));
	return placeholders.reduce((acc, { key, value }) => acc.replace(key, value), key);
};

export const defaultRenderTransformer: TranslationRenderTransformer = (translatedValue: string) =>
	translatedValue;

export interface TranslationRecord {
	[key: string]: TranslationValue;
}

type TranslationsMdFile = string;
export type TranslationsObject = TranslationsMdFile | Record<string, TranslationValue>;

export type TranslationsLoaderFunction = () => Promise<TranslationsObject | string>;

export type TFunction = Readable<(key: string, ...args: string[]) => string>;
export type TMdFunction = Readable<(key: string) => string>;

export interface TCompProps {
	key?: string;
	providedValue?: string;
	params?: string[];
	renderAsHtml?: boolean;
}

export interface TMdCompProps {
	key?: string;
}

export interface TranslationLoader {
	locale: string;
	key: string;
	loader: TranslationsLoaderFunction;
}

export interface TranslationsConfig {
	loaders: TranslationLoader[];
	defaultLocale?: string;
	placeholderResolver?: TranslationPlaceholderResolver;
	renderTransformer?: TranslationRenderTransformer;
}

export interface TranslationsAPI {
	t: Readable<(key: string, ...params: string[]) => string>;
	md: Readable<(key: string) => string>;
	loading: Writable<boolean>;
	locale: Writable<string>;
	locales: Readable<string[]>;
	loadTranslations: (locale?: string) => Promise<void>;
}
