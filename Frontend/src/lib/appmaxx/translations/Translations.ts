import type { Component } from 'svelte';
import type { Readable, Writable } from 'svelte/store';
import { derived, get, readable, writable } from 'svelte/store';

import { convertMarkdownToHtml } from '$lib/appmaxx/translations/md-util';
import TComp from '$lib/appmaxx/translations/TComp.svelte';
import TMdComp from '$lib/appmaxx/translations/TMdComp.svelte';

import {
	defaultPlaceholderResolver,
	defaultRenderTransformer,
	type TCompProps,
	type TFunction,
	type TMdCompProps,
	type TMdFunction,
	type TranslationPlaceholderResolver,
	type TranslationRecord,
	type TranslationRenderTransformer,
	type TranslationsAPI,
	type TranslationsConfig,
	type TranslationsObject,
	type TranslationValue
} from './types';

export class Translations implements TranslationsAPI {
	private readonly config: TranslationsConfig;
	private readonly translationsStore: Writable<Record<string, TranslationsObject>>;

	public readonly loading: Writable<boolean>;
	public readonly locale: Writable<string>;
	public readonly locales: Readable<string[]>;

	private readonly placeholderResolver: TranslationPlaceholderResolver;
	private readonly renderTransformer: TranslationRenderTransformer;

	constructor(config: TranslationsConfig) {
		this.config = config;

		this.loading = writable(true);
		this.locale = writable(config.defaultLocale || this.getAvailableLocales()[0]);
		this.locales = readable(this.getAvailableLocales());
		this.translationsStore = writable({});

		this.placeholderResolver = config.placeholderResolver || defaultPlaceholderResolver;
		this.renderTransformer = config.renderTransformer || defaultRenderTransformer;
	}

	private getAvailableLocales(): string[] {
		return Array.from(new Set(this.config.loaders.map((l) => l.locale)));
	}

	public readonly loadTranslations = async (locale?: string): Promise<void> => {
		let targetLocale = locale || get(this.locale);
		const supportedLocales = this.getAvailableLocales();
		if (!supportedLocales.includes(targetLocale)) {
			targetLocale = this.config.defaultLocale || supportedLocales[0];
		}

		if (locale) this.locale.set(targetLocale);
		this.loading.set(true);

		try {
			const loaders = this.config.loaders.filter((l) => l.locale === targetLocale);
			const results = await Promise.all(
				loaders.map(async (loader) => ({
					key: `${loader.locale}.${loader.key}`,
					translations: await loader.loader()
				}))
			);

			const current = get(this.translationsStore);
			const updated = { ...current };

			results.forEach(({ key, translations }) => {
				updated[key] = translations;
			});

			this.translationsStore.set(updated);
		} catch (error) {
			console.error('Failed to load translations:', error);
		} finally {
			this.loading.set(false);
		}
	};

	public get t(): TFunction {
		return derived(
			[this.locale, this.translationsStore],
			([$locale, $translations]) =>
				(key: string, ...args: string[]): string => {
					const [namespace, ...pathParts] = key.split('.');
					const namespaceKey = `${$locale}.${namespace}`;
					let result: TranslationValue | undefined = $translations[namespaceKey];

					if (!result) return key;

					for (const part of pathParts) {
						if (typeof result !== 'object' || result === null || !(part in result)) {
							return key;
						}

						result = (result as TranslationRecord)[part];
					}

					if (typeof result !== 'string') return key;

					return this.renderTransformer(this.placeholderResolver(result, args));
				}
		);
	}

	public get md(): TMdFunction {
		return derived(
			[this.locale, this.translationsStore],
			([$locale, $translations]) =>
				(key: string): string => {
					const namespaceKey = `${$locale}.${key}`;
					const result: TranslationValue | undefined = $translations[namespaceKey];

					if (!result) return key;

					return convertMarkdownToHtml(result as string);
				}
		);
	}

	// Component has no export - hence the empty object type
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
	public get T(): Component<TCompProps, {}, ''> {
		return TComp;
	}

	// Component has no export - hence the empty object type
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
	public get TMd(): Component<TMdCompProps, {}, ''> {
		return TMdComp;
	}
}
