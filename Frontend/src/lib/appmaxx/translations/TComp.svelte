<script lang="ts">
	import { getContext, hasContext } from 'svelte';

	import { TRANSLATIONS_CTX } from '$lib/appmaxx/translations/constants';
	import type { TCompProps, TranslationsAPI } from '$lib/appmaxx/translations/types';
	import { exists } from '$lib/appmaxx/util';

	let { key, providedValue, renderAsHtml = true, params = [] }: TCompProps = $props();
	if (!hasContext(TRANSLATIONS_CTX))
		throw new Error(
			'No translations context provided. Make sure to call setTranslationsContext(Translations instance) in your root layout.'
		);

	const translationsAPI = getContext<TranslationsAPI>(TRANSLATIONS_CTX);
	const tReadable = translationsAPI.t;

	if (!exists(key) && !exists(providedValue))
		throw new Error('Either key or providedValue must be provided');

	let value = $derived.by(() => {
		if (exists(providedValue)) return providedValue;
		if (!key) throw new Error('Key must be provided if no providedValue is provided');
		return $tReadable(key, ...params);
	});
</script>

{#if renderAsHtml}
	<!-- We are sure that the translation HTML is safe, so we disable the eslint rule -->
	<!-- eslint-disable-next-line svelte/no-at-html-tags -->
	{@html value}
{:else}
	{value}
{/if}
