import type { ActionReturn } from 'svelte/action';

import { assertIsElement, exists, isEmpty } from '../util';

const CLICK_OUTSIDE_EVENT_TYPE = 'clickoutside';
type ClickEventTypes = 'click' | 'contextmenu';

interface Parameters {
	disabled: boolean;
	eventsTypes: ClickEventTypes[];
	ignoredElementsSelectors: string[];
}

const defaultParams: Parameters = {
	disabled: false,
	eventsTypes: ['click'],
	ignoredElementsSelectors: []
};

interface Attributes {
	onclickoutside?: (e: CustomEvent<void>) => void;
}

export function clickOutside(
	node: HTMLElement,
	customParams: Partial<Parameters> = defaultParams
): ActionReturn<Parameters, Attributes> {
	const params: Parameters = { ...defaultParams, ...customParams };
	let isDisabled = params.disabled;

	const documentClickListener = (event: MouseEvent) => {
		if (isDisabled) {
			return;
		}

		assertIsElement(event.target);
		if (!node || event.defaultPrevented) {
			return;
		}

		if (isIgnored(event.target, params.ignoredElementsSelectors)) {
			return;
		}

		if (!node.contains(event.target)) {
			node.dispatchEvent(new CustomEvent(CLICK_OUTSIDE_EVENT_TYPE));
		}
	};

	(params.eventsTypes ?? defaultParams.eventsTypes).forEach((type) =>
		document.addEventListener(type, documentClickListener, true)
	);

	return {
		update(parameters) {
			isDisabled = parameters.disabled;
		},
		destroy: () =>
			(params.eventsTypes ?? defaultParams.eventsTypes).forEach((type) =>
				document.removeEventListener(type, documentClickListener, true)
			)
	};
}

function isIgnored(currentElement: Element | null, ignoredSelectors: string[]): boolean {
	if (isEmpty(ignoredSelectors)) {
		return false;
	}

	while (exists(currentElement)) {
		for (const ignoredSelector of ignoredSelectors) {
			if (currentElement.matches(ignoredSelector)) {
				return true;
			}
		}

		currentElement = currentElement.parentElement;
	}
	return false;
}
