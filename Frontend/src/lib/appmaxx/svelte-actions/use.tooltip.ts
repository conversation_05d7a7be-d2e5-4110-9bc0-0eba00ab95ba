import './tooltip.styles.scss';

import type { ActionReturn } from 'svelte/action';
import type { Instance } from 'tippy.js';
import tippy from 'tippy.js';

type TooltipParams =
	| string
	| {
			content: string;
			enabled: boolean;
	  };

export function tooltip(node: HTMLElement, params: TooltipParams): ActionReturn<TooltipParams> {
	let instance: Instance;

	if (typeof params === 'string') {
		instance = tippy(node, {
			content: params,
			zIndex: 1000000,
			duration: [null, 0],
			role: 'tooltip'
		});
	} else {
		instance = tippy(node, {
			content: params.content,
			zIndex: 1000000,
			duration: [null, 0],
			role: 'tooltip'
		});

		if (!params.enabled) {
			instance.disable();
		}
	}

	return {
		update: (updatedContentOrConfig: TooltipParams) => {
			if (typeof updatedContentOrConfig === 'string') {
				instance.setContent(updatedContentOrConfig);
			} else {
				instance.setContent(updatedContentOrConfig.content);

				if (updatedContentOrConfig.enabled) {
					instance.enable();
				} else {
					instance.disable();
				}
			}
		},
		destroy: () => {
			instance.destroy();
		}
	};
}
