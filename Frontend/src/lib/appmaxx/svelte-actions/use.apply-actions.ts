import type { ActionReturn } from 'svelte/action';

import { exists, isFunction, isObject } from '../util';

export type SvelteAction<
	P = Record<string, unknown>,
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
	E extends Record<string, CustomEvent<unknown>> = {}
> = (node: HTMLElement, params?: P) => ActionReturn<P, E>;

export type ActionArray = SvelteAction[];

export interface Actionable {
	use?: ActionArray;
}

export function applyActions(node: HTMLElement, actions?: ActionArray) {
	const actionReturns: (ActionReturn | void)[] = [];

	if (exists(actions)) {
		actions.forEach((actionEntry) => {
			const action = Array.isArray(actionEntry) ? actionEntry[0] : actionEntry;
			if (Array.isArray(actionEntry) && actionEntry.length > 1) {
				actionReturns.push(action(node, actionEntry[1]));
			} else {
				actionReturns.push(action(node));
			}
		});
	}

	return {
		update(updatedActions: ActionArray) {
			if (((updatedActions && updatedActions.length) || 0) !== actionReturns.length) {
				throw new Error('You must not change the length of an actions array.');
			}

			if (exists(updatedActions)) {
				for (let i = 0; i < updatedActions.length; i++) {
					const returnEntry = actionReturns[i];
					if (isObject(returnEntry) && isFunction(returnEntry.update)) {
						const actionEntry = updatedActions[i];
						if (Array.isArray(actionEntry) && actionEntry.length > 1) {
							returnEntry.update(actionEntry[1]);
						} else {
							returnEntry.update(undefined);
						}
					}
				}
			}
		},

		destroy() {
			actionReturns.forEach((returnEntry) => {
				if (isObject(returnEntry) && isFunction(returnEntry.destroy)) {
					returnEntry.destroy();
				}
			});
		}
	};
}
