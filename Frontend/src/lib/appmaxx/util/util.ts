export function isElement(value: unknown): value is Element {
	return value instanceof Element;
}

export function assertIsElement(value: unknown): asserts value is Element {
	if (!isElement(value)) {
		throw new TypeError(`${stringifyArgument(value)} is not a Element`);
	}
}

export function exists<T>(value: T | null | undefined): value is NonNullable<T> {
	return !isNull(value) && !isUndefined(value);
}

export function isFunction(
	functionToCheck: unknown
): functionToCheck is (...args: unknown[]) => unknown {
	return exists(functionToCheck) && {}.toString.call(functionToCheck) === '[object Function]';
}

export function isObject(value: unknown): value is Record<string, unknown> {
	const type = typeof value;
	return type === 'function' || (type === 'object' && exists(value));
}

export function assertExists<T>(value: T | null | undefined): asserts value is NonNullable<T> {
	if (isNullOrUndefined(value)) {
		throw new TypeError('Value is nullable');
	}
}

export function isNull<T>(value: T | null | undefined): value is null {
	return value === null;
}

export function isUndefined<T>(value: T | null | undefined): value is undefined {
	return typeof value === 'undefined';
}

export function isNullOrUndefined<T>(value: T | null | undefined): value is null | undefined {
	return isNull(value) || isUndefined(value);
}

export function isEmpty<T>(array: T[]) {
	assertIsArray(array);
	return array.length === 0;
}

export function isArray(arg: unknown): arg is unknown[] {
	return Array.isArray(arg);
}

export function assertIsArray(arg: unknown): asserts arg is unknown[] {
	if (!isArray(arg)) {
		throw new Error(`Argument ${stringifyArgument(arg)} is not an array`);
	}
}

export function stringifyArgument(arg: unknown): string {
	const asString = arg?.toString();
	const type = typeof arg;

	if (type !== 'object') {
		return `{type=${type}, value=${asString}}`;
	}

	let asJson: string;

	try {
		asJson = JSON.stringify(arg);
	} catch {
		asJson = 'JSON.stringify ERROR';
	}

	return `{type=${type}, value=${asJson}}`;
}

export function range(start: number, end: number): number[] {
	return Array.from({ length: end - start + 1 }, (_, i) => start + i);
}
