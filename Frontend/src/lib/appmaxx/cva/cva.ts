import type { ClassValue } from 'svelte/elements';

import { isResponsiveType } from '$lib/appmaxx/cva/util';

const breakpointPrefixes = {
	mobile: 'mobile:',
	tablet: 'tablet:',
	desktop: 'desktop:'
};

export function cva<T extends Record<string, unknown>>(
	mainClass: string,
	variants: Record<string, Record<string, string>>,
	defaultVariants?: Partial<T>
): (values?: Partial<T>, className?: ClassValue | null | undefined) => string {
	return (
		props: Partial<T> = {},
		className: ClassValue | null | undefined = undefined
	): string => {
		let classes = mainClass;

		for (const key of Object.keys(variants)) {
			const explicit = props[key];
			const value = explicit !== undefined ? explicit : defaultVariants?.[key];

			if (value === undefined || value === null) continue;

			const map = variants[key];

			if (isResponsiveType(value)) {
				for (const [bp, bpVal] of Object.entries(value)) {
					if (bpVal == null) continue;
					const className = map[String(bpVal) as keyof typeof map];
					if (className) {
						classes += ` ${breakpointPrefixes[bp as keyof typeof breakpointPrefixes]}${className}`;
					}
				}
			} else {
				const className = map[String(value) as keyof typeof map];
				if (className) classes += ` ${className}`;
			}
		}

		if (className) classes += ` ${className.toString()}`;

		return classes.trim();
	};
}
