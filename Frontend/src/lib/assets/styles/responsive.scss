$breakpoints: (
	mobile: '(max-width: 639px)',
	tablet: '(min-width: 640px) and (max-width: 1023px)',
	desktop: '(min-width: 1024px)'
);

@mixin responsive-class($class, $property, $value) {
	.#{$class} {
		#{$property}: $value;
	}

	@each $name, $query in $breakpoints {
		.#{$name}\:#{$class} {
			@media #{$query} {
				#{$property}: $value;
			}
		}
	}
}

@mixin responsive-class-block($class) {
	.#{$class} {
		@content;
	}

	@each $name, $query in $breakpoints {
		.#{$name}\:#{$class} {
			@media #{$query} {
				@content;
			}
		}
	}
}

@mixin paddings() {
	// Horizontal padding
	@include responsive-class-block('px-xs') {
		padding-left: var(--spacing-xs);
		padding-right: var(--spacing-xs);
	}

	@include responsive-class-block('px-s') {
		padding-left: var(--spacing-s);
		padding-right: var(--spacing-s);
	}

	@include responsive-class-block('px-sm') {
		padding-left: var(--spacing-sm);
		padding-right: var(--spacing-sm);
	}

	@include responsive-class-block('px-m') {
		padding-left: var(--spacing-m);
		padding-right: var(--spacing-m);
	}

	@include responsive-class-block('px-ml') {
		padding-left: var(--spacing-ml);
		padding-right: var(--spacing-ml);
	}

	@include responsive-class-block('px-l') {
		padding-left: var(--spacing-l);
		padding-right: var(--spacing-l);
	}

	@include responsive-class-block('px-xl') {
		padding-left: var(--spacing-xl);
		padding-right: var(--spacing-xl);
	}

	@include responsive-class-block('px-xxl') {
		padding-left: var(--spacing-xxl);
		padding-right: var(--spacing-xxl);
	}

	@include responsive-class-block('px-none') {
		padding-left: 0;
		padding-right: 0;
	}

	// Vertical padding
	@include responsive-class-block('py-xs') {
		padding-top: var(--spacing-xs);
		padding-bottom: var(--spacing-xs);
	}

	@include responsive-class-block('py-s') {
		padding-top: var(--spacing-s);
		padding-bottom: var(--spacing-s);
	}

	@include responsive-class-block('py-sm') {
		padding-top: var(--spacing-sm);
		padding-bottom: var(--spacing-sm);
	}

	@include responsive-class-block('py-m') {
		padding-top: var(--spacing-m);
		padding-bottom: var(--spacing-m);
	}

	@include responsive-class-block('py-ml') {
		padding-top: var(--spacing-ml);
		padding-bottom: var(--spacing-ml);
	}

	@include responsive-class-block('py-l') {
		padding-top: var(--spacing-l);
		padding-bottom: var(--spacing-l);
	}

	@include responsive-class-block('py-xl') {
		padding-top: var(--spacing-xl);
		padding-bottom: var(--spacing-xl);
	}

	@include responsive-class-block('py-xxl') {
		padding-top: var(--spacing-xxl);
		padding-bottom: var(--spacing-xxl);
	}

	@include responsive-class-block('py-none') {
		padding-top: 0;
		padding-bottom: 0;
	}
}
