<script lang="ts">
	import type { IconAssetProps } from '$lib/assets/types';

	const { size, ...props }: IconAssetProps = $props();
</script>

<svg
	width={size ?? 24}
	height={size ?? 24}
	viewBox="0 0 25 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<g clip-path="url(#clip0_3013_20300)">
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M24.1172 23.24C23.9772 23.1 23.3572 22.93 23.2172 23.07L6.10722 22.7C5.14815 22.7598 4.18629 22.7598 3.22722 22.7C2.69814 22.6794 2.19119 22.4823 1.78722 22.14C1.56185 21.8038 1.44974 21.4044 1.46722 21C1.43876 20.1997 1.45545 19.3984 1.51722 18.6C1.57722 15.95 1.61722 6.26 1.65722 2.22V0.589998C1.66211 0.563555 1.66211 0.53644 1.65722 0.509998C1.68722 0.0599979 1.22722 -0.230002 1.05722 0.369998C1.05193 0.419858 1.05193 0.470138 1.05722 0.519998C1.05722 0.779998 1.05722 1.36 0.977222 2.17C0.797222 6.23 0.447222 15.91 0.307222 18.55C0.219126 19.5481 0.219126 20.5519 0.307222 21.55C0.353993 22.0816 0.559564 22.5868 0.897222 23C1.34924 23.4185 1.9112 23.6995 2.51722 23.81C3.70726 23.9691 4.91127 23.996 6.10722 23.89C9.02722 23.89 18.2072 24.01 22.1072 23.95C22.8872 23.95 23.4672 23.95 23.7372 23.89C23.8777 23.8804 24.0127 23.8319 24.1272 23.75C24.1601 23.7159 24.186 23.6756 24.2033 23.6315C24.2206 23.5874 24.2291 23.5403 24.2281 23.4929C24.2272 23.4455 24.2169 23.3988 24.1979 23.3554C24.1789 23.312 24.1514 23.2728 24.1172 23.24Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M4.74719 9C4.74719 9 5.37719 9.11 5.99719 9.27C6.26183 9.33405 6.52232 9.4142 6.77719 9.51C7.43376 9.7905 8.06593 10.125 8.66719 10.51C9.26646 10.8711 9.82912 11.2898 10.3472 11.76C11.1623 12.4433 11.8409 13.2746 12.3472 14.21C12.6885 14.8455 12.9601 15.5161 13.1572 16.21C13.6275 17.9956 13.8694 19.8336 13.8772 21.68C13.8869 21.7667 13.9277 21.8469 13.9921 21.9058C14.0564 21.9646 14.14 21.9981 14.2272 22C14.3236 22 14.4162 21.9624 14.4853 21.8952C14.5545 21.8279 14.5946 21.7364 14.5972 21.64C14.799 19.3165 14.5615 16.9757 13.8972 14.74C13.3454 13.2151 12.3918 11.8676 11.1372 10.84C10.5128 10.365 9.84284 9.95295 9.13719 9.61C8.45843 9.27183 7.75265 8.99086 7.02719 8.77C6.78719 8.7 6.45719 8.63 6.12719 8.57C5.47719 8.46 4.81719 8.39 4.81719 8.39C4.74909 8.40181 4.68664 8.43536 4.6392 8.48561C4.59175 8.53587 4.56185 8.60014 4.55397 8.66881C4.54609 8.73748 4.56065 8.80685 4.59548 8.86655C4.63031 8.92625 4.68354 8.97307 4.74719 9Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M2.96723 20.7C3.24723 20.8 3.13723 20.61 4.11723 20.12C4.38723 19.98 4.60723 19.85 4.69723 19.79C4.87747 19.6267 5.04465 19.4494 5.19723 19.26C5.45723 18.94 5.70723 18.52 5.92723 18.26C5.92723 18.26 6.04723 18.18 6.02723 18.15C6.1066 18.1319 6.17749 18.0875 6.22834 18.0239C6.2792 17.9603 6.307 17.8814 6.30723 17.8C6.30726 17.7036 6.26966 17.611 6.20242 17.5418C6.13518 17.4727 6.04362 17.4326 5.94723 17.43C5.6308 17.4983 5.33568 17.6425 5.08723 17.85C4.85844 18.0011 4.63811 18.1647 4.42723 18.34C4.17723 18.56 3.98723 18.78 3.89723 18.85C3.80723 18.92 3.02723 19.72 2.83723 20C2.64723 20.28 2.71723 20.65 2.96723 20.7Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M10.7772 13.61C10.7786 13.561 10.77 13.5123 10.7519 13.4668C10.7338 13.4213 10.7066 13.3799 10.6719 13.3453C10.6373 13.3107 10.596 13.2835 10.5504 13.2653C10.5049 13.2472 10.4562 13.2386 10.4072 13.24C9.98867 13.3352 9.60244 13.5386 9.28724 13.83C9.08245 13.9762 8.88551 14.1331 8.69724 14.3C8.47724 14.52 8.31724 14.73 8.23724 14.81C8.15724 14.89 7.65724 15.61 7.48724 15.9C7.43702 15.9761 7.39352 16.0564 7.35724 16.14C7.35724 16.34 7.35724 16.46 7.50724 16.51C7.57459 16.5372 7.64989 16.5372 7.71724 16.51C7.95724 16.51 7.59724 16.51 9.02724 15.65C9.28016 15.4518 9.51752 15.2345 9.73724 15C9.99724 14.68 10.2072 14.31 10.4072 14.1C10.4072 14.1 10.5072 14.04 10.4872 14.01C10.5775 13.9929 10.6578 13.942 10.7117 13.8676C10.7656 13.7932 10.789 13.7011 10.7772 13.61Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M13.9672 10.51C14.3772 10.18 14.8172 9.91 15.2472 9.58C15.4272 9.45 15.5972 9.3 15.7672 9.15L16.2472 8.65C16.6172 8.25 16.9472 7.82 17.2472 7.39C17.3156 7.32075 17.354 7.22733 17.354 7.13C17.354 7.03266 17.3156 6.93925 17.2472 6.87C17.2138 6.83644 17.174 6.80981 17.1303 6.79164C17.0865 6.77347 17.0396 6.76411 16.9922 6.76411C16.9448 6.76411 16.8979 6.77347 16.8542 6.79164C16.8104 6.80981 16.7707 6.83644 16.7372 6.87C16.2972 7.2 15.8372 7.49 15.3972 7.87C15.1023 8.09176 14.8248 8.33581 14.5672 8.6C14.151 9.07479 13.7794 9.58696 13.4572 10.13C13.4254 10.1608 13.4004 10.1978 13.3836 10.2388C13.3669 10.2797 13.3588 10.3237 13.3599 10.368C13.361 10.4122 13.3713 10.4557 13.3901 10.4958C13.4089 10.5359 13.4358 10.5716 13.4691 10.6007C13.5024 10.6299 13.5414 10.6518 13.5836 10.6651C13.6258 10.6784 13.6703 10.6828 13.7143 10.678C13.7583 10.6733 13.8008 10.6594 13.8392 10.6374C13.8776 10.6154 13.9109 10.5856 13.9372 10.55L13.9672 10.51Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M19.3372 5.39999C19.4672 5.32999 19.8072 5.07999 19.8572 5.03999L20.8572 4.20999C21.0072 4.05999 21.3672 3.73999 21.7372 3.34999C22.1072 2.95999 22.2772 2.72999 22.5372 2.45999C23.3372 1.64999 23.5372 1.92999 23.5372 1.55999C23.5372 1.51141 23.5276 1.46329 23.509 1.4184C23.4904 1.37351 23.4632 1.33272 23.4288 1.29837C23.3945 1.26401 23.3537 1.23675 23.3088 1.21816C23.2639 1.19957 23.2158 1.18999 23.1672 1.18999C22.6045 1.29353 22.0798 1.54556 21.6472 1.91999C21.3572 2.12999 21.0472 2.36999 20.7872 2.59999C20.5272 2.82999 20.1872 3.23999 20.0572 3.37999L19.2272 4.51999C19.0185 4.66646 18.8584 4.87191 18.7672 5.10999C18.7586 5.17937 18.7715 5.24972 18.8043 5.31146C18.8371 5.37321 18.8881 5.42335 18.9504 5.45505C19.0127 5.48675 19.0833 5.49847 19.1525 5.48862C19.2217 5.47877 19.2862 5.44782 19.3372 5.39999Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
	</g>
	<defs>
		<clipPath id="clip0_3013_20300">
			<rect width="24" height="24" fill="white" transform="translate(0.227234)" />
		</clipPath>
	</defs>
</svg>
