<script lang="ts">
	import type { IconAssetProps } from '$lib/assets/types';

	const { size, ...props }: IconAssetProps = $props();
</script>

<svg
	width={size ?? 24}
	height={size ?? 24}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<g clip-path="url(#clip0_3013_17609)">
		<path
			d="M15.5082 19.28C15.6482 19.87 15.7082 20.52 15.8782 21.13C15.931 21.307 15.9945 21.4806 16.0682 21.65H15.5482H14.1882H12.1382C11.4582 21.65 10.6782 21.73 9.9382 21.81C10.0382 21.56 10.1382 21.32 10.2182 21.07C10.3782 20.62 10.4782 20.15 10.6082 19.69L10.7982 18.61L14.2682 18.72H15.4182C15.4582 18.9067 15.4882 19.0933 15.5082 19.28ZM1.7482 11.3V9.05001C1.76088 8.49019 1.81435 7.93205 1.9082 7.38001C2.0282 6.54001 2.2182 5.72001 2.3882 4.86001C2.45208 4.37398 2.54897 3.89287 2.6782 3.42001C2.75654 3.1363 2.89654 2.87338 3.0882 2.65001C3.11512 2.62046 3.13596 2.5859 3.14952 2.5483C3.16309 2.5107 3.16911 2.47079 3.16726 2.43087C3.1654 2.39094 3.1557 2.35176 3.1387 2.31558C3.1217 2.27941 3.09775 2.24693 3.0682 2.22001C3.03865 2.19309 3.00409 2.17225 2.96649 2.15868C2.92889 2.14512 2.88899 2.13909 2.84906 2.14095C2.80913 2.14281 2.76996 2.15251 2.73378 2.16951C2.6976 2.1865 2.66512 2.21046 2.6382 2.24001C2.38482 2.53133 2.19418 2.87174 2.0782 3.24001C1.9414 3.73036 1.83124 4.22775 1.7482 4.73001C1.5582 5.60001 1.3582 6.43001 1.2082 7.27001C1.09275 7.8412 1.0126 8.41896 0.968201 9.00001L0.968201 11.3C0.968201 12.08 0.968201 12.85 1.0482 13.6C1.08707 14.7254 1.37389 15.8283 1.8882 16.83C2.08782 17.2081 2.37226 17.5349 2.71924 17.7847C3.06621 18.0345 3.46631 18.2006 3.8882 18.27C4.19248 18.3225 4.49975 18.3559 4.8082 18.37C5.4882 18.37 6.2582 18.37 6.8082 18.47C7.33938 18.5305 7.87359 18.5606 8.4082 18.56H10.0182C9.9182 18.83 9.8382 19.11 9.7382 19.38C9.6182 19.84 9.4682 20.29 9.3582 20.75C9.2482 21.21 9.2082 21.49 9.1282 21.86C8.79739 21.9168 8.46345 21.9535 8.1282 21.97C7.74111 22.0079 7.3506 21.9911 6.9682 21.92C6.93012 21.9094 6.89031 21.9064 6.85107 21.9112C6.81182 21.916 6.77392 21.9286 6.73954 21.9481C6.70515 21.9676 6.67496 21.9937 6.65071 22.0249C6.62645 22.0562 6.60861 22.0919 6.5982 22.13C6.58758 22.1681 6.58459 22.2079 6.58941 22.2471C6.59423 22.2864 6.60675 22.3243 6.62627 22.3587C6.64579 22.3931 6.67191 22.4232 6.70313 22.4475C6.73435 22.4718 6.77006 22.4896 6.8082 22.5C7.21636 22.6314 7.63996 22.7087 8.0682 22.73H9.8982H11.5882H13.2882C14.0482 22.73 14.8082 22.67 15.5782 22.62L16.6782 22.55H16.8182L17.7582 22.49L18.6682 22.43C18.7122 22.4385 18.7574 22.4383 18.8013 22.4294C18.8453 22.4204 18.887 22.4029 18.9241 22.3778C18.9613 22.3528 18.9931 22.3206 19.0178 22.2833C19.0425 22.2459 19.0597 22.204 19.0682 22.16C19.0767 22.116 19.0765 22.0708 19.0676 22.0269C19.0586 21.983 19.0411 21.9412 19.016 21.9041C18.991 21.8669 18.9588 21.8351 18.9215 21.8104C18.8841 21.7857 18.8422 21.7685 18.7982 21.76L17.7982 21.71H16.9682L16.4182 19.09C16.4182 18.96 16.3282 18.82 16.2782 18.67H19.6182C20.2844 18.705 20.952 18.705 21.6182 18.67C21.9619 18.6421 22.2993 18.5612 22.6182 18.43C23.0807 18.2108 23.4546 17.8404 23.6782 17.38C24.0406 16.5193 24.2893 15.615 24.4182 14.69C24.7648 13.2043 24.9492 11.6855 24.9682 10.16C24.9901 8.67402 24.9233 7.18804 24.7682 5.71001C24.7525 4.91331 24.6621 4.11983 24.4982 3.34001C24.3976 2.9194 24.1666 2.54138 23.8382 2.26001C23.5734 2.05229 23.2662 1.90547 22.9382 1.83001C22.3171 1.71138 21.6892 1.63123 21.0582 1.59001C20.3482 1.49001 19.6282 1.44001 18.8982 1.40001C17.2482 1.33001 15.5782 1.40001 13.8982 1.29001C12.8982 1.24001 11.8982 1.23001 10.9582 1.29001C9.5082 1.29001 8.0582 1.44001 6.5882 1.57001L3.5882 1.93001C3.54355 1.93526 3.50037 1.94926 3.46113 1.9712C3.42189 1.99314 3.38736 2.02259 3.3595 2.05788C3.33164 2.09316 3.31101 2.13359 3.29877 2.17685C3.28654 2.22011 3.28295 2.26536 3.2882 2.31001C3.29345 2.35466 3.30745 2.39783 3.32939 2.43707C3.35133 2.47632 3.38078 2.51085 3.41607 2.53871C3.45135 2.56657 3.49178 2.5872 3.53504 2.59943C3.5783 2.61167 3.62355 2.61526 3.6682 2.61001L6.6682 2.30001C8.1282 2.20001 9.5582 2.11001 10.9982 2.09001C11.9482 2.09001 12.8982 2.09001 13.8682 2.17001C15.4982 2.28001 17.1682 2.27001 18.8682 2.37001C19.5614 2.40431 20.2523 2.47441 20.9382 2.58001C21.3682 2.64001 21.9382 2.66001 22.4782 2.77001C22.7427 2.80789 22.9935 2.91099 23.2082 3.07001C23.4323 3.30287 23.5669 3.60749 23.5882 3.93001C23.6732 4.55993 23.7199 5.19443 23.7282 5.83001C23.8538 7.26971 23.9005 8.7152 23.8682 10.16C23.834 11.0606 23.7506 11.9586 23.6182 12.85L23.4882 13.56C23.4882 13.84 23.3782 14.12 23.3182 14.4C23.2012 15.1217 23.0307 15.8336 22.8082 16.53C22.7072 16.8749 22.4782 17.1682 22.1682 17.35C21.8874 17.4576 21.5889 17.5118 21.2882 17.51H19.6482L14.2682 17.6L10.6682 17.48C9.8982 17.48 9.1182 17.54 8.3482 17.55C7.8482 17.55 7.3482 17.55 6.8482 17.49L3.9682 17.36C3.68284 17.308 3.41338 17.1907 3.18091 17.0172C2.94845 16.8437 2.75927 16.6188 2.6282 16.36C1.8982 15.15 1.7982 12.93 1.7482 11.3Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			d="M13.6482 14.79C12.8482 14.29 11.5082 15.26 12.3082 16.06C12.4003 16.1507 12.5047 16.2281 12.6182 16.29C12.8109 16.3982 13.0321 16.4448 13.2521 16.4233C13.472 16.4019 13.6801 16.3134 13.8482 16.17C13.9367 16.0709 14.0025 15.9535 14.0408 15.8263C14.0791 15.699 14.0891 15.5649 14.07 15.4334C14.051 15.3018 14.0033 15.1761 13.9304 15.0649C13.8576 14.9538 13.7612 14.8599 13.6482 14.79Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			d="M3.5582 13.9H6.3382C7.8682 14 9.3882 14 10.9082 14C12.4282 14 13.9682 13.95 15.4982 13.89C17.2482 13.78 18.9982 13.6 20.7382 13.53H21.4782C22.7782 13.59 23.2982 12.84 21.4782 12.71H20.7282C18.9682 12.71 17.2182 12.78 15.4582 12.8C13.6982 12.82 11.8082 12.87 9.9882 12.92C8.7682 12.92 7.5482 13.01 6.3282 13.07C5.4082 13.07 4.4982 13.19 3.5882 13.25C2.2582 13.33 2.3282 14 3.5582 13.9Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
	</g>
	<defs>
		<clipPath id="clip0_3013_17609">
			<rect width="24" height="24" fill="white" transform="translate(0.968201)" />
		</clipPath>
	</defs>
</svg>
