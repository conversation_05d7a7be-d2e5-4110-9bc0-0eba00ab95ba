<script lang="ts">
	import type { IconAssetProps } from '$lib/assets/types';

	const { size, ...props }: IconAssetProps = $props();
</script>

<svg
	width={size ?? 24}
	height={size ?? 24}
	viewBox="0 0 25 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<g clip-path="url(#clip0_3013_12622)">
		<path
			d="M19.1281 11.6899C19.0292 10.375 18.5881 9.10895 17.8486 8.01725C16.2812 5.69615 13.4138 4.62525 10.7086 5.35075C8.01612 5.96385 5.97152 8.15955 5.55142 10.8889C5.47802 11.404 5.47802 11.9269 5.55142 12.4419C5.62672 12.9776 5.82382 13.4889 6.12772 13.9364C6.66492 14.6982 7.49512 14.9131 7.91512 15.5089C8.07792 15.7387 8.15082 16.0201 8.12032 16.3001C8.10912 16.4458 8.21862 16.5729 8.36442 16.5833H8.63792C9.33722 16.7765 10.0486 16.9233 10.7672 17.0228C11.075 17.0618 11.3849 17.0814 11.6951 17.0814H12.623C13.336 17.0814 14.0393 16.9447 14.7621 16.847C14.9293 16.8417 15.0606 16.7017 15.0551 16.5345C15.0499 16.3632 14.9038 16.2303 14.7328 16.2414L12.623 16.1242H11.7244H10.8258H8.71612H8.65752C8.65782 15.7996 8.55902 15.4827 8.37422 15.2159C7.97372 14.6396 7.16302 14.3759 6.65512 13.614C6.43162 13.2388 6.29482 12.8183 6.25472 12.3833C6.19422 11.9258 6.20082 11.4618 6.27422 11.0061C6.72062 8.63095 8.56392 6.76445 10.9333 6.28845C13.2422 5.69145 15.6767 6.61515 17.0086 8.59355C17.6464 9.53115 18.0301 10.6182 18.1221 11.7485C18.244 12.845 17.8881 13.941 17.1453 14.7568C16.9964 14.9048 16.8399 15.045 16.6765 15.1768C16.237 15.5187 15.7974 15.7922 15.3677 16.1536C15.2087 16.3113 15.0913 16.5059 15.0258 16.7201C14.9343 17.0409 14.8625 17.3671 14.8109 17.6968C14.8013 17.9216 14.7267 18.1387 14.596 18.3219C13.9893 18.8424 13.2067 19.1114 12.4081 19.074C11.1858 19.0363 9.98082 18.7748 8.85282 18.3024C8.94072 18.1461 8.85282 17.6675 8.85282 17.3745C8.84232 17.2057 8.69952 17.0759 8.53052 17.0815C8.36312 17.0919 8.23612 17.2364 8.24722 17.4038C8.24722 17.7945 8.17892 18.2926 8.24722 18.5075C8.27422 18.6423 8.36542 18.7554 8.49142 18.8103C9.74472 19.3888 11.0977 19.7205 12.4765 19.787C13.4701 19.8526 14.4503 19.5305 15.2114 18.8884C15.3679 18.7365 15.4852 18.5488 15.5533 18.3415C15.6557 18.0222 15.734 17.6957 15.7877 17.3647C15.783 17.1925 15.8199 17.0216 15.8951 16.8666C16.3249 16.4954 16.7546 16.2512 17.1844 15.8898C17.3871 15.7403 17.5767 15.5736 17.7509 15.3917C18.7259 14.4164 19.2286 13.0653 19.1281 11.6899Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			d="M10.7086 13.9363C10.7672 13.3697 10.8063 12.7935 10.8551 12.227C11.0077 12.3845 11.182 12.5193 11.3728 12.6274C11.6848 12.8247 12.049 12.9235 12.4179 12.9107C12.761 12.9011 13.0964 12.8072 13.3946 12.6372V13.4479C13.3906 14.0829 13.4429 14.717 13.5509 15.3427C13.5509 15.5479 14.0393 15.6748 14.137 15.3427C14.0776 14.7125 14.0776 14.0781 14.137 13.4479C14.273 12.5896 14.4655 11.7413 14.7132 10.9084C14.7772 10.7141 14.6955 10.5017 14.5179 10.4005C14.3346 10.2608 14.0746 10.2864 13.9221 10.4591C13.883 10.5068 13.8502 10.5593 13.8244 10.6153L13.717 10.8302L13.4825 11.2111C13.3507 11.395 13.1808 11.5482 12.9844 11.6604C12.8093 11.7726 12.6063 11.8335 12.3984 11.8363C12.1735 11.8344 11.9562 11.7551 11.783 11.6116C11.5711 11.4577 11.3973 11.2572 11.2751 11.0256L11.0993 10.586C11.0993 10.4981 11.0309 10.2832 11.0114 10.2246C10.9192 10.1017 10.7744 10.0293 10.6207 10.0293C10.462 9.98832 10.2935 10.0369 10.1812 10.1563C9.86861 10.4884 10.1128 12.3637 9.97601 14.5418C10 14.7528 10 14.9658 9.97601 15.1767C9.97601 15.5869 10.4351 15.7042 10.5426 15.2744C10.6465 14.8357 10.7022 14.387 10.7086 13.9363Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			d="M4.24262 6.29824C4.12162 6.15274 3.98752 6.01874 3.84212 5.89774L3.39282 5.56564C3.03142 5.33124 2.65052 5.14564 2.26962 4.93074C2.13142 4.83374 1.94112 4.86394 1.83982 4.99914C1.74382 5.13424 1.77432 5.32144 1.90822 5.41914C2.20122 5.72194 2.48452 6.02474 2.78732 6.31774C2.91422 6.44474 3.04122 6.57164 3.16822 6.67914C3.29522 6.78664 3.43192 6.90374 3.56862 7.01124C3.90702 7.24144 4.23302 7.48924 4.54542 7.75354C4.64822 7.85534 4.81382 7.85534 4.91652 7.75354C5.03952 7.66434 5.06192 7.48994 4.96542 7.37264C4.76652 6.98794 4.52392 6.62744 4.24262 6.29824Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			d="M8.13 1.94193C8.1787 2.12213 8.2408 2.29842 8.3156 2.46933C8.384 2.63543 8.4523 2.80142 8.5402 2.96753C8.7454 3.34842 8.9895 3.69032 9.2044 4.08102C9.3056 4.26562 9.5687 4.27143 9.678 4.09143C9.7137 4.03273 9.7259 3.96273 9.7123 3.89543C9.649 3.45773 9.5609 3.02402 9.4486 2.59632C9.3998 2.42052 9.3314 2.24473 9.263 2.06893C9.2062 1.89833 9.1377 1.73193 9.0579 1.57073C8.804 1.18003 8.5695 0.828425 8.3449 0.447525C8.2899 0.281725 8.1065 0.196725 7.9444 0.261925C7.7918 0.325025 7.7179 0.498925 7.7784 0.652625C7.8956 1.11173 7.9933 1.53173 8.13 1.94193Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			d="M15.4263 4.51073C15.5722 4.52783 15.704 4.42243 15.7193 4.27633C15.8574 3.92103 15.978 3.55923 16.0807 3.19213C16.1295 2.97723 16.149 2.75263 16.1783 2.53773C16.1873 2.20973 16.2232 1.88303 16.2858 1.56103C16.4098 1.34673 16.2554 1.07843 16.0078 1.07813C15.8929 1.07803 15.7866 1.13923 15.729 1.23863C15.5215 1.54133 15.3541 1.86973 15.2309 2.21543C15.1827 2.36263 15.1436 2.51273 15.1137 2.66473C15.104 2.81763 15.104 2.97093 15.1137 3.12383C15.1137 3.49493 15.1918 3.82703 15.2211 4.15913C15.1779 4.31323 15.2708 4.47253 15.4263 4.51073Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			d="M23.8262 7.5874C23.3671 7.5874 22.9374 7.5191 22.4881 7.5191H21.9118C21.7262 7.5191 21.5309 7.5874 21.3453 7.6363C20.9091 7.7829 20.4874 7.9692 20.0853 8.193C19.877 8.2237 19.7801 8.4684 19.9109 8.6334C19.9949 8.7394 20.1437 8.7681 20.2611 8.7009C20.7006 8.6326 21.1109 8.6326 21.5309 8.6033L22.0583 8.5447L22.5955 8.447C23.0155 8.3591 23.4258 8.2516 23.8653 8.1637C24.0317 8.1435 24.1548 7.9992 24.1485 7.8316C24.1192 7.6799 23.9802 7.5746 23.8262 7.5874Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			d="M13.8635 20.8223C13.5382 20.8426 13.212 20.8426 12.8867 20.8223C12.5742 20.8223 12.2616 20.7734 11.91 20.7539L10.9333 20.7051C10.2886 20.7051 9.65372 20.7051 8.97982 20.6367C8.83452 20.6062 8.69142 20.6973 8.65752 20.8418C8.62702 20.9871 8.71812 21.1302 8.86262 21.1641C9.41012 21.3819 9.97552 21.5519 10.5523 21.672C10.8039 21.7174 11.0586 21.7435 11.3142 21.7502C11.5678 21.7697 11.8225 21.7697 12.0761 21.7502C12.7063 21.6987 13.3307 21.5908 13.9416 21.4278C14.1071 21.398 14.2193 21.2426 14.1956 21.0762C14.1705 20.9165 14.0242 20.8047 13.8635 20.8223Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			d="M12.8086 22.7953C12.4815 22.7857 12.1554 22.7564 11.8319 22.7074C11.6172 22.6928 11.4018 22.6928 11.1872 22.7074C10.7965 22.7074 10.4351 22.805 10.0542 22.8441C9.90833 22.827 9.77653 22.9324 9.76123 23.0785C9.75423 23.2267 9.85903 23.3568 10.0053 23.3813C10.3345 23.5073 10.6703 23.6149 11.0114 23.7036C11.1567 23.7296 11.3035 23.7459 11.4509 23.7525C11.6005 23.7625 11.7506 23.7625 11.9002 23.7525C12.2699 23.6811 12.6281 23.5595 12.9649 23.3911C13.1942 23.3422 13.2846 23.0634 13.1276 22.8893C13.0548 22.8084 12.9443 22.7726 12.8379 22.7953H12.8086Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
	</g>
	<defs>
		<clipPath id="clip0_3013_12622">
			<rect width="24" height="24" fill="white" transform="translate(0.966309)" />
		</clipPath>
	</defs>
</svg>
