<script lang="ts">
	import type { IconAssetProps } from '$lib/assets/types';

	const { size, ...props }: IconAssetProps = $props();
</script>

<svg
	width={size ?? 24}
	height={size ?? 24}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<g clip-path="url(#clip0_3013_20986)">
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M23.2272 0.679988C22.6942 0.277663 22.0333 0.0822536 21.3672 0.129988C21.1254 0.156713 20.8893 0.22081 20.6672 0.319988C20.3726 0.484389 20.1003 0.686047 19.8572 0.919988C19.8242 0.95171 19.798 0.98976 19.7801 1.03186C19.7622 1.07396 19.7529 1.11924 19.7529 1.16499C19.7529 1.21074 19.7622 1.25602 19.7801 1.29812C19.798 1.34022 19.8242 1.37827 19.8572 1.40999C19.888 1.44267 19.9252 1.46872 19.9665 1.48651C20.0078 1.50431 20.0522 1.51349 20.0972 1.51349C20.1421 1.51349 20.1866 1.50431 20.2279 1.48651C20.2691 1.46872 20.3063 1.44267 20.3372 1.40999C20.5019 1.25852 20.6792 1.12136 20.8672 0.999988C21.0461 0.902356 21.2439 0.844377 21.4472 0.829988C21.9145 0.829075 22.3674 0.991826 22.7272 1.28999C22.9094 1.40523 23.0627 1.56071 23.1753 1.74447C23.288 1.92823 23.3571 2.13537 23.3772 2.34999C23.3808 2.69683 23.302 3.03957 23.1472 3.34999C22.9858 3.66726 22.7805 3.96016 22.5372 4.21999L21.3272 5.57999C21.0272 5.25999 20.7172 4.93999 20.3872 4.63999C20.2272 4.48999 20.0572 4.35999 19.8972 4.21999L19.5672 3.99999L18.7272 3.50999L18.5672 3.38999L18.8372 2.99999L19.5172 2.14999C19.5411 2.11728 19.558 2.0799 19.5666 2.04029C19.5752 2.00067 19.5754 1.95969 19.5672 1.91999C19.5672 1.76999 19.4172 1.53999 19.1172 1.71999C18.8272 1.49999 18.5772 1.23999 18.2972 1.01999C18.127 0.892247 17.9424 0.784851 17.7472 0.699988C17.5888 0.633975 17.4188 0.599988 17.2472 0.599988C17.0756 0.599988 16.9056 0.633975 16.7472 0.699988C16.4401 0.850855 16.1571 1.0463 15.9072 1.27999C15.5774 1.57308 15.2667 1.88706 14.9772 2.21999C14.577 2.72479 14.2064 3.25234 13.8672 3.79999C13.8031 3.86541 13.7671 3.95337 13.7671 4.04499C13.7671 4.1366 13.8031 4.22456 13.8672 4.28999C13.9309 4.3535 14.0172 4.38916 14.1072 4.38916C14.1972 4.38916 14.2835 4.3535 14.3472 4.28999C14.6472 3.97999 14.9572 3.69999 15.2572 3.41999C15.7045 2.99716 16.1752 2.59986 16.6672 2.22999C16.8072 2.10999 16.9772 1.95999 17.1572 1.82999C17.3372 1.69999 17.3172 1.66999 17.4272 1.68999C17.5572 1.7092 17.6846 1.74273 17.8072 1.78999C18.0872 1.91999 18.3572 2.08999 18.6372 2.22999L18.2872 2.63999C16.8972 4.26999 13.8472 7.83999 12.5972 9.45999C12.3138 9.78665 12.0934 10.163 11.9472 10.57C11.9472 10.85 11.8172 12.86 11.8372 13.68C11.8254 13.7796 11.8254 13.8803 11.8372 13.98C11.6838 14.0735 11.5369 14.1771 11.3972 14.29C11.3097 14.3658 11.2294 14.4495 11.1572 14.54C11.0882 14.6293 11.0247 14.7228 10.9672 14.82C10.4172 15.72 10.5372 15.82 10.5472 15.82C10.5611 15.8965 10.6041 15.9646 10.6672 16.01C10.7247 16.0633 10.7989 16.0951 10.8772 16.1C10.8772 16.1 11.0272 16.19 11.8172 15.49C11.9069 15.4203 11.9875 15.3397 12.0572 15.25C12.1334 15.1675 12.2004 15.0769 12.2572 14.98C12.3342 14.8486 12.401 14.7115 12.4572 14.57C12.6787 14.6108 12.9057 14.6108 13.1272 14.57C13.9911 14.3698 14.8398 14.1091 15.6672 13.79C15.8322 13.7257 15.9771 13.6187 16.0872 13.48C16.3372 13.21 16.8272 12.64 17.4372 11.89C19.1972 9.72999 22.0072 6.11999 23.1372 4.80999C23.4282 4.47935 23.6706 4.10897 23.8572 3.70999C24.0782 3.2962 24.2045 2.83853 24.2272 2.36999C24.1956 2.03195 24.0895 1.7051 23.9166 1.4129C23.7437 1.12071 23.5083 0.870413 23.2272 0.679988ZM15.4672 12.53L15.2772 12.73C14.5463 13.0328 13.79 13.2704 13.0172 13.44V10.71C13.1072 10.71 12.3972 11.2 18.1072 3.91999L18.2372 4.04999L18.7772 4.69999C19.0387 5.01794 19.3376 5.30332 19.6672 5.54999C20.0256 5.78837 20.3998 6.0022 20.7872 6.18999C19.9372 7.18999 18.9572 8.32999 18.0572 9.40999C16.9572 10.76 15.9572 12 15.4672 12.53Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M18.8572 17.53L18.1572 15.93C18.1489 15.8863 18.1313 15.8449 18.1056 15.8086C18.0799 15.7722 18.0467 15.7419 18.0082 15.7196C17.9697 15.6972 17.9269 15.6835 17.8826 15.6792C17.8383 15.6749 17.7936 15.6801 17.7516 15.6946C17.7095 15.7091 17.6711 15.7325 17.6389 15.7632C17.6066 15.7939 17.5814 15.8311 17.5649 15.8724C17.5483 15.9137 17.5409 15.9581 17.543 16.0026C17.5451 16.047 17.5568 16.0905 17.5772 16.13L18.2672 17.83C18.8072 18.91 19.2672 19.46 19.4872 19.9C19.6285 20.119 19.6788 20.3845 19.6272 20.64C19.6272 20.69 19.5272 20.7 19.4272 20.76C19.0783 20.9452 18.7092 21.0895 18.3272 21.19C15.9994 21.8013 13.6244 22.2161 11.2272 22.43C8.85635 22.7299 6.46286 22.8102 4.07722 22.67C3.73968 22.6416 3.40515 22.5848 3.07722 22.5C2.98067 22.4639 2.88706 22.4205 2.79722 22.37C2.68002 22.1602 2.57647 21.9431 2.48722 21.72C2.39021 21.5121 2.31643 21.2941 2.26722 21.07C1.78293 18.3091 1.47564 15.5201 1.34722 12.72C1.14001 10.0306 1.14001 7.32933 1.34722 4.63999C2.27722 4.41999 3.53722 4.20999 4.94722 4.02999C6.83722 3.78999 8.94722 3.61999 10.9472 3.54999C10.9927 3.55001 11.0378 3.54089 11.0797 3.52318C11.1216 3.50547 11.1595 3.47953 11.1912 3.44689C11.2229 3.41425 11.2478 3.37558 11.2643 3.33317C11.2807 3.29076 11.2886 3.24547 11.2872 3.19999C11.2846 3.1089 11.2466 3.02243 11.1812 2.95894C11.1159 2.89545 11.0283 2.85995 10.9372 2.85999C8.93722 2.85999 6.80722 3.01999 4.88722 3.20999C3.54321 3.33581 2.20763 3.53949 0.887216 3.81999C0.800767 3.84204 0.722598 3.88876 0.662231 3.95446C0.601863 4.02015 0.561902 4.10198 0.547216 4.18999C0.210242 7.04042 0.133258 9.9156 0.317216 12.78C0.425601 15.6374 0.729646 18.4841 1.22722 21.3C1.28636 21.5441 1.35982 21.7845 1.44722 22.02C1.58239 22.3419 1.73598 22.6557 1.90722 22.96C2.12462 23.2097 2.39155 23.4116 2.69108 23.5528C2.99061 23.6939 3.31621 23.7713 3.64722 23.78C5.62617 23.9491 7.61756 23.9055 9.58722 23.65C12.9049 23.3714 16.1778 22.7 19.3372 21.65C20.0172 21.36 20.3372 21.01 20.4072 20.74C20.4674 20.4526 20.4431 20.1539 20.3372 19.88C20.0972 19.39 19.5672 18.88 18.8572 17.53Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M9.89719 11.63C9.86704 11.5342 9.80196 11.4532 9.71489 11.4032C9.62782 11.3532 9.52512 11.3378 9.42719 11.36C9.34719 11.36 9.42719 11.28 9.07719 11.36C8.30719 11.45 7.07719 11.66 6.01719 11.81C5.69719 11.86 5.38719 11.9 5.10719 11.96C4.45719 12.08 3.96719 12.22 3.77719 12.24C3.71323 12.2522 3.65554 12.2864 3.61405 12.3366C3.57256 12.3868 3.54987 12.4498 3.54987 12.515C3.54987 12.5801 3.57256 12.6432 3.61405 12.6934C3.65554 12.7435 3.71323 12.7777 3.77719 12.79C4.27643 12.8691 4.78186 12.9026 5.28719 12.89C5.73719 12.89 6.28719 12.83 6.75719 12.78C7.54462 12.7043 8.32629 12.5774 9.09719 12.4C9.82719 12.16 9.95719 11.85 9.89719 11.63Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M5.67724 7.77C4.99968 7.91946 4.33179 8.10981 3.67724 8.34C3.60532 8.35893 3.54341 8.40478 3.50433 8.46805C3.46525 8.53133 3.45197 8.60721 3.46724 8.68C3.48606 8.75145 3.53236 8.81257 3.59604 8.85004C3.65973 8.8875 3.73565 8.89827 3.80724 8.88C4.69724 8.74 5.55724 8.71 6.45724 8.64C7.69724 8.53 8.98724 8.4 10.4572 8.21C10.8372 8.16 11.2372 8.13 11.6372 8.04C11.7651 8.04422 11.893 8.03076 12.0172 8C12.0772 8 12.0972 7.93 12.1572 7.89C12.2547 7.87283 12.3417 7.81829 12.3996 7.73798C12.4575 7.65767 12.4818 7.55793 12.4672 7.46C12.4072 7 11.6072 7 11.5272 7C9.81724 7.12 8.36724 7.33 7.01724 7.51C6.55724 7.61 6.11724 7.67 5.67724 7.77Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M9.0372 15.84C8.9072 15.84 8.7472 15.84 8.5672 15.89L7.2672 16.09C6.5872 16.2 5.8672 16.31 5.3172 16.45C5.01236 16.527 4.71192 16.6204 4.4172 16.73H4.3572C4.30828 16.7152 4.25611 16.7152 4.2072 16.73C4.2072 16.73 4.0772 17.22 4.4872 17.26C4.6572 17.26 4.9872 17.32 5.4872 17.32C6.15638 17.2984 6.82397 17.2416 7.4872 17.15C7.9772 17.09 8.4872 17.02 8.8172 16.95C8.95949 16.9271 9.09988 16.8937 9.2372 16.85C9.40823 16.7788 9.56687 16.6809 9.7072 16.56C9.79881 16.511 9.86777 16.4283 9.8995 16.3294C9.93123 16.2305 9.92323 16.1231 9.8772 16.03C9.7872 16 9.7472 15.86 9.0372 15.84Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
	</g>
	<defs>
		<clipPath id="clip0_3013_20986">
			<rect width="24" height="24" fill="white" transform="translate(0.227234)" />
		</clipPath>
	</defs>
</svg>
