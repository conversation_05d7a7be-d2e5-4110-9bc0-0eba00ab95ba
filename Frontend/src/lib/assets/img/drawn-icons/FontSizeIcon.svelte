<script lang="ts">
	import type { IconAssetProps } from '$lib/assets/types';

	const { size, ...props }: IconAssetProps = $props();
</script>

<svg
	width={size ?? 24}
	height={size ?? 24}
	viewBox="0 0 25 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<g clip-path="url(#clip0_3013_20319)">
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M13.4572 14.7C12.7872 12.43 12.3172 9.86999 11.5872 7.48999C11.2816 6.43971 10.8903 5.41628 10.4172 4.42999C10.0945 3.76567 9.69514 3.14141 9.22723 2.56999C8.86285 2.10394 8.3688 1.75603 7.80723 1.56999C7.50652 1.48325 7.18825 1.47733 6.88452 1.55282C6.58079 1.62831 6.30233 1.78256 6.07723 1.99999C5.43136 2.82962 4.96176 3.78243 4.69723 4.79999C3.88532 6.9255 3.18438 9.09176 2.59723 11.29C1.98723 13.5 1.47723 15.75 1.03723 17.89L0.227227 22.08C0.21231 22.125 0.20697 22.1726 0.211552 22.2198C0.216134 22.267 0.230536 22.3127 0.253832 22.354C0.277129 22.3953 0.308805 22.4313 0.346827 22.4596C0.384848 22.4879 0.428375 22.508 0.47461 22.5185C0.520845 22.529 0.568767 22.5297 0.615297 22.5206C0.661828 22.5115 0.705938 22.4928 0.744795 22.4656C0.783652 22.4384 0.816397 22.4034 0.840926 22.3629C0.865456 22.3223 0.881227 22.277 0.887227 22.23L1.88723 18.08C2.19723 16.83 2.54723 15.54 2.88723 14.25H3.24723H4.82723L9.54723 13.74L11.1172 13.63H12.1172C12.2472 14.09 12.3772 14.55 12.5272 14.99C12.7172 15.59 12.9772 16.54 13.2172 17.55C13.5772 19 13.9272 20.55 14.1172 21.47C14.1267 21.5204 14.1463 21.5684 14.1748 21.6111C14.2033 21.6538 14.2402 21.6902 14.2831 21.7183C14.3261 21.7463 14.3743 21.7653 14.4249 21.7742C14.4754 21.7831 14.5273 21.7817 14.5772 21.77C14.6757 21.7483 14.762 21.6893 14.8179 21.6054C14.8738 21.5215 14.8951 21.4192 14.8772 21.32C14.7072 20.43 14.3972 18.82 14.0872 17.32C13.8672 16.3 13.6372 15.32 13.4572 14.7ZM11.1072 12.77L9.53723 12.67C8.87117 12.6296 8.20328 12.6296 7.53723 12.67C6.95723 12.67 6.36723 12.79 5.79723 12.88C5.40723 12.94 5.03723 13.03 4.65723 13.1L3.11723 13.38C3.28723 12.79 3.45723 12.19 3.63723 11.6C4.30723 9.42999 5.04723 7.27999 5.87723 5.27999C6.08894 4.54533 6.3842 3.83737 6.75723 3.16999C6.82412 3.04335 6.93096 2.94234 7.06116 2.88267C7.19136 2.82299 7.33762 2.80799 7.47723 2.83999C7.78008 2.95311 8.04157 3.15533 8.22723 3.41999C8.63813 3.90355 8.99056 4.43388 9.27723 4.99999C9.74158 5.92119 10.1328 6.87747 10.4472 7.85999C10.9872 9.45999 11.4472 11.16 11.8672 12.78L11.1072 12.77Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M23.8772 19.5C23.6572 18.31 23.4272 17.34 23.2672 16.57C23.0335 15.5456 22.7157 14.5422 22.3172 13.57C21.9472 12.63 21.5072 11.65 21.0572 10.74C20.7272 10.07 20.3872 9.44 20.0572 8.9C19.7205 8.19082 19.26 7.5474 18.6972 7C18.5633 6.91044 18.4097 6.85458 18.2495 6.83717C18.0893 6.81976 17.9273 6.84131 17.7772 6.9C17.4622 7.01192 17.1803 7.20101 16.9572 7.45C16.6798 7.75471 16.4382 8.09023 16.2372 8.45C15.8172 9.2 15.5172 10.03 15.0272 10.75C15.001 10.7861 14.9821 10.827 14.9716 10.8705C14.9612 10.9139 14.9594 10.9589 14.9663 11.003C14.9733 11.0471 14.9889 11.0894 15.0122 11.1275C15.0355 11.1656 15.0661 11.1987 15.1022 11.225C15.1383 11.2513 15.1793 11.2702 15.2227 11.2806C15.2661 11.291 15.3112 11.2928 15.3553 11.2859C15.3994 11.2789 15.4417 11.2633 15.4798 11.24C15.5178 11.2167 15.551 11.1861 15.5772 11.15C16.1272 10.46 16.4872 9.64 16.9872 8.92C17.171 8.6314 17.393 8.36901 17.6472 8.14C17.7343 8.0557 17.8359 7.98793 17.9472 7.94C17.9972 7.94 18.0572 7.87 18.1072 7.94C18.4661 8.41474 18.7651 8.93199 18.9972 9.48C19.3972 10.26 19.8572 11.21 20.3172 12.2C20.9792 13.5454 21.5635 14.9277 22.0672 16.34H22.0172C21.9275 16.3305 21.837 16.3305 21.7472 16.34C21.2872 16.27 20.1572 16.18 19.9472 16.17H18.3972C17.5772 16.17 16.7872 16.34 15.9972 16.44C15.9071 16.44 15.8206 16.4758 15.7568 16.5396C15.693 16.6033 15.6572 16.6898 15.6572 16.78C15.6572 16.8702 15.693 16.9566 15.7568 17.0204C15.8206 17.0842 15.9071 17.12 15.9972 17.12C16.7872 17.22 17.5772 17.41 18.3872 17.45C18.9318 17.4633 19.4766 17.4365 20.0172 17.37C20.2172 17.37 21.2772 17.18 21.7172 17.15H21.7872C21.8668 17.1772 21.9532 17.1773 22.0328 17.1501C22.1125 17.123 22.1808 17.0702 22.2272 17C22.2301 16.9801 22.2301 16.9599 22.2272 16.94C22.4372 17.68 22.7172 18.6 23.0072 19.74C23.1672 20.32 23.3172 20.97 23.4672 21.67C23.4744 21.7199 23.4917 21.7678 23.518 21.8108C23.5443 21.8537 23.5792 21.8909 23.6204 21.9199C23.6616 21.9489 23.7083 21.9692 23.7576 21.9796C23.8069 21.9899 23.8578 21.99 23.9072 21.98C24.0076 21.9606 24.0962 21.9025 24.1542 21.8184C24.2122 21.7342 24.2348 21.6307 24.2172 21.53C24.1172 20.77 23.9972 20.11 23.8772 19.5Z"
			fill={props.color ? 'currentColor' : '#0C6FFF'}
		/>
	</g>
	<defs>
		<clipPath id="clip0_3013_20319">
			<rect width="24" height="24" fill="white" transform="translate(0.227234)" />
		</clipPath>
	</defs>
</svg>
