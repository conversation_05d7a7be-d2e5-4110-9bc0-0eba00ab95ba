<script lang="ts">
	import type { IconAssetProps } from '$lib/assets/types';

	const { size, ...props }: IconAssetProps = $props();
</script>

<svg
	width={size ?? 24}
	height={size ?? 24}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<g clip-path="url(#loop_arrow_clip_3013_20588)">
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M22.9931 9.77709C22.2237 8.64199 20.9944 7.90229 19.6312 7.75409C18.3115 7.63749 16.9886 7.93829 15.8491 8.61409C14.865 9.19529 13.9389 9.86949 13.0834 10.6273C11.1288 12.2985 9.45755 14.2433 7.34665 15.6897C6.75745 16.058 6.08556 16.273 5.39206 16.3152C4.71416 16.3919 4.02796 16.2944 3.39836 16.0317C1.73696 15.201 1.31676 13.3149 1.63926 11.5558C1.96176 9.79669 3.16386 7.92999 4.80566 7.64659C5.45055 7.52889 6.10946 7.50909 6.76025 7.58799C7.38465 7.63629 7.98975 7.82679 8.52915 8.14499C8.79615 8.35139 9.03875 8.58739 9.25235 8.84869C9.48705 9.08939 9.69065 9.35859 9.85825 9.64999C9.33085 9.62179 8.80546 9.56309 8.28485 9.47409C7.73505 9.38599 7.19516 9.24529 6.67226 9.05389C6.50745 8.96669 6.30315 9.02749 6.21295 9.19069C6.13465 9.34579 6.18515 9.53499 6.33026 9.63049C7.27015 10.1977 8.29205 10.6164 9.35985 10.8717C9.94555 11.0445 10.5727 11.0135 11.1386 10.7837C11.3316 10.6443 11.4671 10.4393 11.5197 10.2071C11.543 9.97309 11.543 9.73739 11.5197 9.50339C11.5197 8.96589 11.4513 7.37299 11.4317 6.68889C11.4317 6.45429 11.4806 6.01449 11.0897 6.08289C10.8942 6.01449 10.6988 6.08289 10.6499 6.37609V6.65949C10.5717 7.27519 10.4154 8.61409 10.3372 9.28839C10.1584 8.96139 9.94185 8.65629 9.69215 8.37959C9.44115 8.07169 9.15956 7.79009 8.85166 7.53909C8.22945 7.14749 7.52885 6.89729 6.79936 6.80609C6.08306 6.70309 5.35575 6.70309 4.63955 6.80609C2.65565 7.09929 1.10176 9.13209 0.622855 11.3603C0.143955 13.5885 0.740155 15.9536 2.83156 17.0677C3.65946 17.4599 4.57765 17.6219 5.48975 17.5368C6.39825 17.496 7.28065 17.2199 8.05025 16.7354C10.1018 15.1356 12.0596 13.4193 13.9141 11.5949C14.7121 10.884 15.5723 10.2462 16.4843 9.68909C17.3878 9.13019 18.4445 8.87029 19.5042 8.94639C20.5404 9.03929 21.4836 9.58239 22.0842 10.4319C22.9287 11.6417 23.1086 13.195 22.5631 14.5658C21.9838 15.9461 20.6834 16.8884 19.1914 17.009C18.219 17.0633 17.2476 16.8858 16.3573 16.4911C15.4507 16.2043 14.6905 15.5773 14.2366 14.7417C14.1593 14.5489 13.9446 14.4501 13.7479 14.5169C13.5427 14.5807 13.438 14.8083 13.5231 15.0056C13.9149 15.8734 14.6099 16.5684 15.4777 16.9602C16.6641 17.6443 18.0182 17.9828 19.3869 17.9375C21.2252 17.8283 22.8461 16.6948 23.5794 15.0056C24.2858 13.2764 24.0649 11.3068 22.9931 9.77709Z"
			fill={props.color ? 'currentColor' : '#020202'}
		/>
	</g>
	<defs>
		<clipPath id="loop_arrow_clip_3013_20588">
			<rect width="24" height="24" fill="white" transform="translate(0.227234)" />
		</clipPath>
	</defs>
</svg>
