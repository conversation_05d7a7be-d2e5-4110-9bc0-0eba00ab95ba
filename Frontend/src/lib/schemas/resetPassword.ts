import { z } from 'zod';

export const ResetPasswordSchema = z
	.object({
		newPassword: z
			.string({ message: 'global.errors.validation.required' })
			.min(8, { message: 'global.errors.validation.password_min' })
			.default(''),
		newPasswordConfirm: z
			.string()
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),
		v: z.string(),
		rpt: z.string()
	})
	.refine((data) => data.newPassword === data.newPasswordConfirm, {
		message: 'global.errors.validation.password_mismatch',
		path: ['newPasswordConfirm']
	});

export type ResetPasswordValues = z.infer<typeof ResetPasswordSchema>;
