import { z } from 'zod/v4';

export const RegisterSchema = z
	.object({
		firstName: z
			.string({ message: 'global.errors.validation.required' })
			.trim()
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),
		lastName: z
			.string({ message: 'global.errors.validation.required' })
			.trim()
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),

		email: z.email({ message: 'global.errors.validation.string_email' }).default(''),

		emailStudent: z.email({ message: 'global.errors.validation.string_email' }),
		originCity: z.string().trim().optional(),
		schoolType: z.enum(['cg', 'ss', 'og', 'other']).optional().or(z.literal('')),

		password: z
			.string({ message: 'global.errors.validation.required' })
			.min(8, { message: 'global.errors.validation.password_min' })
			.default(''),

		passwordConfirm: z
			.string({ message: 'global.errors.validation.required' })
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),

		consent: z
			.boolean()
			.refine((consent) => consent, {
				message: 'global.errors.validation.required'
			})
			.default(false)
	})
	.refine((data) => data.password === data.passwordConfirm, {
		path: ['passwordConfirm'],
		message: 'global.errors.validation.password_mismatch'
	});

export type RegisterInput = z.input<typeof RegisterSchema>;
export type RegisterValues = z.output<typeof RegisterSchema>;
