<script lang="ts">
	import '$lib/assets/styles/global.scss';

	import { QueryClient, QueryClientProvider } from '@tanstack/svelte-query';
	import { onMount } from 'svelte';

	import { browser } from '$app/environment';
	import { afterNavigate, goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import { setTranslationsContext } from '$lib/appmaxx/translations';
	import faviconSvg from '$lib/assets/img/logos/logo.svg';
	import { BackgroundPattern, Footer, Header } from '$lib/components';
	import { t, translations } from '$lib/translations/config';

	let { children, data } = $props();
	setTranslationsContext(translations);

	let initialized = $state(false);
	let splashPlayed = $state(false);

	const queryClient = new QueryClient();

	const doRefresh = async () => {
		if (data.refreshPresent && !data.user) {
			const res = await fetch('/api/auth/refresh', {
				method: 'POST'
			});
			if (res.ok) {
				await invalidate('app:user');
			} else {
				if (page.url.searchParams.has('next')) return;

				const next = encodeURIComponent(page.url.pathname + page.url.search);
				await goto(`/prihlaseni?next=${next}`, { replaceState: true });
			}
		}
	};

	onMount(async () => {
		if (!browser) return;

		//await doRefresh();

		initialized = true;
		setTimeout(() => {
			splashPlayed = true;
		}, 300);
	});

	afterNavigate(async () => {
		await invalidate('app:user');
		await doRefresh();
	});
</script>

<svelte:head>
	<meta name="keywords" content={$t('meta.keywords')} />
</svelte:head>

<div class="splash" class:initialized class:played={splashPlayed}>
	<img src={faviconSvg} alt="Logo" />
</div>

<div class="content">
	<Header />

	<QueryClientProvider client={queryClient}>
		{@render children?.()}
	</QueryClientProvider>

	<BackgroundPattern />
</div>

<Footer />
<noscript lang="scss">
	<!--
    <div class="no-js">
        <Flex>
            <div>
                <Text weight="bold">Javascript je vypnutý</Text>
                <Text>Abychom vám mohli ukázat obsah a funkce našeho webu, potřebujeme, abyste si
                    zapnuli JavaScript ve svém prohlížeči.
                </Text>
            </div>
        </Flex>
    </div>
  -->

	<style lang="scss">
		.no-js {
			bottom: var(--spacing-l);
			left: var(--spacing-l);
			right: var(--spacing-l);
			position: fixed;
			z-index: 1000;

			max-width: 30rem;
			padding: var(--spacing-m) var(--spacing-l);
			background-color: var(--color-error-hover);
			color: var(--color-light);
			border-radius: var(--border-radius-s);
		}
		.splash {
			display: none !important;
		}
	</style>
</noscript>

<style lang="scss">
	.content {
		position: relative;
		min-height: 100dvh;
		display: flex;
		flex-direction: column;
	}

	.splash {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: var(--color-light);
		display: flex;
		align-items: center;
		justify-content: center;
		pointer-events: none;
		z-index: 1000;
		transition: opacity 0.3s ease;

		img {
			width: 100px;
			transition:
				scale 0.3s ease,
				opacity 0.3s ease,
				translate 0.3s ease;
			opacity: 0;
			scale: 1;
			translate: 0 -20px;
		}

		&.initialized {
			img {
				opacity: 1;
				translate: 0 0;
			}
		}

		&.played {
			opacity: 0;

			img {
				scale: 1.5;
			}
		}
	}
</style>
