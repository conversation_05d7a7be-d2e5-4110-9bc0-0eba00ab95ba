import { getLocale } from '$lib/appmaxx/translations';
import { loadTranslations } from '$lib/translations/config';

import type { LayoutServerLoad } from './$types';
// TODO: cleanup
// import { buildAbilitiesFromRules } from '$lib/permissions';

export const load: LayoutServerLoad = async ({ locals, url, request, depends }) => {
	depends('app:user');

	const locale = getLocale(url, request);
	await loadTranslations(locale);

	return {
		locale,
		user: locals.user ?? null,
		refreshPresent: locals.refreshPresent
	};
};
