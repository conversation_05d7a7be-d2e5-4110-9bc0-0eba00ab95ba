<script lang="ts">
	import { MetaSeo } from '$lib/components';
	import Role from '$lib/permissions/Role.svelte';
	import { t } from '$lib/translations/config';

	import ManagementDashboard from './ManagementDashboard.svelte';
	import StudentDashboard from './StudentDashboard.svelte';
</script>

<MetaSeo title={$t('dashboard.pageTitle')} />

<Role role={['admin', 'manager', 'teacher']}>
	<ManagementDashboard />
</Role>

<Role role="student">
	<StudentDashboard />
</Role>
