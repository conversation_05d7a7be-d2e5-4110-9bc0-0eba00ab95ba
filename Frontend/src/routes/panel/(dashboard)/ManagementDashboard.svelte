<script lang="ts">
	import { ArrowLeft } from '@lucide/svelte';

	import { Button, Flex, Heading, Spacer, Text } from '$lib/components';
	import { T, t } from '$lib/translations/config';
</script>

<Flex direction="row" align="center" flex="1">
	<Flex direction="col" align="center" flex="1">
		<Heading as="h1" size="hero" align="center">
			<T key="panel.hero" />
		</Heading>
		<Spacer direction="vertical" size="xl" />

		<Text style="max-width: 120ch" align="center">
			<T key="panel.wip" />
		</Text>
		<Spacer direction="vertical" size="m" />

		<Text style="max-width: 120ch" align="center">
			<T key="panel.expectedLaunch" />
		</Text>
		<Spacer direction="vertical" size="xl" />

		<Button as="a" href="/" Icon={ArrowLeft}>
			{$t('panel.homeButton')}
		</Button>
	</Flex>
</Flex>
