<script lang="ts">
	import { CirclePlus } from '@lucide/svelte';

	import {
		Button,
		Flex,
		Heading,
		MetaSeo,
		NavTab,
		PageTransition,
		Spacer
	} from '$lib/components';
	import HorizontalTabNav from '$lib/components/panel/HorizontalTabNav/HorizontalTabNav.svelte';
	import { t } from '$lib/translations/config';

	let { children } = $props();
</script>

<MetaSeo title={$t('locations.pageTitle')} />

<Flex direction="row" align="center">
	<Heading as="h1" size="2">{$t('locations.title')}</Heading>

	<Spacer direction="horizontal" size="m" />
	<Spacer grow="1" />

	<HorizontalTabNav>
		<NavTab href="/panel/lokace" label={$t('locations.navTabs.list')} />
		<NavTab href="/panel/lokace/mapa" label={$t('locations.navTabs.map')} />
	</HorizontalTabNav>

	<Spacer direction="horizontal" size="m" />
	<Button disabled variant="success" Icon={CirclePlus} size="medium">
		{$t('locations.addButton')}
	</Button>
</Flex>

<Spacer direction="vertical" size="l" />

<PageTransition key="panel.locations">
	{@render children()}
</PageTransition>
