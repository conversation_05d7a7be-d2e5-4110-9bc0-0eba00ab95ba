<script lang="ts">
	import { exists } from '$lib/appmaxx/util';
	import { DataTable, Loader, Text } from '$lib/components';
	import DateCell from '$lib/components/panel/DataTable/cells/renderers/DateCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import {
		createCellRenderer,
		createManagementColumn,
		type DataTableColumnDefinitions
	} from '$lib/components/panel/DataTable/DataTable';
	import { type Location, locationService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	const locationsQuery = locationService.listLocations();

	// Color constants for purpose pills
	const PURPOSE_COLORS = {
		prijimacky_nanecisto: '#427ef4',
		prezencni_doucovani: '#2ecc71'
	};

	let columns = $state<DataTableColumnDefinitions<Location>[]>([
		createManagementColumn(),
		{
			accessorKey: 'name',
			header: $t('locations.dataGrid.name')
		},
		{
			accessorKey: 'address',
			header: $t('locations.dataGrid.address')
		},
		{
			accessorKey: 'description',
			header: $t('locations.dataGrid.description')
		},
		{
			accessorKey: 'capacity',
			header: $t('locations.dataGrid.capacity')
		},
		{
			accessorKey: 'purpose',
			header: $t('locations.dataGrid.purpose'),
			cell: ({ getValue }) => {
				const purpose = getValue() as Location['purpose'];
				return createCellRenderer(PillCell, {
					hexColor: PURPOSE_COLORS[purpose],
					label: $t(`locations.purpose.${purpose}`)
				});
			}
		},
		{
			accessorKey: 'imageUrl',
			header: $t('locations.dataGrid.imageUrl'),
			cell: ({ getValue }) => {
				const hasImage = exists(getValue());
				return createCellRenderer(PillCell, {
					hexColor: hasImage ? '#2ecc71' : '#868686',
					label: hasImage
						? $t('locations.common.hasImage')
						: $t('locations.common.noImage')
				});
			}
		},
		{
			accessorKey: 'createdAt',
			header: $t('locations.dataGrid.createdAt'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		},
		{
			accessorKey: 'updatedAt',
			header: $t('locations.dataGrid.updatedAt'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		}
	]);
</script>

{#if $locationsQuery.isPending}
	<Loader fillPage />
{:else if $locationsQuery.error}
	<Text>
		<T key="locations.errors.loadingLocations" />
		: {$locationsQuery.error.message}
	</Text>
{:else if $locationsQuery.data}
	<DataTable data={$locationsQuery.data} {columns} />
{/if}
