<script lang="ts">
	import { Flex, Heading, LocationsMap, MetaSeo, Spacer } from '$lib/components';
	import { t } from '$lib/translations/config';
</script>

<MetaSeo title={$t('locations.map.pageTitle')} />

<Heading as="h2" size="3">{$t('locations.map.title')}</Heading>
<Spacer direction="vertical" size="l" />

<Flex class="panel-locations-map-container" direction="col" flex="1">
	<LocationsMap withoutSearch withoutPageOverflow />
</Flex>

<style lang="scss">
	:global {
		.panel-locations-map-container .locations-map-card {
			flex: 1;

			.map {
				height: 100%;
			}
		}
	}
</style>
