<script lang="ts">
	import { exists } from '$lib/appmaxx/util';
	import { DataTable, Loader, MetaSeo, Text } from '$lib/components';
	import DateCell from '$lib/components/panel/DataTable/cells/renderers/DateCell.svelte';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import LanguageCell from '$lib/components/panel/DataTable/cells/renderers/LanguageCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import {
		createCellRenderer,
		createManagementColumn,
		type DataTableColumnDefinitions
	} from '$lib/components/panel/DataTable/DataTable';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import { getUserRoleColor, getUserRoleTranslationKey, type UserRole } from '$lib/permissions';
	import type { Admin } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	import { ERR_PILL_COLOR, OK_PILL_COLOR } from '../constants';

	const adminsQuery = userService.listAdmins();

	let columns = $state<DataTableColumnDefinitions<Admin>[]>([
		createManagementColumn(),
		{
			accessorFn: ({ firstName, lastName }) => `${firstName} ${lastName}`,
			header: $t('users.dataGrid.name')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			cell: ({ getValue }) => createCellRenderer(EmailCell, { email: getValue() as string })
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getUserRoleColor(getValue() as UserRole),
					label: $t(getUserRoleTranslationKey(getValue() as UserRole))
				})
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language'),
			cell: ({ getValue }) =>
				createCellRenderer(LanguageCell, { language: getValue() as string })
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? OK_PILL_COLOR : ERR_PILL_COLOR,
					label: getValue() ? $t('users.emailVerified.yes') : $t('users.emailVerified.no')
				})
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		}
	]);
</script>

<MetaSeo title={$t('users.admins.pageTitle')} />

<UsersPageHeading title={$t('users.admins.title')} queryKey={['users', 'admins']} />

{#if $adminsQuery.isPending}
	<Loader fillPage />
{:else if $adminsQuery.error}
	<Text>
		<T key="users.errors.loadingAdmins" />
		: {$adminsQuery.error.message}</Text
	>
{:else if $adminsQuery.data}
	<DataTable data={$adminsQuery.data} {columns} />
{/if}
