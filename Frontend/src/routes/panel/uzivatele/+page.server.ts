import { redirect } from '@sveltejs/kit';

import { buildAbilitiesFromRules, can } from '$lib/permissions';

import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	const abilities = buildAbilitiesFromRules(locals.user?.rules ?? []);

	if (can(abilities, 'Student', 'read')) {
		throw redirect(302, '/panel/uzivatele/studenti');
	}

	if (can(abilities, 'Teacher', 'read')) {
		throw redirect(302, '/panel/uzivatele/lektori');
	}

	if (can(abilities, 'Manager', 'read')) {
		throw redirect(302, '/panel/uzivatele/manageri');
	}

	if (can(abilities, 'Admin', 'read')) {
		throw redirect(302, '/panel/uzivatele/administratori');
	}
};
