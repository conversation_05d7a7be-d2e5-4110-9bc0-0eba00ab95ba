<script lang="ts">
	import { CirclePlus } from '@lucide/svelte';

	import { exists } from '$lib/appmaxx/util';
	import {
		Button,
		DataTable,
		Flex,
		Heading,
		Loader,
		MetaSeo,
		Spacer,
		Text
	} from '$lib/components';
	import DateCell from '$lib/components/panel/DataTable/cells/renderers/DateCell.svelte';
	import LanguageCell from '$lib/components/panel/DataTable/cells/renderers/LanguageCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import PriceCell from '$lib/components/panel/DataTable/cells/renderers/PriceCell.svelte';
	import {
		createCellRenderer,
		createManagementColumn,
		type DataTableColumnDefinitions
	} from '$lib/components/panel/DataTable/DataTable';
	import { type Lecture, lectureService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	const lecturesQuery = lectureService.listLectures();

	// Color constants for type and status pills
	const TYPE_COLORS = {
		online: '#427ef4', // Primary blue
		onsite: '#2ecc71', // Success green
		test: '#f44242' // Error red
	};

	const STATUS_COLORS = {
		active: '#2ecc71', // Success green
		canceled: '#f44242' // Error red
	};

	let columns = $state<DataTableColumnDefinitions<Lecture>[]>([
		createManagementColumn(),
		{
			accessorKey: 'name',
			header: $t('lectures.dataGrid.name')
		},
		{
			accessorKey: 'type',
			header: $t('lectures.dataGrid.type'),
			cell: ({ getValue }) => {
				const type = getValue() as Lecture['type'];
				return createCellRenderer(PillCell, {
					hexColor: TYPE_COLORS[type as keyof typeof TYPE_COLORS] || '#868686',
					label: $t(`lectures.type.${type}`)
				});
			}
		},
		{
			accessorKey: 'price',
			header: $t('lectures.dataGrid.price'),
			cell: ({ getValue }) =>
				createCellRenderer(PriceCell, {
					price: getValue() as number,
					currency: $t('lectures.common.currency')
				})
		},
		{
			accessorKey: 'lang',
			header: $t('lectures.dataGrid.lang'),
			cell: ({ getValue }) =>
				createCellRenderer(LanguageCell, { language: getValue() as string })
		},
		{
			accessorKey: 'dateStart',
			header: $t('lectures.dataGrid.dateStart'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		},
		{
			accessorKey: 'dateEnd',
			header: $t('lectures.dataGrid.dateEnd'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		},
		{
			accessorKey: 'capacity',
			header: $t('lectures.dataGrid.capacity'),
			cell: ({ getValue }) => {
				const capacity = getValue() as number | undefined;
				return capacity ? capacity.toString() : '';
			}
		},
		{
			accessorKey: 'notes',
			header: $t('lectures.dataGrid.notes')
		},
		{
			accessorKey: 'canceled',
			header: $t('lectures.dataGrid.canceled'),
			cell: ({ getValue }) => {
				const isCanceled = getValue() as boolean;
				return createCellRenderer(PillCell, {
					hexColor: isCanceled ? STATUS_COLORS.canceled : STATUS_COLORS.active,
					label: isCanceled
						? $t('lectures.status.canceled')
						: $t('lectures.status.active')
				});
			}
		}
	]);
</script>

<MetaSeo title={$t('lectures.pageTitle')} />

<Flex direction="row" align="center">
	<Heading as="h1" size="2">{$t('lectures.title')}</Heading>
	<Spacer grow="1" />
	<Button disabled size="medium" variant="success" Icon={CirclePlus}
		>{$t('lectures.createButton')}</Button
	>
</Flex>

<Spacer direction="vertical" size="l" />

{#if $lecturesQuery.isPending}
	<Loader fillPage />
{:else if $lecturesQuery.error}
	<Text>
		<T key="lectures.errors.loadingLectures" />
		: {$lecturesQuery.error.message}
	</Text>
{:else if $lecturesQuery.data}
	<DataTable data={$lecturesQuery.data} {columns} />
{/if}
