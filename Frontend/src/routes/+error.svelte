<script lang="ts">
	import { ArrowLeft } from '@lucide/svelte';

	import { page } from '$app/state';
	import logo from '$lib/assets/img/logos/logo.svg';
	import {
		Button,
		Flex,
		Heading,
		Image,
		PresentationPageContainer,
		Spacer,
		Text
	} from '$lib/components';
	import { T, t } from '$lib/translations/config';
</script>

<PresentationPageContainer grow>
	<Flex direction="col" align="center" justify="center" flex="1">
		<Image src={logo} alt="Logo" height="48px" />
		<Spacer direction="vertical" size="m" />

		<Heading as="h1" size="hero">{page.status}</Heading>
		<Spacer direction="vertical" size="l" />

		<Text align="center">
			<T key="httpErrors.{page.status}" />
		</Text>

		{#if page.error?.message}
			<Spacer direction="vertical" size="s" />
			<Text size="small" color="secondary" align="center">
				{page.error?.message}
			</Text>
		{/if}
		<Spacer direction="vertical" size="l" />

		<Button as="a" size="medium" href="/" Icon={ArrowLeft}>
			{$t('httpErrors.backToHome')}
		</Button>

		<Spacer direction="vertical" size="xxl" />
	</Flex>
</PresentationPageContainer>
