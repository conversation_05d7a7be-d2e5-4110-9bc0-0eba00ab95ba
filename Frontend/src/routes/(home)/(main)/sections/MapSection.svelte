<script lang="ts">
	import { Heading, LocationsMap, Spacer } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T } from '$lib/translations/config';
</script>

<Heading
	use={[animateOnScroll]}
	as="h2"
	align="center"
	size={{ mobile: '3', tablet: '2', desktop: '1' }}
>
	<T key="home.map.title" />
</Heading>

<Spacer direction="vertical" size="xxl" />

<LocationsMap />
