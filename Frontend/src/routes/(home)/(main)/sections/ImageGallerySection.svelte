<script lang="ts">
	import image1 from '$lib/assets/img/page-specific/landing/image-gallery/1.webp';
	import image2 from '$lib/assets/img/page-specific/landing/image-gallery/2.webp';
	import image3 from '$lib/assets/img/page-specific/landing/image-gallery/3.webp';
	import image4 from '$lib/assets/img/page-specific/landing/image-gallery/4.webp';
	import { Card, Carousel, type CarouselItem, Image } from '$lib/components';
	import { t } from '$lib/translations/config';

	interface GalleryImage extends CarouselItem {
		id: number;
		src: string;
		alt: string;
	}

	const images: GalleryImage[] = [
		{ id: 0, src: image1, alt: 'Image 1' },
		{ id: 1, src: image2, alt: 'Image 2' },
		{ id: 2, src: image3, alt: 'Image 3' },
		{ id: 3, src: image4, alt: 'Image 4' }
	];
</script>

<Card
	borderRadius="none"
	paddingX="none"
	variant="primary"
	paddingY="xl"
	pageContainerOverflow="fullscreen"
	fillWidth="true"
	style="overflow: hidden;"
>
	<Carousel
		items={images}
		gap="var(--spacing-xl)"
		itemClass="gallery-image-wrapper"
		containerClass="gallery-container"
		ariaLabel={$t('home.imageGallery.ariaLabel')}
	>
		{#snippet item(image: GalleryImage)}
			<Image
				width="100%"
				height="100%"
				src={image.src}
				alt={image.alt}
				style="max-height: min(30rem, 40dvh); height: 100%; width: auto;"
			/>
		{/snippet}
	</Carousel>
</Card>

<style lang="scss">
	:global {
		.gallery-container {
			width: 100%;
		}

		.gallery-image-wrapper {
			display: flex;
			flex-shrink: 0;
			overflow: hidden;
			border-radius: var(--border-radius-m);
		}
	}
</style>
