<script lang="ts">
	import { ArrowRight } from '@lucide/svelte';

	import penPaper from '$lib/assets/img/drawn-icons/pen-paper.svg';
	import personFramed from '$lib/assets/img/drawn-icons/person-framed.svg';
	import teamwork from '$lib/assets/img/drawn-icons/teamwork.svg';
	import { Button, Card, Flex, Grid, Heading, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';

	import FeatureCard from './components/FeatureCard.svelte';
	import LandingBlueCard from './components/LandingBlueCard.svelte';
</script>

<Card
	borderRadius="xl"
	paddingX="none"
	paddingY="none"
	border="light"
	overflow="hidden"
	use={[animateOnScroll]}
	pageContainerOverflow={{ mobile: 'none', tablet: 'small', desktop: 'subtle' }}
>
	<Flex direction={{ mobile: 'col-reverse', tablet: 'row', desktop: 'row' }}>
		<Flex
			direction="col"
			align="start"
			flex="5"
			paddingX={{ mobile: 'l', tablet: 'xl', desktop: 'xxl' }}
			paddingY={{ mobile: 'l', tablet: 'xl', desktop: 'xl' }}
		>
			<Heading size={{ mobile: '2', tablet: '1', desktop: 'hero' }}>
				<T key="home.landing.hero" />
			</Heading>
			<Spacer direction="vertical" size="l" />

			<Text>
				<T key="home.landing.subtitle1" />
			</Text>
			<Spacer direction="vertical" size="l" />

			<Text>
				<T key="home.landing.subtitle2" />
			</Text>

			<Spacer direction="vertical" grow="1" />
			<Spacer direction="vertical" size="xl" />
			<Button as="a" href="/registrace" Icon={ArrowRight}>
				{$t('home.landing.button')}
			</Button>
		</Flex>

		<Flex direction="col" align="start" flex="3">
			<LandingBlueCard />
		</Flex>
	</Flex>
</Card>

<Spacer direction="vertical" size="xxl" />
<Heading as="h2" align="center" class="sr-only">
	<T key="home.landing.featureCards.titleSr" />
</Heading>

<Grid
	use={[animateOnScroll]}
	cols={{ mobile: '1', tablet: '2', desktop: '3' }}
	alignItems="start"
	gap="xl"
>
	<FeatureCard
		iconSrc={personFramed}
		title={$t('home.landing.featureCards.card1.title')}
		subtitle={$t('home.landing.featureCards.card1.description')}
	/>
	<FeatureCard
		iconSrc={teamwork}
		title={$t('home.landing.featureCards.card2.title')}
		subtitle={$t('home.landing.featureCards.card2.description')}
	/>
	<FeatureCard
		iconSrc={penPaper}
		title={$t('home.landing.featureCards.card3.title')}
		subtitle={$t('home.landing.featureCards.card3.description')}
	/>
</Grid>
