<script lang="ts">
	import clock from '$lib/assets/img/drawn-icons/clock.svg';
	import deviceExchange from '$lib/assets/img/drawn-icons/device-exchange.svg';
	import discoBall from '$lib/assets/img/drawn-icons/disco-ball.svg';
	import headphonesSupport from '$lib/assets/img/drawn-icons/headphones-support.svg';
	import smileyThumbsUp from '$lib/assets/img/drawn-icons/smiley-thumbs-up.svg';
	import target from '$lib/assets/img/drawn-icons/target.svg';
	import forParents from '$lib/assets/img/page-specific/landing/for-parents.webp';
	import forStudents from '$lib/assets/img/page-specific/landing/for-students.webp';
	import { Flex, Grid, Heading, Image, Spacer } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';

	import FeatureCard from './components/FeatureCard.svelte';
</script>

<Heading
	use={[animateOnScroll]}
	as="h2"
	align="center"
	size={{ mobile: '3', tablet: '2', desktop: '1' }}
>
	<T key="home.expectations.title" />
</Heading>

<Spacer direction="vertical" size="xxl" />

{#snippet sectionTitle(image: string, title: string)}
	<Flex
		use={[animateOnScroll]}
		direction="row"
		align="center"
		justify="center"
		style="width: 100%; margin-bottom: -33px"
	>
		<Image height="128px" src={image} alt={title} />
		<Heading as="h3" size="3">
			<T providedValue={title} />
		</Heading>
	</Flex>
{/snippet}

<Grid cols={{ mobile: '1', tablet: '1', desktop: '2' }} gap="xxl">
	<Flex direction="col" align="start" gap="l">
		{@render sectionTitle(forStudents, $t('home.expectations.student.subtitle'))}

		<Flex use={[animateOnScroll]}>
			<FeatureCard
				iconSrc={smileyThumbsUp}
				title={$t('home.expectations.student.cards.card1.title')}
				subtitle={$t('home.expectations.student.cards.card1.description')}
			/>
		</Flex>
		<Flex use={[animateOnScroll]}>
			<FeatureCard
				iconSrc={discoBall}
				title={$t('home.expectations.student.cards.card2.title')}
				subtitle={$t('home.expectations.student.cards.card2.description')}
			/>
		</Flex>
		<Flex use={[animateOnScroll]}>
			<FeatureCard
				iconSrc={target}
				title={$t('home.expectations.student.cards.card3.title')}
				subtitle={$t('home.expectations.student.cards.card3.description')}
			/>
		</Flex>
	</Flex>

	<Flex direction="col" align="start" gap="l">
		{@render sectionTitle(forParents, $t('home.expectations.parent.subtitle'))}

		<Flex use={[animateOnScroll]}>
			<FeatureCard
				iconSrc={deviceExchange}
				title={$t('home.expectations.parent.cards.card1.title')}
				subtitle={$t('home.expectations.parent.cards.card1.description')}
			/>
		</Flex>
		<Flex use={[animateOnScroll]}>
			<FeatureCard
				iconSrc={headphonesSupport}
				title={$t('home.expectations.parent.cards.card2.title')}
				subtitle={$t('home.expectations.parent.cards.card2.description')}
			/>
		</Flex>
		<Flex use={[animateOnScroll]}>
			<FeatureCard
				iconSrc={clock}
				title={$t('home.expectations.parent.cards.card3.title')}
				subtitle={$t('home.expectations.parent.cards.card3.description')}
			/>
		</Flex>
	</Flex>
</Grid>
