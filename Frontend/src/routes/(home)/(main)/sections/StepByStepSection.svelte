<script lang="ts">
	import stepByStep from '$lib/assets/img/page-specific/landing/step-by-step.webp';
	import { Flex, Grid, Heading, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T } from '$lib/translations/config';

	const steps = Array.from({ length: 5 }, (_, i) => i + 1);
</script>

<Heading
	use={[animateOnScroll]}
	as="h2"
	align="center"
	size={{ mobile: '3', tablet: '2', desktop: '1' }}
>
	<T key="home.stepbystep.title" />
</Heading>

<Spacer direction="vertical" size="xxl" />

{#snippet stepCard(index: number)}
	<div use:animateOnScroll class="card">
		<div class="chip"></div>
		<Flex direction="col" gap="sm" inline="true" style="padding-left: var(--spacing-l);">
			<Heading size={{ mobile: '4', tablet: '3', desktop: '3' }} as="h2">
				<T key={`home.stepbystep.cards.card${index}.title`} />
			</Heading>

			<Text color="secondary">
				<T key={`home.stepbystep.cards.card${index}.description`} />
			</Text>
		</Flex>
	</div>
{/snippet}

<Grid rows="1" alignItems="center" gap="xxl" cols={{ mobile: '1', tablet: '2', desktop: '2' }}>
	<Flex direction="col" gap="xxl" style="height: max-content;">
		{#each steps as step (step)}
			{@render stepCard(step)}
		{/each}
	</Flex>

	<Flex paddingX="none" paddingY="none" use={[animateOnScroll]}>
		<picture class="illustration">
			<source srcset={stepByStep} media="(min-width: 639px)" />
			<img src="" alt="students" />
		</picture>
	</Flex>
</Grid>

<style lang="scss">
	.illustration {
		border-radius: var(--spacing-l);
		overflow: hidden;
		width: 100%;
		//max-height: 35rem;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.card {
		position: relative;

		.chip {
			height: 100%;
			width: 0.25rem;
			position: absolute;
			background: var(--color-primary);
			left: 0;
			border-radius: 2em;
		}
	}

	@media screen and (max-width: 639px) {
		.illustration {
			display: none;
		}
	}
</style>
