<script lang="ts">
	import { onMount } from 'svelte';
	import { cubicOut } from 'svelte/easing';
	import { Tween } from 'svelte/motion';

	import { browser } from '$app/environment';
	import bookStack from '$lib/assets/img/page-specific/landing/blue-card/bookstack.webp';
	import ellipse from '$lib/assets/img/page-specific/landing/blue-card/ellipse.svg';
	import handsHeart from '$lib/assets/img/page-specific/landing/blue-card/hands-heart.webp';
	import landingBlueCard from '$lib/assets/img/page-specific/landing/blue-card/landing-bg.webp';
	import line from '$lib/assets/img/page-specific/landing/blue-card/line.svg';
	import student from '$lib/assets/img/page-specific/landing/blue-card/student.webp';
	import triangle from '$lib/assets/img/page-specific/landing/blue-card/triangle.svg';
	import { Card } from '$lib/components';

	const inAnimDuration = 750;
	let animEnded = $state(false);
	let tween = new Tween(-30, {
		duration: inAnimDuration,
		easing: cubicOut
	});

	let scrollY = $state(0);
	const parallaxFactor = -0.2;

	let translateY = $derived(-scrollY * parallaxFactor);
	let finalTranslate = $derived(animEnded ? translateY : tween.current);

	onMount(() => {
		if (browser) {
			tween.set(0);
			setTimeout(() => (animEnded = true), inAnimDuration);
		}
	});
</script>

<svelte:window bind:scrollY />

<div class="landing-blue-card" style:--translate-y="{finalTranslate}px">
	<img class="hands-heart" src={handsHeart} alt="Hands heart" aria-hidden="true" />
	<img class="student" src={student} alt="Student" aria-hidden="true" />

	<Card
		borderRadius="xl"
		fillHeight="true"
		fillWidth="true"
		overflow="hidden"
		paddingX="none"
		paddingY="none"
		style="position: relative;"
	>
		<img class="triangle" src={triangle} alt="Triangle" aria-hidden="true" />
		<img class="line" src={line} alt="Line" aria-hidden="true" />
		<img class="ellipse" src={ellipse} alt="Ellipse" aria-hidden="true" />
		<img class="book-stack" src={bookStack} alt="Book stack" aria-hidden="true" />

		<img class="background" src={landingBlueCard} alt="Landing blue card" aria-hidden="true" />
	</Card>
</div>

<style lang="scss">
	.landing-blue-card {
		position: relative;
		height: 100%;
		isolation: isolate;
	}

	.hands-heart {
		position: absolute;
		left: 0;
		top: 5%;
		z-index: 1;
		translate: -45% calc(var(--translate-y));
		rotate: -13deg;
		width: 9rem;

		@media (max-width: 639px) {
			display: none;
		}
	}

	.student {
		position: absolute;
		bottom: -1px;
		left: 0;
		transform: translateX(-20%);
		height: 100%;
		z-index: 2;
	}

	.book-stack {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 15rem;
		translate: 7% calc(14% + var(--translate-y) * 0.8);
	}

	.triangle {
		position: absolute;
		top: 0;
		right: 0;
		rotate: 20deg;
		width: 4.5rem;
		opacity: 0.9;
		translate: 0 calc(var(--translate-y) * 0.6);
	}

	.line {
		position: absolute;
		top: 20%;
		left: -1%;
		width: 12rem;
		opacity: 0.9;
		translate: 0 calc(var(--translate-y) * 0.55);
	}

	.ellipse {
		position: absolute;
		bottom: 22%;
		right: 4%;
		width: 9.5rem;
		opacity: 0.9;
		translate: 0 calc(var(--translate-y) * 0.7);
	}

	.background {
		width: 100%;
		height: 100%;
		object-fit: cover;
		aspect-ratio: 1/1;
	}
</style>
