<script lang="ts">
	import { Card, Flex, Heading, Image, Spacer, Text } from '$lib/components';
	import { T } from '$lib/translations/config';

	let { iconSrc, title, subtitle } = $props<{
		iconSrc: string;
		title: string;
		subtitle: string;
	}>();
</script>

<Card
	paddingX="l"
	paddingY="l"
	flexDirection="row"
	border="light"
	fillWidth="true"
	style="align-items: start;"
>
	<Image alt={title} srHidden width="48px" src={iconSrc} />
	<Spacer direction="horizontal" size="l" />

	<Flex direction="col">
		<Heading as="h3" size="4">
			<T providedValue={title} />
		</Heading>
		<Spacer direction="vertical" size="s" />
		<Text color="secondary">
			<T providedValue={subtitle} />
		</Text>
	</Flex>
</Card>
