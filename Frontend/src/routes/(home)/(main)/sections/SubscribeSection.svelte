<script lang="ts">
	import { Mail } from '@lucide/svelte';
	import { defaults, type Infer, superForm } from 'sveltekit-superforms';
	import { zod4 } from 'sveltekit-superforms/adapters';

	import { apiService, applyBackendToForm } from '$lib/api';
	import newsletterImg from '$lib/assets/img/page-specific/landing/newsletter.webp';
	import { Button, Card, Flex, Heading, Input, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { NewsletterSubscribeSchema } from '$lib/schemas';
	import { T, t } from '$lib/translations/config';

	let sent: boolean = $state(false);

	const data = defaults(zod4(NewsletterSubscribeSchema));
	const { form, errors, enhance, message, submitting } = superForm<
		Infer<typeof NewsletterSubscribeSchema>,
		string
	>(data, {
		validators: zod4(NewsletterSubscribeSchema),
		SPA: true,
		taintedMessage: null,
		resetForm: false,
		clearOnSubmit: 'errors-and-message',
		autoFocusOnError: false,
		onUpdate: async ({ form }) => {
			if (!form.valid) return;

			const res = await apiService.domains.newsletter.subscribe(form.data);

			if (!res.ok) {
				form.valid = false;
				form.message = 'global.errors.http.internal';
			}

			applyBackendToForm(form, res);

			if (form.valid) {
				sent = true;
			}
		}
	});
</script>

<div class="newsletter-section" style="position: relative;">
	<Card
		id="newsletter-subscribe"
		borderRadius="m"
		paddingX="xxl"
		variant="section"
		paddingY="xxl"
		overflow="hidden"
		use={[animateOnScroll]}
		border="primary"
		class="newsletter-section__background"
	/>

	<div class="newsletter-section__layout">
		<Flex
			direction="col"
			gap="xl"
			class="newsletter-section__layout__form"
			use={[animateOnScroll]}
		>
			{#if !sent}
				<Heading
					style="scroll-margin: 10rem;"
					size={{ mobile: '2', tablet: '2', desktop: '1' }}
				>
					<T key="home.newsletter.title" />
				</Heading>

				<Text>
					<T key="home.newsletter.description" />
				</Text>
				<form action="POST" use:enhance>
					<Flex
						flex="1"
						style="width: 100%"
						gap="l"
						align={{ mobile: 'start', tablet: 'start', desktop: 'center' }}
						direction={{ mobile: 'col', tablet: 'col', desktop: 'row' }}
					>
						<Input
							id="email"
							type="email"
							placeholder={$t('home.newsletter.form.email.label')}
							autocomplete="email"
							Icon={Mail}
							errors={$errors.email}
							bind:value={$form.email}
							fullWidth="true"
						/>

						<Button
							fullWidth={{ mobile: 'true', tablet: 'false', desktop: 'false' }}
							disabled={$submitting}
							type="submit"
						>
							<T key="home.newsletter.form.submit" />
						</Button>
					</Flex>
					{#if $message}
						<Text style="color: var(--color-error);">
							<T key={$message} />
						</Text>
					{/if}
				</form>
			{:else}
				<Heading size="2">
					<T key="home.newsletter.success.title" />
				</Heading>

				<Text>
					<T key="home.newsletter.success.description" />
				</Text>
			{/if}
		</Flex>

		<picture use:animateOnScroll class="newsletter-section__layout__ilustration">
			<source srcset={newsletterImg} media="(min-width: 300px)" />
			<img src="" alt="students" />
		</picture>
	</div>
</div>

<style lang="scss">
	:global {
		.newsletter-section {
			width: 100%;

			&__background {
				position: absolute;
				inset: 0;
				width: 85%;
			}

			&__layout {
				position: relative;
				display: grid;
				grid-template-columns: 5fr 3fr;
				width: calc(100% - 4rem);

				&__form {
					padding: 5rem 4rem;
					min-height: 27rem;
				}
				&__ilustration {
					width: 100%;
					padding: 5rem 0;

					img {
						object-fit: cover;
						width: 100%;
						height: 100%;
						border-radius: var(--border-radius-m);
						overflow: hidden;
					}
				}
			}
		}

		@media screen and (max-width: 1023px) {
			.newsletter-section {
				&__background {
					width: 100%;
				}

				&__layout {
					grid-template-columns: 1fr;
					width: 100%;

					&__form {
						padding: var(--spacing-xxl);
					}
					&__ilustration {
						display: none;
					}
				}
			}
		}

		form {
			width: 100%;

			input {
				width: 100%;
				flex-grow: 1;
			}

			@media screen and (max-width: 1023px) {
			}
		}
	}
</style>
