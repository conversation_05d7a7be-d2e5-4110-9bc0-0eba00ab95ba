<script lang="ts">
	import { ArrowRight } from '@lucide/svelte';

	import personTarget from '$lib/assets/img/drawn-icons/light-person-target.svg';
	import onlineImage from '$lib/assets/img/page-specific/landing/online.webp';
	import inPersonImage from '$lib/assets/img/page-specific/landing/prezencni.webp';
	import mockTestsImage from '$lib/assets/img/page-specific/landing/testy-nanecisto.webp';
	import { Button, Card, Flex, Grid, Heading, Image, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import Chip from '$lib/components/shared/primitives/Chip/Chip.svelte';
	import { T, t } from '$lib/translations/config';
</script>

<Heading
	as="h2"
	use={[animateOnScroll]}
	align="center"
	size={{ mobile: '3', tablet: '2', desktop: '1' }}
>
	<T key="home.offerings.title" />
</Heading>

<Spacer direction="vertical" size="xxl" />

<!-- Offerings -->
{#snippet offeringCard(
	imageSrc: string,
	title: string,
	subtitle: string,
	buttonLabel: string,
	buttonLink: string,
	chipLabels: string[]
)}
	<Card borderRadius="l" paddingX="none" paddingY="none" border="light">
		<Flex style="position: relative;">
			<Image
				alt=""
				width="100%"
				height="100%"
				src={imageSrc}
				aria-hidden="true"
				aspectRatio="16/10"
			/>

			<Flex
				direction="row"
				gap="s"
				align="start"
				wrap="wrap"
				style="position: absolute; top: 0.5rem; left: 0.5rem; right: 0;"
			>
				{#each chipLabels as label (label)}
					<Chip variant="dark">{label}</Chip>
				{/each}
			</Flex>
		</Flex>
		<Flex direction="col" align="start" flex="1" paddingX="xl" paddingY="xl">
			<Heading as="h3" size="4">
				<T providedValue={title} />
			</Heading>
			<Spacer direction="vertical" size="sm" />
			<Text color="secondary">
				<T providedValue={subtitle} />
			</Text>
			<Spacer grow="1" />
			<Spacer direction="vertical" size="l" />
			<Button as="a" href={buttonLink}>{buttonLabel}</Button>
		</Flex>
	</Card>
{/snippet}

<Grid use={[animateOnScroll]} cols={{ mobile: '1', tablet: '2', desktop: '3' }} gap="xl" debug>
	{@render offeringCard(
		inPersonImage,
		$t('home.offerings.cards.card1.title'),
		$t('home.offerings.cards.card1.description'),
		$t('home.offerings.cards.card1.buttonLabel'),
		'/prezencni-doucovani',
		[
			$t('home.offerings.cards.card1.chips.chip1'),
			$t('home.offerings.cards.card1.chips.chip2'),
			$t('home.offerings.cards.card1.chips.chip3'),
			$t('home.offerings.cards.card1.chips.chip4')
		]
	)}
	{@render offeringCard(
		onlineImage,
		$t('home.offerings.cards.card2.title'),
		$t('home.offerings.cards.card2.description'),
		$t('home.offerings.cards.card2.buttonLabel'),
		'/online-doucovani',
		[
			$t('home.offerings.cards.card2.chips.chip1'),
			$t('home.offerings.cards.card2.chips.chip2'),
			$t('home.offerings.cards.card2.chips.chip3'),
			$t('home.offerings.cards.card2.chips.chip4')
		]
	)}
	{@render offeringCard(
		mockTestsImage,
		$t('home.offerings.cards.card3.title'),
		$t('home.offerings.cards.card3.description'),
		$t('home.offerings.cards.card3.buttonLabel'),
		'/prijimacky-nanecisto',
		[
			$t('home.offerings.cards.card3.chips.chip1'),
			$t('home.offerings.cards.card3.chips.chip2'),
			$t('home.offerings.cards.card3.chips.chip3')
		]
	)}
</Grid>

<Spacer direction="vertical" size="xxl" />

<Card
	use={[animateOnScroll]}
	borderRadius="l"
	variant="primary"
	flexDirection="row"
	paddingX={{ mobile: 'xl', tablet: 'xl', desktop: 'xxl' }}
	paddingY={{ mobile: 'l', tablet: 'l', desktop: 'xl' }}
	border="light"
	fillWidth="true"
	pageContainerOverflow={{ mobile: 'none', tablet: 'small', desktop: 'subtle' }}
	style="align-items: center"
>
	<Flex
		flex="1"
		align={{ mobile: 'stretch', tablet: 'center', desktop: 'center' }}
		direction={{ mobile: 'col', tablet: 'row', desktop: 'row' }}
	>
		<Image width="64px" src={personTarget} alt="Target" />
		<Spacer
			direction={{ mobile: 'vertical', tablet: 'horizontal', desktop: 'horizontal' }}
			size="l"
		/>

		<Flex direction="col" align="start">
			<Heading as="h3" size="4">
				<T key="home.offerings.individual.title" />
				<Spacer direction="vertical" size="s" />
				<Text style="max-width: 30rem;">
					<T key="home.offerings.individual.subtitle" />
				</Text>
			</Heading>
		</Flex>

		<Spacer grow="1" />
		<Spacer
			direction={{ mobile: 'vertical', tablet: 'horizontal', desktop: 'horizontal' }}
			size="m"
		/>

		<Button
			as="a"
			href="/individualni-doucovani"
			size="small"
			variant="light"
			aria-label={$t('home.offerings.individual.buttonSrLabel')}
		>
			<ArrowRight size="1.25rem" strokeWidth={2} />
		</Button>
	</Flex>
</Card>
