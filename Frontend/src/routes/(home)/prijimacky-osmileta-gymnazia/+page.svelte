<script lang="ts">
	import { Heading, Spacer, Text } from '$lib/components';
	import { T, t } from '$lib/translations/config';
</script>

<svelte:head>
	<title>{$t('preparations8g.pageTitle')}</title>
</svelte:head>

<Spacer direction="vertical" size="xxl" />
<Spacer direction="vertical" size="l" />
<Heading as="h1" size="hero" align="center">
	<T key="preparations8g.hero" />
</Heading>
<Spacer direction="vertical" size="between-sections" />

<Text as="p">
	<T key="preparations8g.description" />
</Text>
<Spacer direction="vertical" size="between-sections" />

<Heading as="h2" size="1" align="center">
	<T key="preparations8g.expectations.title" />
</Heading>
<Spacer direction="vertical" size="xxl" />

<Text as="p">
	<T key="preparations8g.expectations.paragraphs.1" />
</Text>
<Spacer direction="vertical" size="l" />
<Text as="p">
	<T key="preparations8g.expectations.paragraphs.2" />
</Text>
