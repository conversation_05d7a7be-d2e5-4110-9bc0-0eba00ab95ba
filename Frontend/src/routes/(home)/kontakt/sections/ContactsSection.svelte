<script lang="ts">
	import { Mail, Phone } from '@lucide/svelte';

	import janStastny from '$lib/assets/img/page-specific/contact/jan-stastny.webp';
	import julian<PERSON><PERSON> from '$lib/assets/img/page-specific/contact/julian-arden.webp';
	import michalTucek from '$lib/assets/img/page-specific/contact/michal-tucek.webp';
	import { Card, Flex, Grid, Heading, Image, Spacer, TextLink } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

{#snippet contactCard(
	imageSrc: string,
	name: string,
	phone: string | undefined,
	phoneLink: string | undefined,
	email: string,
	emailLink: string
)}
	<Card use={[animateOnScroll]} borderRadius="l" paddingX="l" paddingY="l" border="light">
		<Flex direction="row" align="center" gap="m" wrap="wrap">
			<Image width="80px" src={imageSrc} alt={name} />

			<Flex direction="col" align="start">
				<Heading as="h3" size="4">
					<T providedValue={name} />
				</Heading>
				<Spacer direction="vertical" size="sm" />

				{#if phone && phoneLink}
					<Flex direction="row" gap="s" align="center">
						<Phone size="1.1em" strokeWidth={2} color="var(--color-primary)" />

						<TextLink href={phoneLink}>
							<T providedValue={phone} />
						</TextLink>
					</Flex>
					<Spacer direction="vertical" size="s" />
				{/if}

				<Flex direction="row" gap="s" align="center">
					<Mail size="1.1em" strokeWidth={2} color="var(--color-primary)" />

					<TextLink href={emailLink}>
						<T providedValue={email} />
					</TextLink>
				</Flex>
			</Flex>
		</Flex>
	</Card>
{/snippet}

<Grid cols={{ mobile: '1', tablet: '2', desktop: '3' }} gap="xl">
	{@render contactCard(
		janStastny,
		$t('contact.contactCards.card1.personName'),
		undefined,
		undefined,
		$t('contact.contactCards.card1.email'),
		$t('contact.contactCards.card1.emailLink')
	)}

	{@render contactCard(
		michalTucek,
		$t('contact.contactCards.card2.personName'),
		undefined,
		undefined,
		$t('contact.contactCards.card2.email'),
		$t('contact.contactCards.card2.emailLink')
	)}

	{@render contactCard(
		julianArden,
		$t('contact.contactCards.card3.personName'),
		undefined,
		undefined,
		$t('contact.contactCards.card3.email'),
		$t('contact.contactCards.card3.emailLink')
	)}
</Grid>
