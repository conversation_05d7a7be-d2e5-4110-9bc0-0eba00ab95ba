<script lang="ts">
	import { Phone } from '@lucide/svelte';

	import phoneEmojis from '$lib/assets/img/page-specific/contact/phone-emojis.webp';
	import { Button, Card, Flex, Heading, Image, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

<Flex
	direction={{ mobile: 'col', tablet: 'row', desktop: 'row' }}
	align="center"
	justify="center"
	gap="xl"
	style="align-self: center; max-width: 1000px; width: 100%;"
>
	<Flex flex="1" use={[animateOnScroll]}>
		<Heading as="h1" size="hero" align="center">
			<T key="contact.hero.title" />
		</Heading>
	</Flex>

	<Flex flex="1" style="width: 100%" use={[animateOnScroll]}>
		<Card
			variant="primary"
			fillWidth="true"
			paddingX="xl"
			paddingY="l"
			style="position: relative; isolation: isolate;"
			overflow="visible"
		>
			<Flex direction="col" align="start">
				<Heading as="h2" size="3">
					<T key="contact.hero.card.title" />
				</Heading>
				<Spacer direction="vertical" size="sm" />
				<Text>
					<T key="contact.hero.card.description" />
				</Text>
				<Spacer direction="vertical" size="l" />
				<Button
					as="a"
					href={$t('contact.hero.card.buttonLink')}
					variant="light"
					size="medium"
					Icon={Phone}
				>
					{$t('contact.hero.card.buttonLabel')}
				</Button>
			</Flex>

			<Image
				src={phoneEmojis}
				alt="Phone emojis"
				aria-hidden="true"
				style="height: 95%; position: absolute; z-index: -1; bottom: 0; right: -1.5rem;"
			/>
		</Card>
	</Flex>
</Flex>
