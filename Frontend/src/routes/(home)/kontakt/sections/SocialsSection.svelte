<script lang="ts">
	import facebook from '$lib/assets/img/page-specific/contact/facebook.webp';
	import instagram from '$lib/assets/img/page-specific/contact/instagram.webp';
	import tiktok from '$lib/assets/img/page-specific/contact/tiktok.webp';
	import { Card, Flex, Grid, Heading, Image, Spacer, TextLink } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

<Heading use={[animateOnScroll]} as="h2" size="1" align="center">
	<T key="contact.socials.title" />
</Heading>

<Spacer direction="vertical" size="xxl" />

{#snippet socialCard(socialName: string, socialLink: string, socialIconSrc: string)}
	<Card
		use={[animateOnScroll]}
		paddingX="xl"
		paddingY="l"
		border="light"
		fillWidth="true"
		flexDirection="row"
	>
		<Flex direction="row" align="center" gap="m">
			<Image width="48px" src={socialIconSrc} alt={socialName} />

			<Heading as="h3" size="4">
				<TextLink href={socialLink}>
					<T providedValue={socialName} />
				</TextLink>
			</Heading>
		</Flex>
	</Card>
{/snippet}

<Grid cols={{ mobile: '1', tablet: '2', desktop: '3' }} gap="xl">
	{@render socialCard(
		$t('contact.socials.instagram.name'),
		$t('contact.socials.instagram.link'),
		instagram
	)}
	{@render socialCard(
		$t('contact.socials.facebook.name'),
		$t('contact.socials.facebook.link'),
		facebook
	)}
	{@render socialCard(
		$t('contact.socials.tiktok.name'),
		$t('contact.socials.tiktok.link'),
		tiktok
	)}
</Grid>
