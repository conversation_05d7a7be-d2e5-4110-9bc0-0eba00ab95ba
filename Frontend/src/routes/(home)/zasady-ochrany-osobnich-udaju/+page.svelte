<script lang="ts">
	import { <PERSON>ing, MarkdownStyles, MetaSeo, Spacer } from '$lib/components';
	import { T, t, TMd } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('privacy.pageTitle')}
	description={$t('privacy.pageDescription')}
	canonical="https://www.dej-prijimacky.cz/zasady-ochrany-osobnich-udaju"
	robots="index, follow"
/>

<Spacer direction="vertical" size="xxl" />

<Heading as="h1" size={{ mobile: '1', tablet: 'hero', desktop: 'hero' }} align="center">
	<T key="privacy.hero" />
</Heading>

<Spacer direction="vertical" size="between-sections" />

<MarkdownStyles>
	<TMd key="privacyMd" />
</MarkdownStyles>
