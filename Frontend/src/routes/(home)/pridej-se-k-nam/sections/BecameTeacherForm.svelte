<script lang="ts">
	import { GraduationCap, Mail, Phone, Send, SquareUserRound } from '@lucide/svelte';
	import { defaults, type Infer, superForm } from 'sveltekit-superforms';
	import { zod4 } from 'sveltekit-superforms/adapters';

	import { apiService, applyBackendToForm } from '$lib/api';
	import {
		Button,
		Card,
		Checkbox,
		Flex,
		Heading,
		Input,
		Spacer,
		Text,
		Textarea
	} from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { BecomeTeacherSchema } from '$lib/schemas/becomeTeacher';
	import { T, t } from '$lib/translations/config';

	let sent: boolean = $state(false);

	const data = defaults(zod4(BecomeTeacherSchema));
	const { form, errors, enhance, message, submitting } = superForm<
		Infer<typeof BecomeTeacherSchema>,
		string
	>(data, {
		validators: zod4(BecomeTeacherSchema),
		SPA: true,
		taintedMessage: null,
		resetForm: false,
		clearOnSubmit: 'errors-and-message',
		autoFocusOnError: false,
		onUpdate: async ({ form }) => {
			if (!form.valid) return;

			const res = await apiService.domains.becomeTeacher.create(form.data);

			if (!res.ok) {
				form.valid = false;
				form.message = 'global.errors.http.internal';
			}

			applyBackendToForm(form, res);

			if (form.valid) {
				sent = true;
			}
		}
	});
</script>

<!-- Form -->
<Flex align="center" justify="center" use={[animateOnScroll]}>
	<Card paddingX="xl" paddingY="xxl" border="primary">
		{#if !sent}
			<form action="POST" use:enhance autocomplete="on">
				<Flex direction="col" gap="m">
					<Heading align="center" size={{ mobile: '3', tablet: '2', desktop: '2' }}>
						<T key="becomeTeacher.form.title" />
					</Heading>
					<Spacer size="m" />

					<Flex direction="col" gap="s">
						<Input
							autocomplete="name"
							id="name"
							Icon={SquareUserRound}
							type="text"
							errors={$errors.name}
							bind:value={$form.name}
							placeholder={$t('becomeTeacher.form.name.label')}
						/>
						<Input
							id="school"
							Icon={GraduationCap}
							type="text"
							errors={$errors.school}
							bind:value={$form.school}
							placeholder={$t('becomeTeacher.form.school.label')}
						/>
						<Input
							autocomplete="email"
							id="mail"
							Icon={Mail}
							type="text"
							errors={$errors.email}
							bind:value={$form.email}
							placeholder={$t('becomeTeacher.form.email.label')}
						/>
						<Input
							autocomplete="tel"
							id="phone"
							Icon={Phone}
							type="text"
							errors={$errors.phone}
							bind:value={$form.phone}
							placeholder={$t('becomeTeacher.form.phone.label')}
						/>
					</Flex>

					<Textarea
						id="message"
						rows={10}
						errors={$errors.message}
						bind:value={$form.message}
						placeholder={$t('becomeTeacher.form.message.label')}
					/>

					<Checkbox id="consent" errors={$errors.consent} bind:checked={$form.consent}>
						<T key="becomeTeacher.form.consent.label" />
					</Checkbox>
					<Spacer size="l" />

					{#if $message}
						<Text role="alert" style="color: crimson;">
							<T key={$message} />
						</Text>

						<Spacer direction="vertical" size="s" />
					{/if}
					<Button Icon={Send} disabled={$submitting}>Odeslat</Button>
				</Flex>
			</form>
		{:else}
			<Text>Děkujeme</Text>
		{/if}
	</Card>
</Flex>
