<script lang="ts">
	import { ArrowRight } from '@lucide/svelte';

	import { Button, Flex, Heading, Spacer } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

<Flex direction="col" align="center" use={[animateOnScroll]}>
	<Heading as="h2" size="2">
		<T key="becomeTeacher.whatAreYouWaitingFor.title" />
	</Heading>
	<Spacer size="l" />

	<Button as="a" href="/kontakt" Icon={ArrowRight}>
		{$t('becomeTeacher.whatAreYouWaitingFor.buttonLabel')}
	</Button>
</Flex>
