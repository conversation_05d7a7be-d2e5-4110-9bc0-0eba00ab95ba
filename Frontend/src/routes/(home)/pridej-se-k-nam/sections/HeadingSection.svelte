<script lang="ts">
	import studentsImg from '$lib/assets/img/page-specific/auth/students.webp';
	import { Card, Flex, Heading, Image, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

<Flex
	pageContainerOverflow={{ mobile: 'none', tablet: 'small', desktop: 'subtle' }}
	direction={{ mobile: 'col', tablet: 'col', desktop: 'row' }}
	gap="xl"
>
	<Flex direction="col" flex="1" gap="l">
		<Card
			use={[animateOnScroll]}
			style="height: 100%"
			borderRadius="l"
			paddingX="none"
			paddingY="none"
			fillWidth="true"
			border="light"
		>
			<Image
				style="width:100%; height: 100%; object-fit: cover"
				src={studentsImg}
				alt="Main image"
			/>
		</Card>
	</Flex>

	<Flex
		direction="col"
		flex="1"
		paddingY={{ mobile: 'xl', tablet: 'xl', desktop: 'l' }}
		paddingX="m"
		gap="l"
	>
		{#snippet card(title: string, description: string)}
			<Flex use={[animateOnScroll]} direction="col" gap="m">
				<Heading size="3">
					<T providedValue={title} />
				</Heading>

				<Text>
					<T providedValue={description} />
				</Text>
			</Flex>
		{/snippet}

		{@render card(
			$t('becomeTeacher.cards.card1.title'),
			$t('becomeTeacher.cards.card1.description')
		)}

		{@render card(
			$t('becomeTeacher.cards.card2.title'),
			$t('becomeTeacher.cards.card2.description')
		)}

		{@render card(
			$t('becomeTeacher.cards.card3.title'),
			$t('becomeTeacher.cards.card3.description')
		)}
	</Flex>
</Flex>
