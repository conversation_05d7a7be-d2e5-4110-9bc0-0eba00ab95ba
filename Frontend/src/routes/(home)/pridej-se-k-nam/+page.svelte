<script lang="ts">
	import { Head<PERSON>, MetaSeo, Spacer } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';

	import HeadingSection from './sections/HeadingSection.svelte';
	import WhatAreYouWaitingForSection from './sections/WhatAreYouWaitingForSection.svelte';
</script>

<MetaSeo
	title={$t('becomeTeacher.pageTitle')}
	description={$t('becomeTeacher.pageDescription')}
	canonical="https://www.dej-prijimacky.cz/pridej-se-k-nam"
	robots="index, follow"
/>

<Spacer direction="vertical" size="xxl" />
<Heading use={[animateOnScroll]} as="h1" size="hero" align="center">
	<T key="becomeTeacher.hero" />
</Heading>
<Spacer direction="vertical" size="between-sections" />

<HeadingSection />

<Spacer size="between-sections" />
<WhatAreYouWaitingForSection />

<!--
<BecameTeacherForm />
-->
