<script lang="ts">
	import { type Component } from 'svelte';

	import CoinStackIcon from '$lib/assets/img/drawn-icons/CoinStackIcon.svelte';
	import DesktopMonitorIcon from '$lib/assets/img/drawn-icons/DesktopMonitorIcon.svelte';
	import FontSizeIcon from '$lib/assets/img/drawn-icons/FontSizeIcon.svelte';
	import IdeaBulbIcon from '$lib/assets/img/drawn-icons/IdeaBulbIcon.svelte';
	import LoopArrowIcon from '$lib/assets/img/drawn-icons/LoopArrowIcon.svelte';
	import PenPaperIcon from '$lib/assets/img/drawn-icons/PenPaperIcon.svelte';
	import PresentationAudienceIcon from '$lib/assets/img/drawn-icons/PresentationAudienceIcon.svelte';
	import RotateAngleIcon from '$lib/assets/img/drawn-icons/RotateAngleIcon.svelte';
	import slashIcon from '$lib/assets/img/page-specific/pricing/slash.svg?url';
	import { type IconAssetProps } from '$lib/assets/types';
	import { Button, Card, Flex, Grid, Heading, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

{#snippet passCard(
	cardVariant: 'primary' | 'section',
	Icon: Component<IconAssetProps>,
	title: string,
	features: { text: string; Icon: Component<IconAssetProps> }[],
	priceMonthly: string | undefined,
	priceYearly: string
)}
	<Card
		use={[animateOnScroll]}
		borderRadius="l"
		paddingX="l"
		paddingY="l"
		border={cardVariant === 'primary' ? 'primary' : 'light'}
		fillWidth="true"
		variant={cardVariant}
	>
		<Flex justify="center">
			<svelte:component
				this={Icon}
				aria-hidden="true"
				size={64}
				color={cardVariant === 'section' ? undefined : 'white'}
			/>
		</Flex>

		<Spacer size="sm" />

		<Flex justify="center">
			<Heading size="3" color={cardVariant === 'section' ? 'primary' : 'light'}>
				{title}
			</Heading>
		</Flex>
		<Spacer size="sm" />

		<Flex
			justify="center"
			align="center"
			gap="s"
			direction={{ desktop: 'row', mobile: 'col', tablet: 'col' }}
		>
			<Flex align="start" gap="s">
				<Text size="h2" weight="bold">{priceYearly}</Text>
				<Text size="regular">
					<T key="pricing.passes.yearly" />
				</Text>
			</Flex>
			{#if priceMonthly}
				<img src={slashIcon} alt="slash" class="slash-icon" />
				<Flex align="start" gap="s">
					<Text size="h3" weight="semibold" color="secondary">{priceMonthly}</Text>
					<Text size="regular" color="secondary">
						<T key="pricing.passes.monthly" />
					</Text>
				</Flex>
			{/if}
		</Flex>
		<Spacer size="ml" />

		<Flex direction="col" gap="s">
			{#each features as { Icon, text } (text)}
				<Flex direction="row" align="start" gap="s">
					<svelte:component
						this={Icon}
						aria-hidden="true"
						size={24}
						color={cardVariant === 'section' ? undefined : 'white'}
					/>
					<Text>
						<T providedValue={text} />
					</Text>
				</Flex>
			{/each}
		</Flex>
		<Spacer size="xl" />
		<Spacer grow="1" />

		<Button as="a" href="/prihlaseni" variant={cardVariant === 'section' ? 'primary' : 'light'}>
			<T key="pricing.passes.buyButtonLabel" />
		</Button>
	</Card>
{/snippet}

<Grid
	cols={{ mobile: '1', tablet: '2', desktop: '3' }}
	paddingX={{ mobile: 'none', tablet: 's', desktop: 'm' }}
	paddingY={{ mobile: 'none', tablet: 'l', desktop: 'xl' }}
	gap="l"
	centerOrphan
>
	{@render passCard(
		'section',
		LoopArrowIcon,
		$t(`pricing.passes.card1.title`),
		[
			{ text: $t(`pricing.passes.card1.features.0`), Icon: PresentationAudienceIcon },
			{ text: $t(`pricing.passes.card1.features.1`), Icon: DesktopMonitorIcon },
			{ text: $t(`pricing.passes.card1.features.2`), Icon: CoinStackIcon }
		],
		$t(`pricing.passes.card1.priceMonthly`),
		$t(`pricing.passes.card1.priceYearly`)
	)}

	{@render passCard(
		'section',
		RotateAngleIcon,
		$t(`pricing.passes.card3.title`),
		[
			{ text: $t(`pricing.passes.card3.features.0`), Icon: PresentationAudienceIcon },
			{ text: $t(`pricing.passes.card3.features.1`), Icon: DesktopMonitorIcon },
			{ text: $t(`pricing.passes.card3.features.2`), Icon: CoinStackIcon }
		],
		$t(`pricing.passes.card3.priceMonthly`),
		$t(`pricing.passes.card3.priceYearly`)
	)}

	{@render passCard(
		'section',
		FontSizeIcon,
		$t(`pricing.passes.card4.title`),
		[
			{ text: $t(`pricing.passes.card4.features.0`), Icon: PresentationAudienceIcon },
			{ text: $t(`pricing.passes.card4.features.1`), Icon: DesktopMonitorIcon },
			{ text: $t(`pricing.passes.card4.features.2`), Icon: CoinStackIcon }
		],
		$t(`pricing.passes.card4.priceMonthly`),
		$t(`pricing.passes.card4.priceYearly`)
	)}

	{@render passCard(
		'section',
		PenPaperIcon,
		$t(`pricing.passes.card2.title`),
		[{ text: $t(`pricing.passes.card2.features.0`), Icon: PenPaperIcon }],
		undefined,
		$t(`pricing.passes.card2.priceYearly`)
	)}

	{@render passCard(
		'primary',
		IdeaBulbIcon,
		$t(`pricing.passes.card5.title`),
		[
			{ text: $t(`pricing.passes.card5.features.0`), Icon: PresentationAudienceIcon },
			{ text: $t(`pricing.passes.card5.features.1`), Icon: DesktopMonitorIcon },
			{ text: $t(`pricing.passes.card5.features.2`), Icon: PenPaperIcon },
			{ text: $t(`pricing.passes.card5.features.3`), Icon: CoinStackIcon }
		],
		undefined,
		$t(`pricing.passes.card5.priceYearly`)
	)}
</Grid>

<style lang="scss">
	@use '$lib/assets/styles/breakpoints' as mq;

	.slash-icon {
		@include mq.mobile {
			display: none;
		}
		@include mq.tablet {
			display: none;
		}
	}
</style>
