<script lang="ts">
	import { ShoppingCart } from '@lucide/svelte';

	import expand from '$lib/assets/img/drawn-icons/expand.svg';
	import flash from '$lib/assets/img/drawn-icons/flash.svg';
	import personQueen from '$lib/assets/img/drawn-icons/light-person-queen.svg';
	import moneyBag from '$lib/assets/img/drawn-icons/money-bag.svg';
	import bgImage from '$lib/assets/img/page-specific/pricing/pricing-bg.webp';
	import { Button, Card, Flex, Grid, Heading, Image, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import Chip from '$lib/components/shared/primitives/Chip/Chip.svelte';
	import { T, t } from '$lib/translations/config';
</script>

{#snippet packageCard(
	iconSrc: string,
	cardVariant: 'primary' | 'section',
	title: string,
	subtitle: string,
	lectionsCount: string,
	price: string,
	pricePerLection: string,
	discount: string | undefined
)}
	<Card
		use={[animateOnScroll]}
		borderRadius="l"
		paddingX={{ mobile: 'l', tablet: 'l', desktop: 'm' }}
		border={cardVariant === 'primary' ? 'primary' : 'light'}
		fillWidth="true"
		variant={cardVariant}
	>
		<Flex direction="col" align="start" flex="1">
			<Flex direction="row" align="center" justify="between" style="width: 100%;">
				<Image
					src={iconSrc}
					alt="Background image"
					aria-hidden="true"
					style="height: 44px"
				/>

				{#if discount}
					<Chip variant={cardVariant === 'primary' ? 'light' : 'dark'}>{discount}</Chip>
				{/if}
			</Flex>

			<Spacer direction="vertical" size="m" />
			<Heading as="h2" size="4">
				<T providedValue={title} />
			</Heading>
			<Spacer direction="vertical" size="s" />
			<Text>
				<T providedValue={subtitle} />
			</Text>

			<Spacer grow="1" />
			<Spacer direction="vertical" size="l" />

			<Text size="small" color={cardVariant === 'primary' ? 'inherited' : 'secondary'}>
				<T providedValue={lectionsCount} />
			</Text>
			<Flex direction="row" align="end" justify="between" style="width: 100%;">
				<Heading as="h3" size="3">
					<T providedValue={price} />
				</Heading>

				<Text size="small" color={cardVariant === 'primary' ? 'inherited' : 'secondary'}>
					<T providedValue={pricePerLection} />
				</Text>
			</Flex>
			<Spacer direction="vertical" size="l" />

			<Button
				Icon={ShoppingCart}
				size="medium"
				as="a"
				href="/prihlaseni"
				fullWidth={{ desktop: 'false', tablet: 'false', mobile: 'true' }}
				variant={cardVariant === 'primary' ? 'light' : 'primary'}
			>
				<T key="pricing.packages.buyButtonLabel" />
			</Button>
		</Flex>
	</Card>
{/snippet}

<Flex direction="col" style="position: relative; isolation: isolate;">
	<Card
		paddingX="none"
		paddingY="none"
		fillWidth="true"
		overflow="hidden"
		borderRadius={{ mobile: 'none', tablet: 'l', desktop: 'xl' }}
		pageContainerOverflow={{ mobile: 'fullscreen', tablet: 'small', desktop: 'small' }}
		style="position: absolute; top: 0; left: 0; right: 0; z-index: -1;"
	>
		<Image
			use={[animateOnScroll]}
			src={bgImage}
			alt="Background image"
			aria-hidden="true"
			style="width: 100%; object-fit: cover; height: max(300px, 20dvh)"
		/>
	</Card>

	<Grid
		cols={{ mobile: '1', tablet: '2', desktop: '4' }}
		paddingX={{ mobile: 'none', tablet: 's', desktop: 'm' }}
		paddingY={{ mobile: 'l', tablet: 'l', desktop: 'xl' }}
		gap="l"
		style="padding-bottom: 0;"
	>
		{@render packageCard(
			expand,
			'section',
			$t(`pricing.packages.card1.title`),
			$t(`pricing.packages.card1.subtitle`),
			$t(`pricing.packages.card1.lectionsCount`),
			$t(`pricing.packages.card1.price`),
			$t(`pricing.packages.card1.pricePerLection`),
			undefined
		)}

		{@render packageCard(
			flash,
			'section',
			$t(`pricing.packages.card2.title`),
			$t(`pricing.packages.card2.subtitle`),
			$t(`pricing.packages.card2.lectionsCount`),
			$t(`pricing.packages.card2.price`),
			$t(`pricing.packages.card2.pricePerLection`),
			$t(`pricing.packages.card2.discount`)
		)}

		{@render packageCard(
			moneyBag,
			'section',
			$t(`pricing.packages.card3.title`),
			$t(`pricing.packages.card3.subtitle`),
			$t(`pricing.packages.card3.lectionsCount`),
			$t(`pricing.packages.card3.price`),
			$t(`pricing.packages.card3.pricePerLection`),
			$t(`pricing.packages.card3.discount`)
		)}

		{@render packageCard(
			personQueen,
			'primary',
			$t(`pricing.packages.card4.title`),
			$t(`pricing.packages.card4.subtitle`),
			$t(`pricing.packages.card4.lectionsCount`),
			$t(`pricing.packages.card4.price`),
			$t(`pricing.packages.card4.pricePerLection`),
			$t(`pricing.packages.card4.discount`)
		)}
	</Grid>
</Flex>
