<script lang="ts">
	import { Callout, Heading, MetaSeo, Spacer } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';

	import Packages from './sections/Packages.svelte';
	import Passes from './sections/Passes.svelte';
</script>

<MetaSeo
	title={$t('pricing.pageTitle')}
	description={$t('pricing.pageDescription')}
	canonical="https://www.dej-prijimacky.cz/cenik"
	robots="index, follow"
/>

<Callout>
	<T key="pricing.comingSoon" />
</Callout>
<Spacer direction="vertical" size="xxl" />
<Spacer direction="vertical" size="xxl" />
<Heading use={[animateOnScroll]} as="h1" size="hero" align="center">
	<T key="pricing.hero" />
</Heading>
<Spacer direction="vertical" size="between-sections" />

<Packages />
<Spacer direction="vertical" size="between-sections" />

<Passes />
