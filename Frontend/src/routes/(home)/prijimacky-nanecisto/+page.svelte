<script lang="ts">
	import students from '$lib/assets/img/page-specific/auth/students.webp';
	import { MetaSeo } from '$lib/components';
	import TutoringPageContainer from '$lib/components/presentation/TutoringPageContainer.svelte';
	import { t } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('tutoringTests.pageTitle')}
	description={$t('tutoringTests.pageDescription')}
	canonical="https://www.dej-prijimacky.cz/prijimacky-nanecisto"
	robots="index, follow"
/>

<TutoringPageContainer
	heading={$t('tutoringTests.hero.title')}
	chips={[
		$t('tutoringTests.hero.chips.1'),
		$t('tutoringTests.hero.chips.2'),
		$t('tutoringTests.hero.chips.3')
	]}
	heroDescription={$t('tutoringTests.hero.description')}
	action={$t('tutoringTests.hero.action')}
	pageDescription={$t('tutoringTests.description')}
	featureCardKeyStart="tutoringTests.perks"
	comingSoon={$t('tutoringTests.comingSoon')}
	map={true}
	imageSrc={students}
	mapTitle={$t('tutoringTests.map.title')}
	mapSubtitle={$t('tutoringTests.map.subtitle')}
/>
