<script lang="ts">
	import hat from '$lib/assets/img/page-specific/about-us/hat.webp';
	import mainImage from '$lib/assets/img/page-specific/about-us/main.webp';
	import { Card, Flex, Grid, Heading, Image, MetaSeo, Spacer, Text } from '$lib/components';
	import { animateOnScroll } from '$lib/components/presentation/use.animate-on-scroll';
	import { T, t } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('aboutUs.pageTitle')}
	description={$t('aboutUs.pageDescription')}
	canonical="https://www.dej-prijimacky.cz/o-nas"
	robots="index, follow"
/>

<Spacer direction="vertical" size="xxl" />
<Spacer direction="vertical" size="l" />
<Flex justify="center">
	<Flex style="position: relative; isolation: isolate;" align="center">
		<Image
			src={hat}
			alt="Hat"
			aria-hidden="true"
			class="about-us__hat"
			style="position: absolute; bottom: 100%; right: 100%; z-index: -1; translate: 40% 40%; height: 140px;"
		/>
		<Heading as="h1" size="hero" align="center">
			<T key="aboutUs.hero" />
		</Heading>
	</Flex>
</Flex>

<Spacer direction="vertical" size="between-sections" />

<Flex
	pageContainerOverflow={{ mobile: 'none', tablet: 'small', desktop: 'subtle' }}
	direction={{ mobile: 'col-reverse', tablet: 'col-reverse', desktop: 'row' }}
	gap="xl"
>
	<Flex
		direction="col"
		flex="1"
		paddingY={{ mobile: 'xl', tablet: 'xl', desktop: 'l' }}
		paddingX="m"
	>
		<Text use={[animateOnScroll]}>
			<T key="aboutUs.description" />
		</Text>
	</Flex>

	<Flex direction="col" flex="1" gap="l">
		{#snippet numberCard(title: string, description: string)}
			<Card use={[animateOnScroll]} paddingX="l" paddingY="l" border="light" fillWidth="true">
				<Heading as="h3" size="3">
					<T providedValue={title} />
				</Heading>

				<Spacer direction="vertical" size="s" />

				<Text color="secondary">
					<T providedValue={description} />
				</Text>
			</Card>
		{/snippet}

		<Card
			use={[animateOnScroll]}
			borderRadius="l"
			paddingX="none"
			paddingY="none"
			fillWidth="true"
		>
			<Image width="100%" src={mainImage} alt="Main image" />
		</Card>

		<Grid cols={{ mobile: '1', tablet: '3', desktop: '3' }} gap="l">
			{@render numberCard(
				$t('aboutUs.cards.card1.title'),
				$t('aboutUs.cards.card1.description')
			)}
			{@render numberCard(
				$t('aboutUs.cards.card2.title'),
				$t('aboutUs.cards.card2.description')
			)}
			{@render numberCard(
				$t('aboutUs.cards.card3.title'),
				$t('aboutUs.cards.card3.description')
			)}
		</Grid>
	</Flex>
</Flex>

<style lang="scss">
	:global {
		.about-us__hat {
			animation: float-nicely 2s ease infinite;
		}
	}

	@keyframes float-nicely {
		0% {
			translate: 40% 40%;
		}
		50% {
			translate: 40% 35%;
		}
		100% {
			translate: 40% 40%;
		}
	}
</style>
