<script lang="ts">
	import { onMount } from 'svelte';

	import { Flex, Heading, Loader, MetaSeo, Text } from '$lib/components';
	import { T, t } from '$lib/translations/config';

	const { data } = $props();
	let loading = $state(true);
	let success = $state(false);
	onMount(async () => {
		loading = true;
		const res = await fetch(`/api/newsletter/unsubscribe?i=${data.subId}&m=${data.mac}`, {
			method: 'GET'
		});
		success = res.ok;
		loading = false;
	});
</script>

<MetaSeo title={$t('unsubscribe.pageTitle')} robots="noindex, follow" />

<Flex flex="1" direction="col" gap={{ mobile: 'm', tablet: 'l', desktop: 'l' }}>
	<Heading size={{ mobile: '3', tablet: '2', desktop: '2' }}>
		<T key="unsubscribe.title"></T>
	</Heading>
	{#if loading}
		<Loader />
	{:else if success}
		<Text>
			<T key="unsubscribe.success"></T>
		</Text>
	{:else}
		<Text>
			<T key="unsubscribe.fail"></T>
		</Text>
	{/if}
</Flex>
