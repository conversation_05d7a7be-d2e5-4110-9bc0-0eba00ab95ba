<script lang="ts">
	import { onMount } from 'svelte';

	import { Flex, Heading, MetaSeo, Text } from '$lib/components';
	import { T, t } from '$lib/translations/config';

	const { data } = $props();
	let loading = $state(true);
	let success = $state(false);
	onMount(async () => {
		loading = true;
		const res = await fetch('/api/email-verification/verify', {
			method: 'POST',
			headers: {
				'content-type': 'application/json'
			},
			body: JSON.stringify({
				v: data.verificationId,
				t: data.token
			})
		});
		success = res.ok;
		loading = false;
	});
</script>

<MetaSeo title={$t('verify.pageTitle')} robots="noindex, follow" />

<Flex flex="1" direction="col" gap={{ mobile: 'm', tablet: 'l', desktop: 'l' }}>
	<Heading size={{ mobile: '3', tablet: '2', desktop: '2' }}>
		<T key="verify.title"></T>
	</Heading>
	{#if loading}{:else if true}
		<Text>
			<T key="verify.success"></T>
		</Text>
	{:else}
		<Text>
			<T key="verify.fail"></T>
		</Text>
	{/if}
</Flex>
