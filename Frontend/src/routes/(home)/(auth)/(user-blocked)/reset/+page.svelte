<script lang="ts">
	import { KeyRound, RectangleEllipsis } from '@lucide/svelte';
	import { defaults, type Infer, superForm } from 'sveltekit-superforms';
	import { zod4 } from 'sveltekit-superforms/adapters';

	import { apiService, applyBackendToForm } from '$lib/api';
	import { Button, Flex, Heading, Input, MetaSeo, Spacer, Text } from '$lib/components';
	import { ResetPasswordSchema } from '$lib/schemas';
	import { T, t } from '$lib/translations/config';

	const { data }: { data: { verificationId: string; token: string } } = $props();
	let sent = $state(false);
	const formData = defaults(zod4(ResetPasswordSchema));
	const { form, errors, enhance, message, submitting } = superForm<
		Infer<typeof ResetPasswordSchema>,
		string
	>(formData, {
		validators: zod4(ResetPasswordSchema),
		SPA: true,
		taintedMessage: null,
		resetForm: false,
		clearOnSubmit: 'errors-and-message',
		onUpdate: async ({ form }) => {
			if (!form.valid) return;

			//@ts-expect-error typing incosistent TODO: fix
			const res = await apiService.domains.auth.reset({
				...form.data,
				v: data.verificationId,
				rpt: data.token
			});

			applyBackendToForm(form, res);

			if (form.valid) {
				sent = true;
			}
		}
	});
</script>

<MetaSeo title={$t('reset.pageTitle')} robots="noindex, follow" />

<Flex flex="1" direction="col" gap={{ mobile: 'm', tablet: 'l', desktop: 'l' }}>
	<Heading size={{ mobile: '3', tablet: '2', desktop: '2' }}>
		<T key="reset.title" />
	</Heading>

	{#if sent}
		<Flex flex="1" direction="col" justify="between">
			<T key="reset.success" />

			<Button as="a" href="/prihlaseni">
				<T key="reset.cta" />
			</Button>
		</Flex>
	{:else}
		<form method="POST" use:enhance autocomplete="on">
			<Flex direction="col" gap="l">
				<Flex direction="col" gap="s">
					<Flex direction="col" gap="s">
						<Input
							id="newPassword"
							type="password"
							placeholder={$t('reset.form.newPassword.label')}
							autocomplete="current-password"
							Icon={KeyRound}
							bind:value={$form.newPassword}
							errors={$errors.newPassword}
						/>

						<Input
							id="newPasswordConfirm"
							type="password"
							placeholder={$t('reset.form.newPasswordConfirm.label')}
							autocomplete="current-password"
							Icon={RectangleEllipsis}
							bind:value={$form.newPasswordConfirm}
							errors={$errors.newPasswordConfirm}
						/>
					</Flex>
				</Flex>

				{#if $message}
					<Text role="alert" style="color: crimson;">
						<T key={$message}></T>
					</Text>
				{/if}

				<Spacer size="s" />
				<Button type="submit" disabled={$submitting}>
					<T key="reset.form.submit" />
				</Button>
			</Flex>
		</form>
	{/if}
</Flex>

<style lang="scss">
	form {
		width: 100%;
		height: 100%;
	}
</style>
