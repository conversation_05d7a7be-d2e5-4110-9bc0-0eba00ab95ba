<script lang="ts">
	import {
		GraduationCap,
		KeyRound,
		Mail,
		RectangleEllipsis,
		School,
		SquareUserRound
	} from '@lucide/svelte';
	import { defaults, type Infer, superForm } from 'sveltekit-superforms';
	import { zod4 } from 'sveltekit-superforms/adapters';

	import { goto, invalidate } from '$app/navigation';
	import { apiService, applyBackendToForm } from '$lib/api';
	import {
		Button,
		Checkbox,
		Flex,
		Heading,
		Input,
		MetaSeo,
		Select,
		Spacer,
		Text
	} from '$lib/components';
	import { RegisterSchema } from '$lib/schemas';
	import { T, t } from '$lib/translations/config';

	const data = defaults(zod4(RegisterSchema));
	const { form, errors, enhance, message } = superForm<Infer<typeof RegisterSchema>, string>(
		data,
		{
			validators: zod4(RegisterSchema),
			SPA: true,
			taintedMessage: null,
			resetForm: false,
			clearOnSubmit: 'errors-and-message',
			onUpdate: async ({ form }) => {
				if (!form.valid) return;

				const res = await apiService.domains.auth.register(form.data);

				if (!res.ok) {
					form.valid = false;
					form.message = 'global.errors.http.internal';
				}

				applyBackendToForm(form, res);

				if (form.valid) {
					await invalidate('app:user');
					await goto('/panel');
				}
			}
		}
	);
</script>

<MetaSeo
	title={$t('register.pageTitle')}
	description={$t('register.pageDescription')}
	canonical="https://www.dej-prijimacky.cz/registrace"
	robots="index, follow"
/>

<Flex flex="1" direction="col" gap={{ mobile: 'm', tablet: 'l', desktop: 'l' }}>
	<Heading size={{ mobile: '3', tablet: '2', desktop: '2' }}>
		<T key="register.title"></T>
	</Heading>
	<form method="POST" use:enhance autocomplete="on">
		<Flex direction="col">
			<Flex direction="col" gap="sm">
				<Flex gap="s" flex="1" direction={{ mobile: 'col', tablet: 'row', desktop: 'row' }}>
					<Input
						fullWidth="true"
						id="firstName"
						type="text"
						placeholder={$t('register.form.firstName.label')}
						autocomplete="given-name"
						Icon={SquareUserRound}
						errors={$errors.firstName}
						bind:value={$form.firstName}
						required
					/>

					<Input
						fullWidth="true"
						id="lastName"
						type="text"
						placeholder={$t('register.form.lastName.label')}
						autocomplete="family-name"
						Icon={SquareUserRound}
						errors={$errors.lastName}
						bind:value={$form.lastName}
						required
					/>
				</Flex>

				<Input
					id="email"
					type="email"
					placeholder={$t('register.form.email.label')}
					autocomplete="email"
					Icon={Mail}
					errors={$errors.email}
					bind:value={$form.email}
					required
				/>

				<Flex direction="col" gap="s">
					<Input
						id="emailStudent"
						type="email"
						placeholder={$t('register.form.emailStudent.label')}
						Icon={Mail}
						errors={$errors.emailStudent}
						bind:value={$form.emailStudent}
						required
					/>

					<Flex as="span" inline="true" gap="xs">
						<Text size="small" color="secondary">*</Text>
						<Text size="small" color="secondary">
							<T key="register.form.email.tooltip"></T>
						</Text>
					</Flex>
				</Flex>
				<Input
					id="originCity"
					type="text"
					placeholder={$t('register.form.originCity.label')}
					Icon={School}
					errors={$errors.originCity}
					bind:value={$form.originCity}
				/>

				<Select
					Icon={GraduationCap}
					options={[
						{ value: '', label: $t('register.form.schoolType.placeholder') },
						{ value: 'ss', label: $t('register.form.schoolType.options.ss') },
						{ value: 'og', label: $t('register.form.schoolType.options.og') },
						{ value: 'other', label: $t('register.form.schoolType.options.other') }
					]}
					id="schoolType"
					errors={$errors.schoolType}
					bind:value={$form.schoolType}
				/>

				<Input
					id="password"
					type="password"
					placeholder={$t('register.form.password.label')}
					autocomplete="new-password"
					Icon={KeyRound}
					errors={$errors.password}
					bind:value={$form.password}
					required
				/>

				<Input
					id="passwordConfirm"
					type="password"
					placeholder={$t('register.form.passwordConfirm.label')}
					autocomplete="new-password"
					Icon={RectangleEllipsis}
					errors={$errors.passwordConfirm}
					bind:value={$form.passwordConfirm}
					required
				/>

				<div>
					<Spacer size="m" />
					<Checkbox
						name="consent"
						id="consent"
						errors={$errors.consent}
						bind:checked={$form.consent}
					>
						<Text as="span">
							<T renderAsHtml={true} key="register.form.consent.label" />
						</Text>
					</Checkbox>
				</div>
				<!--
                 <Checkbox name="subscribe" id="subscribe">
                    <Text as="span">Přihlásit se k odběru novinek.</Text>
                </Checkbox>
                 -->
			</Flex>
			<Spacer size="l" />

			<Flex flex="1" direction="col">
				{#if $message}
					<Text role="alert" style="color: crimson;">
						<T key={$message}></T>
						<Spacer direction="vertical" size="m" />
					</Text>
				{/if}

				<Button type="submit">
					<T renderAsHtml={true} key="register.form.submit" />
				</Button>
				<Spacer direction="vertical" size="l" />

				<Text align="center">
					<T renderAsHtml={true} key="register.alreadyRegistered" />
				</Text>
			</Flex>
		</Flex>
	</form>
</Flex>
