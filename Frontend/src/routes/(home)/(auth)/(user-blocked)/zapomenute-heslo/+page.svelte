<script lang="ts">
	import { Mail } from '@lucide/svelte';
	import { defaults, type Infer, superForm } from 'sveltekit-superforms';
	import { zod4 } from 'sveltekit-superforms/adapters';

	import { apiService, applyBackendToForm } from '$lib/api';
	import { Button, Flex, Heading, Input, MetaSeo, Text } from '$lib/components';
	import { ForgotPasswordSchema } from '$lib/schemas';
	import { T, t } from '$lib/translations/config';

	let sent: boolean = $state(false);

	const data = defaults(zod4(ForgotPasswordSchema));
	const { form, errors, enhance, message, submitting } = superForm<
		Infer<typeof ForgotPasswordSchema>,
		string
	>(data, {
		validators: zod4(ForgotPasswordSchema),
		SPA: true,
		taintedMessage: null,
		resetForm: false,
		clearOnSubmit: 'errors-and-message',
		onUpdate: async ({ form }) => {
			if (!form.valid) return;

			const res = await apiService.domains.auth.forgotPassword(form.data);

			applyBackendToForm(form, res);

			if (form.valid) {
				sent = true;
			}
		}
	});
</script>

<MetaSeo title={$t('forgotPassword.pageTitle')} robots="noindex, follow" />

<Flex flex="1" direction="col" gap={{ mobile: 'm', tablet: 'l', desktop: 'l' }}>
	<Heading size={{ mobile: '3', tablet: '2', desktop: '2' }}>
		<T key="forgotPassword.title"></T>
	</Heading>
	{#if sent}
		<Flex flex="1" direction="col" gap="xxl">
			<Text>
				<T key="forgotPassword.form.success.text" />
			</Text>
			<Button as="a" href="/prihlaseni">
				<T key="forgotPassword.form.success.cta" />
			</Button>
		</Flex>
	{:else}
		<form method="POST" use:enhance autocomplete="on">
			<Flex direction="col" gap="xxl">
				<Flex direction="col" gap="s">
					<Flex direction="col" gap="m">
						<Input
							id="email"
							type="email"
							placeholder={$t('forgotPassword.form.email.label')}
							autocomplete="email"
							Icon={Mail}
							bind:value={$form.email}
							errors={$errors.email}
						/>
					</Flex>
				</Flex>

				{#if $message}
					<Text role="alert" style="color: crimson;">
						<T key={$message}></T>
					</Text>
				{/if}

				<Flex flex="1" direction="col" gap="m">
					<Button type="submit" disabled={$submitting}>
						<T key="forgotPassword.form.submit"></T>
					</Button>

					<Text align="center">
						<T key="forgotPassword.backToLogin"></T>
					</Text>
				</Flex>
			</Flex>
		</form>
	{/if}
</Flex>

<style>
	form {
		width: 100%;
		height: 100%;
	}
</style>
