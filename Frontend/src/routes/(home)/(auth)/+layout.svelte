<script lang="ts">
	import logo from '$lib/assets/img/logos/logo.svg';
	import studentsImg from '$lib/assets/img/page-specific/auth/students.webp';
	import { Card, Flex, Grid, Image, Spacer } from '$lib/components';

	let { children } = $props();
</script>

<Flex
	flex="1"
	direction="col"
	align="center"
	justify="center"
	pageContainerOverflow={{ mobile: 'fullscreen', tablet: 'none', desktop: 'none' }}
>
	<a href="/">
		<Image height="48px" src={logo} alt="Dej přijí<PERSON>" />
	</a>

	<Spacer direction="vertical" size="l" />

	<Grid class="auth-wrapper" rows="1">
		<Card
			border="primary"
			borderRadius="l"
			overflow="hidden"
			class="form"
			paddingX={{ mobile: 'l', tablet: 'xl', desktop: 'xxl' }}
			paddingY={{ mobile: 'xxl', tablet: 'xl', desktop: 'xxl' }}
		>
			<Flex flex="1" style="width: 100%; height: 100%;">
				{@render children()}
			</Flex>
		</Card>

		<picture class="picture" style="object-position: right; width: 100%;">
			<source srcset={studentsImg} media="(min-width: 524px)" />
			<img
				src=""
				alt="students"
				style="object-fit: cover; width: 100%; height: 100%; border-radius: var(--border-radius-l); border: 2px solid var(--color-primary);"
			/>
		</picture>
	</Grid>
</Flex>

<style class="scss">
	:global {
		.auth-wrapper {
			grid-template-columns: 3fr minmax(0, 2fr) !important;
			overflow: hidden;
			max-width: 900px;
			min-height: 300px;

			a {
				color: var(--color-primary);

				&:hover {
					text-decoration: underline;
				}
			}
		}

		.form {
			height: 100%;
			width: calc(100% + 40px);
			position: relative;
		}

		@media (max-width: 600px) {
			.auth-wrapper {
				grid-template-columns: 1fr !important;
				width: 100%;
				min-height: unset;
			}

			.picture {
				display: none;
			}

			.form {
				height: 100%;
				width: 100%;
			}
		}
	}
</style>
