import { browser } from '$app/environment';
import { user } from '$lib/stores/user';
import { loadTranslations, locale } from '$lib/translations/config';

import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async (event) => {
	event.depends('app:user');
	const { data } = event;

	if (data.locale) {
		locale.set(data.locale);
		await loadTranslations(data.locale);
	}

	if (browser) {
		let currentUser = null;
		if (data.user) {
			currentUser = data.user;
		} else if (data.refreshPresent) {
			return {
				...data,
				refreshInProgress: true
			};
		}
		user.set(currentUser);
	}

	return {
		...data
	};
};
